// lib/chatbot-ssr-helper.js
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

import { axiosSsr } from 'lib/axios';
import { selectedLanguage } from 'lib/utils';

import {
  BOT_IMAGES,
  BotTypeLabels,
  chatEndpoint,
} from 'modules/chat/diet-planner/data/pre-defined-messages';
import { getInitialBotMessage } from 'modules/chat/helper';

const getRole = (senderId, initiatorId) =>
  senderId === initiatorId ? 'user' : 'assistant';

const formatMessage = (msg, initiatorId) => ({
  role: getRole(msg?.senderId, initiatorId),
  content: msg.content,
  contentType: msg.contentType,
  time: msg.createdAt,
  hasOptions: true,
});

const CREATE_SESSION_ENDPOINT = '/api/chat/chatbot/start-session';
const CREATE_MESSAGE_ENDPOINT = '/api/chat/chatbot/message';

export const createChatbotSSR = (config) => {
  const {
    chatType,
    recipientId = process.env.chatBotId || 15511278513,
    sections = ['nothing'],
    translationNamespaces = ['chatbot', 'common', 'sellPopups'],
    source = 'CHATBOT',
    options = {},
  } = config;

  return async function getServerSideProps(context) {
    const { cookies } = context.req;
    const { query } = context;
    const { accessToken, lang } = cookies;
    const { question } = query;

    // Auth check
    if (!accessToken) {
      return {
        redirect: {
          destination: '/login',
          permanent: false,
        },
      };
    }

    const axios = axiosSsr(context);
    let chatSession;

    try {
      // Start chat session
      const response = await axios.post(CREATE_SESSION_ENDPOINT, {
        recipientId,
        type: chatType,
      });

      if (response?.data?.status) {
        chatSession = response?.data?.chatSession;
        const initiatorId = response?.data?.initiator?.id;

        if (!chatSession?.messages?.length) {
          // New session - send initial bot message
          const firstBotMessage = getInitialBotMessage(chatSession.type);

          const apiRes = await axios.post(CREATE_MESSAGE_ENDPOINT, {
            receiverId: chatSession.initiatorId,
            senderId: chatSession.recipientId,
            contentType: 'TEXT',
            content: firstBotMessage.content,
            chatSessionId: chatSession.id,
          });

          chatSession.messages = [
            formatMessage(apiRes?.data?.data, initiatorId),
          ];
        } else {
          // Existing session - format existing messages
          chatSession.messages = chatSession.messages.map((msg) =>
            formatMessage(msg, initiatorId),
          );
        }
      }
    } catch (error) {
      console.error(`Error in ${chatType}:`, error);
      return {
        redirect: {
          destination: `/home?source=${chatEndpoint[chatType]}`,
          permanent: false,
        },
      };
    }

    return {
      props: {
        source,
        use: BotTypeLabels[chatType],
        chatSession: chatSession || null,
        image: BOT_IMAGES[chatType],
        initialQuestion: question || null,
        monetizationProps: {
          stats: null,
          source: chatType,
          sections,
        },
        ...options,
        ...(await serverSideTranslations(
          selectedLanguage(lang),
          translationNamespaces,
        )),
      },
    };
  };
};

// Export the formatMessage function for other uses
export { formatMessage };
