// lib/doctor-call.js
import axios from 'lib/axios';
import { logAmplitudeEvent } from 'lib/log-event';
import { getCookie } from 'lib/storage';

/** ✅ doctor directory: phone ➜ name */
export const DEFAULT_DOCTORS = {
  9910337630: 'Manish Ji',
  9910543026: 'Deepak Ji',
};

const normalizePhone = (n) => {
  const digits = (n || '').replace(/[^\d]/g, '');
  if (!digits) return null;
  const last10 = digits.slice(-10);
  return `+91${last10}`;
};

const canonKey = (phone) => {
  const digits = (phone || '').replace(/[^\d]/g, '');
  return digits.slice(-10); // 10-digit key to index DEFAULT_DOCTORS
};

function pickDoctor({ doctors = DEFAULT_DOCTORS, doctorPhone = '' }) {
  const map =
    doctors && Object.keys(doctors).length ? doctors : DEFAULT_DOCTORS;

  // if a specific phone is provided, prefer that
  let chosen<PERSON><PERSON> = doctorPhone ? canonKey(doctorPhone) : null;

  if (!chosenKey) {
    const keys = Object.keys(map);
    chosenKey = keys[Math.floor(Math.random() * keys.length)] || null;
  }

  if (!chosenKey) return { dial: null, name: 'Unknown' };

  const dial = normalizePhone(chosenKey); // +91XXXXXXXXXX
  const name = map[chosenKey] || 'Unknown';
  return { dial, name };
}

export function trackDoctorCallCategories({ source, PHONE, DOCTOR, page }) {
  try {
    logAmplitudeEvent('CALL', 'DOCTOR', 'CATEGORIES', {
      SOURCE: source || 'NA',
      PHONE: PHONE || 'N/A',
      DOCTOR: DOCTOR || 'Unknown',
    });
  } catch (e) {
    console.warn('[trackDoctorCallCategories] failed', e);
  }
}

export async function doctorCall({
  doctors = DEFAULT_DOCTORS,
  doctorPhone = '',
  source = 'vet_intake_doctor',
  answers = { problem_category: 'GENERIC', question: 'NA' },
}) {
  const { dial, name } = pickDoctor({ doctors, doctorPhone });

  const payload = {
    userId: getCookie('animallUserId') || 'Unknown',
    timestamp: new Date().toISOString(),
    source,
    name: getCookie('userName') || 'Unknown',
    phone: getCookie('userPhone') || null,
    locationName: getCookie('locationName') || 'Unknown',
  };

  if (answers && Object.keys(answers).length) {
    payload.answers = answers;
  }
  payload.answers = answers;

  let posted = false;
  try {
    await axios.post('/api/vet-intake/submit', payload);
    posted = true;
  } catch (e) {
    console.error('vet-intake submit failed:', e);
  } finally {
    if (dial) {
      window.location.href = `tel:${dial.replace(/[^\d+]/g, '')}`;
    } else {
      alert('Doctor number not configured.');
    }
  }

  return { PHONE: dial, DOCTOR: name, posted };
}
