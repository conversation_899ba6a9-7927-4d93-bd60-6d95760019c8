import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

import { selectedLanguage } from 'lib/utils';

import ChakraProvider from 'components/ChakraProvider';

import ChatHeaderV2 from 'modules/chat/component/ChatHeaderV2';
import VetIntakeChat from 'modules/chat/vet-intake/VetIntakeChat';

function VetIntakePage(props) {
  return (
    <>
      <ChakraProvider>
        <title>डॉक्टर दीपिका जी</title>
        <div className="border-t border-primary-600">
          <ChatHeaderV2
            isTyping={false}
            use="Dr. दीपिका जी "
            chatSession={{}}
            setChatSession={() => {}}
            setMessages={() => {}}
            image="https://static-assets.animall.in/static/images/ai-partners/ai-doctor.jpg"
            shouldSaveTime={false}
            isEligible={true}
          />
          <VetIntakeChat userId={props.userId} />
        </div>
      </ChakraProvider>
    </>
  );
}

export default VetIntakePage;

export async function getServerSideProps(context) {
  const { cookies } = context.req;
  const { accessToken, animallUserId } = cookies;

  if (!accessToken) {
    return {
      redirect: {
        destination: '/login?redirect=/chat/vet-intake',
        permanent: false,
      },
    };
  }

  const { lang } = cookies;

  try {
    const userId = animallUserId;

    if (!userId) {
      return {
        redirect: {
          destination: '/login?redirect=/chat/vet-intake',
          permanent: false,
        },
      };
    }

    return {
      props: {
        userId: userId,
        source: 'VET_INTAKE',
        ...(await serverSideTranslations(selectedLanguage(lang), [
          'common',
          'chatbot',
        ])),
      },
    };
  } catch (error) {
    console.log('error in Vet Intake page', error?.response?.data);
    return {
      redirect: {
        destination: '/home?error=vet_intake_access',
        permanent: false,
      },
    };
  }
}
