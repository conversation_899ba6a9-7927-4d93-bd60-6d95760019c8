import { createChatbotSSR } from 'lib/getChatbotSSRProps';

import ChakraProvider from 'components/ChakraProvider';

import { chatType } from 'modules/chat/diet-planner/data/pre-defined-messages';
import PashuPartnerModule from 'modules/chat/pashu-partner';

function ChatbotPage(props) {
  return (
    <>
      <ChakraProvider>
        <title>Chatbot</title>
        <PashuPartnerModule {...props} />
      </ChakraProvider>
    </>
  );
}

export default ChatbotPage;

export const getServerSideProps = createChatbotSSR({
  chatType: chatType.BOT_GENERAL_ROBOT,
  source: 'ROBOT_GENERAL',
  sections: ['app'],
});
