import React, { useEffect } from 'react';

import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';

import { logAmplitudeEvent } from 'lib/log-event';
import { getCookie } from 'lib/storage';
import { isInBuckets, selectedLanguage } from 'lib/utils';

import BottomBar from 'components/BottomBar';
import { AnimallRobotIcon } from 'components/BottomBar/PashuRobotIcon';
import CallIcon from 'components/icons/CallIcon';
import {
  H4,
  PrimaryBody,
  SecondaryBody,
  TertiaryBody,
} from 'components/ui/typography';

import { ROBOTS } from 'data/robots';
import useAppPaywall from 'modules/app-subscription/hooks/useAppPaywall';
import { ArrowIcon } from 'modules/home/<USER>/icons/robots';
import { useGrowthUser } from 'providers/GrowthUserProvider';
import { useShowPaywall } from 'providers/PaywallProvider';

const RobotsPage = () => {
  const router = useRouter();
  const { isPremiumUser } = useAppPaywall();
  const { showPaywall } = useShowPaywall();
  const { isGrowthUser, isLoaded } = useGrowthUser();
  const oldX8User =
    isLoaded && !isGrowthUser && isInBuckets([80, 89], getCookie('bucketId'));

  // Filter out generic robot from display
  const displayRobots = ROBOTS.filter(
    (robot) => robot.link !== '/chat/generic-robot',
  );

  useEffect(() => {
    logAmplitudeEvent('LANDED', 'MAIN', 'ROBOTS', {
      SOURCE: router.query?.source || 'NA',
    });
  }, [router.query?.source]);

  const handleRobotClick = (robot) => {
    if (isPremiumUser || oldX8User) {
      const urlWithSource = `${robot.link}?source=RobotsPage`;
      router.push(urlWithSource);
    } else {
      showPaywall({ source: 'ROBOTS_PAGE' });
    }
  };

  const handleVetChatClick = () => {
    if (!isPremiumUser) {
      showPaywall({ source: 'ROBOTS_PAGE' });
    } else {
      router.push('/chat/vet-intake');
    }
  };

  return (
    <div className="min-h-screen bg-surface-3 pb-20">
      {/* Header */}
      <div className="w-full flex justify-center bg-white">
        <div className="flex items-center justify-between w-full h-14 px-4 py-1.5">
          <div className="flex items-center gap-2">
            <AnimallRobotIcon width={28} height={28} />
            <PrimaryBody className="text-text-primary font-semibold text-lg leading-[140%] tracking-[0.01em]">
              पशु रोबोट
            </PrimaryBody>
          </div>
        </div>
      </div>

      {/* Subtitle */}
      <div className="w-full flex justify-center">
        <div className="w-[90vw] max-w-90 mt-2">
          <SecondaryBody className="text-text-primary font-medium text-base leading-[140%] tracking-[0.01em] text-left">
            हर समस्या का हल
          </SecondaryBody>
        </div>
      </div>

      {/* Robot Cards */}
      <div className="w-full flex justify-center mt-2.5">
        <div className="flex flex-col items-center gap-6">
          {displayRobots.map((robot, index) => (
            <div
              key={index}
              onClick={() => handleRobotClick(robot)}
              className="flex items-center justify-between w-[90vw] max-w-[90] h-15 px-4 py-3 border border-gray-300 rounded-xl bg-[radial-gradient(101%_230.88%_at_49.04%_48.5%,_#FFFFFF_0%,_#A8ECE6_100%)] shadow-[0_0_8px_rgba(0,0,0,0.05)] opacity-100 mx-auto cursor-pointer"
            >
              {/* Left - Icon and Title */}
              <div className="flex items-center gap-3">
                <Image
                  src={robot.image}
                  alt={robot.title}
                  width={36}
                  height={36}
                  className="object-contain"
                />
                <div className="flex flex-row items-center gap-1 whitespace-nowrap min-w-0">
                  <H4 className="text-xl leading-[120%] text-primary-500 font-bold whitespace-nowrap">
                    {robot.title}
                  </H4>
                  <H4 className="text-xl leading-[120%] text-primary-500 font-bold whitespace-nowrap">
                    {robot.subtitle}
                  </H4>
                </div>
              </div>

              {/* Right - Arrow */}
              <div className="w-[30px] h-5 bg-primary-600 rounded-full flex items-center justify-center">
                <ArrowIcon size={12} fill="white" />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* CTA Button */}
      <div className="w-full flex justify-center mt-6 ">
        <div className="flex items-center justify-between w-[90vw] max-w-90  h-17 p-4 rounded-xl border-2 border-white bg-[radial-gradient(101%_230.88%_at_49.04%_48.5%,_#FFFFFF_0%,_#A3BEE2_100%)] shadow-[0_0_8px_rgba(0,0,0,0.05)] opacity-100 mx-auto">
          <div className="flex items-center gap-2">
            <Image
              src="https://static-assets.animall.in/static/images/robots/doctor.png"
              alt="Doctor"
              width={36}
              height={36}
            />
            <H4 className="text-text-primary text-xl font-bold">
              सीधा डॉक्टर से बात करें
            </H4>
          </div>

          <button
            className="w-21 h-9 px-3 py-2 rounded-lg bg-[linear-gradient(180deg,_#2F80ED_0%,_#0385DC_100%)] shadow-[0_0_10px_#0000000D] opacity-100"
            onClick={handleVetChatClick}
          >
            <div className="flex items-center gap-1">
              <CallIcon w={16} h={16} fill="white" />
              <TertiaryBody className="text-sm leading-[140%] tracking-[0.01em] font-medium font-mukta text-surface-3">
                बात करें
              </TertiaryBody>
            </div>
          </button>
        </div>
      </div>

      {/* Bottom Navigation */}
      <BottomBar />
    </div>
  );
};

export default RobotsPage;

export async function getServerSideProps(context) {
  const { cookies } = context.req;
  const { accessToken, animallUserId, lang } = cookies;

  if (!accessToken || !animallUserId) {
    return {
      redirect: {
        destination: '/login?redirect=/robots',
        permanent: false,
      },
    };
  }

  try {
    return {
      props: {
        userId: animallUserId,
        source: 'ROBOTS_PAGE',
        monetizationProps: {
          stats: null,
          source: 'ROBOTS_PAGE',
          sections: ['app', 'listing'],
          includePlans: true,
        },
        ...(await serverSideTranslations(selectedLanguage(lang), [
          'common',
          'app_subscription',
          'trialPage',
        ])),
      },
    };
  } catch (error) {
    console.log('error in Robots page:', error?.response?.data);
    return {
      redirect: {
        destination: '/home?error=robot_access',
        permanent: false,
      },
    };
  }
}
