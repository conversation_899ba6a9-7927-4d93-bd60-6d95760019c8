import { useEffect, useState } from 'react';

import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import Head from 'next/head';

import { axiosSsr } from 'lib/axios';
import { initFacebookPixel } from 'lib/init-fbpixel';
import { isInBuckets, selectedLanguage } from 'lib/utils';

import MainLayout from 'components/MainLayout';

import withMonetization from 'hoc/withMonetization';
import Buy from 'modules/buy';
import { GrowthBuckets } from 'modules/plans/constants';
import WalletProvider from 'providers/Wallet';

const BuyPage = ({ ...props }) => {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    initFacebookPixel();
  }, []);
  return (
    <WalletProvider>
      <Head>
        <title>Buy | Animall - Bharat ka animal mela</title>
      </Head>
      <MainLayout {...props?.headerData} isScrolled={isScrolled}>
        <Buy {...props} isScrolled={isScrolled} setIsScrolled={setIsScrolled} />
      </MainLayout>
    </WalletProvider>
  );
};

async function _getServerSideProps(context) {
  const { query, req } = context;
  const { cookies } = req;
  const { lang, bucketId, accessToken } = cookies;
  const axios = axiosSsr(context);

  const isInGrowthBucket = isInBuckets(GrowthBuckets, parseInt(bucketId));

  const promises = [];

  // Only fetch user data if accessToken exists
  if (accessToken) {
    promises.push(
      axios
        .get('/api/user/me')
        .then((res) => res.data)
        .catch((error) => {
          console.log(
            'error getting userData from buy page:',
            error?.response?.data,
          );
          return null;
        }),
    );
  } else {
    promises.push(Promise.resolve(null));
  }

  if (query?.postId) {
    promises.push(
      axios
        .get(`/api/post/${query.postId}`, {
          params: {
            ...(isInGrowthBucket && { extraDetails: true }),
          },
        })
        .then(({ data }) => data?.post),
    );
  }

  const [userData, postInUrl] = await Promise.all(promises);
  let streakData = null;
  if (accessToken) {
    streakData = await axios
      .get('/api/exp/streak', {
        params: { streakType: 'DEFAULT' },
      })
      .then((res) => res.data?.data || null)
      .catch((error) => {
        console.error('Error fetching streak data:', error?.response?.data);
        return null;
      });
  }
  return {
    props: {
      postInUrl: postInUrl || null,
      userData: userData || null,
      streakData,
      headerData: {
        user: userData,
        streakData: streakData || null,
        source: 'BUY_PAGE',
      },
      ...(await serverSideTranslations(selectedLanguage(lang), [
        'common',
        'pashu-farm',
        'home_report',
        'cwa_snooze',
        'cwaBanner',
        'landingPage',
        'pashuExpert',
        'home',
        'buy',
        'app_subscription',
        'trialPage',
      ])),
    },
  };
}

export const getServerSideProps = withMonetization(_getServerSideProps, {
  source: 'BUY_PAGE',
  includePlans: true,
  sections: ['vipBuyer', 'listing', 'app', 'ad', 'buyer', 'prime'],
});

export default BuyPage;
