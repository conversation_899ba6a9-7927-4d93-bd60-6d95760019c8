import { useEffect } from 'react';

import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

import { axiosSsr } from 'lib/axios';
import { getCookie } from 'lib/storage';
import { isGrowthBucketUser, isInBuckets, selectedLanguage } from 'lib/utils';

import ChakraProvider from 'components/ChakraProvider';
import MainLayout from 'components/MainLayout';

import { differenceInHours } from 'date-fns';
import MetaTags from 'modules/home/<USER>/MetaTags';
import { MIN_COUNT } from 'modules/home/<USER>';
import NewHomePage from 'modules/home/<USER>';
import {
  addRandomVarianceToNumber,
  isNewlyJoinedUser,
} from 'modules/home/<USER>';
import { getSimulatedStat } from 'modules/home/<USER>/simulatedStats';
import { GrowthBuckets } from 'modules/plans/constants';
import { VIP_BASE_PATH } from 'modules/vip/constant';

const HomePage = (props) => {
  const { monetizationStats, ssrHadAccessToken, bucketId } = props;

  useEffect(() => {
    // Check if we need to reload due to Android webview cookie timing issues
    const clientAccessToken = getCookie('accessToken');
    const reloadCount = parseInt(
      sessionStorage.getItem('homeReloadCount') || '0',
    );

    // If client has accessToken but SSR didn't see it, and we haven't exceeded max reloads
    if (clientAccessToken && !ssrHadAccessToken && reloadCount < 2) {
      sessionStorage.setItem('homeReloadCount', (reloadCount + 1).toString());
      window.location.reload();
      return;
    }

    // Reset reload count on successful load
    if (ssrHadAccessToken && reloadCount > 0) {
      sessionStorage.removeItem('homeReloadCount');
    }
  }, [ssrHadAccessToken]);

  return (
    <>
      <MetaTags url={props?.url} />
      <ChakraProvider>
        <MainLayout {...props?.headerData}>
          <NewHomePage {...props} />
        </MainLayout>
      </ChakraProvider>
    </>
  );
};

export async function getServerSideProps(context) {
  const { req, query, res } = context;
  const { cookies } = req;
  const {
    lang,
    accessToken,
    latitude,
    longitude,
    animallUserId,
    district,
    buyersCount,
    newPostsCount,
    bucketId,
    userJoinedDate,
    growthNuRedirected,
    hasFreeListingInThisWeek,
    landOnBuyPage,
  } = cookies;

  // Helper function to create redirect with preserved query params
  const createRedirectWithParams = (destination, source) => {
    const queryParams = new URLSearchParams(query);
    queryParams.set('source', source);

    return {
      redirect: {
        destination: `${destination}?${queryParams.toString()}`,
        permanent: false,
      },
    };
  };

  const { utm_term, ...restQuery } = query;

  // Handle UTM-based deep linking to route users directly to sell/buy pages based on campaign parameters
  if (utm_term) {
    let basePath = '';
    let source = '';

    if (utm_term === 'sellPage') {
      basePath = '/sell';
      source = 'sellPage_deeplink';
    } else if (utm_term === 'buyPage') {
      basePath = '/buy';
      source = 'buyPage_deeplink';
    }

    if (basePath) {
      return createRedirectWithParams(basePath, source);
    }
  }

  const axios = axiosSsr(context);

  const promises = [];

  if (
    accessToken &&
    !hasFreeListingInThisWeek &&
    isNewlyJoinedUser(userJoinedDate) &&
    isInBuckets([0, 9], parseInt(bucketId))
  ) {
    const currDate = new Date();
    const diffHours = differenceInHours(currDate, new Date(userJoinedDate));
    if (diffHours <= 168) {
      const _myPostsCount = await axios
        .get('/api/post/mypost', {
          params: {
            limit: 4,
            sortCriteria: JSON.stringify({ publishedOn: -1 }),
            state:
              'PENDING,AUTO_PENDING,ACTIVE,REJECTED,EXPIRED,QUEUED,SOLD,STA,STOP',
          },
        })
        .then(({ data }) => {
          return (data?.posts || []).length;
        })
        .catch((e) => {
          console.error(
            `Error fetching my posts for new user :: ${JSON.stringify(e)}`,
          );
          return 0;
        });

      if (_myPostsCount === 0) {
        res.setHeader(
          'Set-Cookie',
          'hasFreeListingInThisWeek=true; Path=/; Max-Age=86400',
        );

        return createRedirectWithParams('/sell', 'new_user_control');
      }
    }
  }

  const isGrowthUser = await isGrowthBucketUser(axios, context);

  if (accessToken && !hasFreeListingInThisWeek && !isGrowthUser) {
    const _hasFreeListingInThisWeek = await axios
      .get('/api/monetization/subscription/listing/last-free-subscription')
      .then(({ data }) => {
        if (!data) {
          return false;
        }

        const currDate = new Date();
        const sevenDaysFromNow = new Date();
        const sevenDaysAgoFromNow = new Date();
        const endDate = new Date(data.endDate);
        const startDate = new Date(data.startDate);

        sevenDaysFromNow.setDate(currDate.getDate() + 7);
        sevenDaysAgoFromNow.setDate(currDate.getDate() - 7);

        // user has free listing - Old free listing subscription expired in last 1 week
        // or user has free listing - New free listing subscription started in last 1 week
        if (
          (data?.hasExpired &&
            endDate >= sevenDaysAgoFromNow &&
            endDate <= sevenDaysFromNow) ||
          (!data?.hasExpired &&
            startDate <= sevenDaysFromNow &&
            endDate >= currDate)
        ) {
          return true;
        }

        return false;
      })
      .catch((error) => {
        console.log('[lastFreeSubscription] Error:', error?.response?.data);
        return false;
      });

    if (_hasFreeListingInThisWeek) {
      // once per day
      res.setHeader(
        'Set-Cookie',
        'hasFreeListingInThisWeek=true; Path=/; Max-Age=86400',
      );

      return createRedirectWithParams('/sell', 'free_lising_week');
    }
  }

  if (accessToken && !landOnBuyPage && !isGrowthUser) {
    const landOnBuyPageForFirstSession = await axios
      .get('/api/user/home-stats')
      .then(({ data }) => {
        if (!data) {
          return false;
        }
        if (data?.cwa >= 1 && data?.listings === 0) {
          return true;
        }

        return false;
      })
      .catch((error) => {
        console.log('[landOnBuyPage] Error:', error?.response?.data);
        return false;
      });

    // setting cookie for all users to reduce the number of API calls
    res.setHeader(
      'Set-Cookie',
      `landOnBuyPage=${landOnBuyPageForFirstSession}; Path=/; Max-Age=86400`,
    );
    if (landOnBuyPageForFirstSession) {
      return createRedirectWithParams('/buy', 'force_land_on_buy_page');
    }
  }

  if (accessToken) {
    promises.push(
      axios
        .get('/api/user/me')
        .then((res) => res.data)
        .catch((error) => {
          console.log(
            'Error getting userData from home page:',
            error?.response?.data,
          );
          return null;
        }),
    );
  } else {
    promises.push(Promise.resolve(null));
  }

  if (latitude && longitude) {
    promises.push(
      !!buyersCount
        ? Promise.resolve(buyersCount)
        : Promise.resolve(Math.max(getSimulatedStat('online'), MIN_COUNT)),
    );

    promises.push(
      !!newPostsCount
        ? Promise.resolve(newPostsCount)
        : Promise.resolve(Math.max(getSimulatedStat('published'), MIN_COUNT)),
    );
  } else {
    promises.push(Promise.resolve(MIN_COUNT), Promise.resolve(MIN_COUNT));
  }

  if (animallUserId) {
    promises.push(
      axios
        .get(`/api/user/${animallUserId}/profile-stats`)
        .then((res) => res.data)
        .catch((e) => {
          console.error(`Error fetching user stats :: ${JSON.stringify(e)}`);
          return {};
        }),
    );
  } else {
    promises.push(Promise.resolve({}));
  }

  if (accessToken && district) {
    promises.push(
      axios
        .get('/api/monetization/subscription/recent-txn')
        .then((res) => res.data)
        .catch((e) => {
          console.error(
            `Error fetching district transactions :: ${JSON.stringify(e)}`,
          );
          return {};
        }),
    );
  } else {
    promises.push(Promise.resolve({}));
  }

  const isInGrowthBucket = isInBuckets(GrowthBuckets, parseInt(bucketId));

  if (accessToken) {
    const isEligibleForNewExpiredStateChanges = !isGrowthUser;
    promises.push(
      axios
        .get('/api/monetization/subscription/stats', {
          params: {
            includePlans: true,
            source: 'HOME',
            includeTotalSubscriptionStats: true,
            sections: ['listing', 'prime', 'cdf', 'ad', 'app', 'vipBuyer'],
          },
        })
        .then((res) => res.data?.stats)
        .catch((e) => {
          console.error(
            `Error fetching monetization stats :: ${JSON.stringify(e)}`,
          );
          return { app: {}, listing: {}, vipBuyer: null };
        }),
      axios
        .get('/api/post/mypost', {
          params: {
            limit: 4,
            sortCriteria: JSON.stringify({ publishedOn: -1 }),
            ...(isEligibleForNewExpiredStateChanges && {
              hasInterestedBuyers: true,
            }),
            state:
              'PENDING,AUTO_PENDING,ACTIVE,PSEUDO_ACTIVE,DRAFTED,REJECTED,EXPIRED,QUEUED',
            growth_stats: isInGrowthBucket,
            includeHomepageStats: false,
          },
        })
        .then((res) => res.data.posts)
        .catch((e) => {
          console.error(`Error fetching my posts :: ${JSON.stringify(e)}`);
          return [];
        }),

      axios
        .get('/api/monetization/subscription/listing/recent-order')
        .then((res) => res.data)
        .catch((e) => {
          console.error(
            `Error fetching recent listing order :: ${JSON.stringify(e)}`,
          );
          return [];
        }),
    );
  } else {
    promises.push(
      Promise.resolve(null),
      Promise.resolve([]),
      Promise.resolve([]),
    );
  }

  const isEligibleForPrimePopup = (accessToken && !isGrowthUser) || false;
  if (isEligibleForPrimePopup) {
    promises.push(
      axios
        .get('/api/post/mypost', {
          params: {
            limit: 10,
            state: 'ACTIVE',
            sortCriteria: JSON.stringify({ publishedOn: -1 }),
          },
        })
        .then((res) => res.data.posts)
        .catch((e) => {
          console.error(`Error fetching active posts :: ${JSON.stringify(e)}`);
          return [];
        }),
    );
  } else {
    promises.push(Promise.resolve(null));
  }

  if (accessToken) {
    promises.push(
      axios
        .get('/api/exp/streak', {
          params: { streakType: 'DEFAULT' },
        })
        .then((res) => res.data?.data || null)
        .catch((error) => {
          console.error('Error fetching streak data:', error?.response?.data);
          return null;
        }),
    );
  } else {
    promises.push(Promise.resolve(null));
  }
  const isEligibleForInsightsCards = isInBuckets([20, 39], parseInt(bucketId));

  if (accessToken && isEligibleForInsightsCards) {
    promises.push(
      axios
        .get('/api/user/marketing-insights')
        .then((res) => res.data || null)
        .catch((error) => {
          console.error('Error fetching insight data:', error?.response?.data);
          return null;
        }),
    );
  } else {
    promises.push(Promise.resolve(null));
  }

  const [
    user,
    _buyersCount,
    _newPostsCount,
    profileStats,
    districtsRecentTxn,
    monetizationStats,
    myPosts,
    recentlyPlaceListingOrder,
    activePosts,
    streakData,
    insightsData,
  ] = await Promise.all(promises);

  let farmData = {};
  if (!!monetizationStats?.cdf?.userFarm) {
    const daysSinceFarmPublishing = Math.floor(
      (new Date() - new Date(monetizationStats.cdf.userFarm.publishedAt)) /
        (1000 * 60 * 60 * 24),
    );
    farmData = {
      farmName: monetizationStats.cdf.userFarm.farmName,
      slug: monetizationStats.cdf.userFarm.slug,
      resource:
        monetizationStats.cdf.userFarm.resources?.find((r) => !r.isVideo)
          ?.url || '',
      isNew: daysSinceFarmPublishing <= 7,
      report: null,
    };
  }

  if (accessToken) {
    const report = await axios
      .get(`${VIP_BASE_PATH}/report`)
      .then((res) => res.data)
      .catch((e) => {
        console.error('Error fetching farm report :: ', e);
        return {};
      });
    if (Object.keys(report)?.some((key) => report[key] > 0)) {
      farmData.report = report;
    }
  }

  const host = req.headers?.referer?.includes('https://')
    ? req.headers?.referer?.split('https://')?.[1]
    : req.headers?.referer?.split('http://')?.[1];

  const url = host || 'animall.in' + req?.url;

  return {
    props: {
      _buyersCount,
      _newPostsCount,
      user,
      farmData,
      myPosts,
      activePosts,
      isEligibleForPrimePopup,
      url: url,
      districtsRecentTxn,
      profileStats,
      monetizationStats,
      insightsData,
      monetizationProps: {
        // This object is passed to the `MonetizationProvider` in `_app.js`
        stats: monetizationStats,
        source: 'HOME',
      },
      headerData: { user, streakData: streakData || null, source: 'HOME_PAGE' },
      recentlyPlaceListingOrder,
      bucketId,
      streakData,
      ssrHadAccessToken: !!accessToken, // Flag to indicate if SSR had access token
      ...(await serverSideTranslations(selectedLanguage(lang), [
        'common',
        'landingPage',
        'home',
        'astro-cattle',
        'classified_plans',
        'growth_plans',
        'app_subscription',
        'trialPage',
        'prime_plan_selection',
        'profilebase',
        'milkingMusic',
      ])),
    },
  };
}

export default HomePage;
