import { useEffect } from 'react';

import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

import useScrollBlock from 'hooks/useScrollBlock';

import { axiosSsr, internalApi } from 'lib/axios';
import { initializeClarity } from 'lib/init-clarity';
import { getCookie } from 'lib/storage';
import { isGrowthBucketUser, isInBuckets } from 'lib/utils';
import { isCwaCoinsDistrictUser, selectedLanguage } from 'lib/utils';

import ChakraProvider from 'components/ChakraProvider';

import {
  getMonetizationStats,
  getWalletData,
} from 'data-fetchers/monetization';
import PlanSelectionWithIntro from 'modules/prime/PlanSelectionWithIntro';
import { isPrimePost } from 'modules/prime/utils';
import WalletProvider from 'providers/Wallet';

const PlanSelectionPage = ({
  post,
  walletData,
  isMicroTxnUser,
  isCwaCoinsExpUser,
  exitPrimeDiscount,
}) => {
  const [_, allowScroll] = useScrollBlock();
  useEffect(() => {
    // Users can land on this page from several popups
    // hence allow scroll on mount as a precautionary measure
    allowScroll();
    // Initialize Clarity on this page - buckets: 60 - 74
    const bucketId = getCookie('bucketId');
    if (bucketId && isInBuckets([60, 74], parseInt(bucketId))) {
      initializeClarity();
    }
  }, [allowScroll]);

  return (
    <ChakraProvider>
      <WalletProvider walletData={walletData}>
        <PlanSelectionWithIntro
          post={post}
          isMicroTxnUser={isMicroTxnUser}
          isCwaCoinsExpUser={isCwaCoinsExpUser}
          exitPrimeDiscount={exitPrimeDiscount}
        />
      </WalletProvider>
    </ChakraProvider>
  );
};

export default PlanSelectionPage;

export const getServerSideProps = async (context) => {
  const axios = axiosSsr(context);
  const { req, query } = context;
  const { lang, accessToken, animallUserId } = req.cookies;
  const { postId, utm_campaign: utmCampaign, plan } = query;

  if (!accessToken) {
    return {
      redirect: {
        destination: '/login?redirect=/prime/landing',
        permanent: false,
        basePath: false,
      },
    };
  }

  let post = null;
  const [userResponse, isGrowthUser] = await Promise.all([
    axios.get('/api/user/me'),
    isGrowthBucketUser(axios, context),
  ]);

  const userData = userResponse?.data;

  try {
    if (postId) {
      post = await internalApi('post', 'post', 'getPostById', [postId]);
      const isCurrentlyPrime = isPrimePost(post);

      if (isCurrentlyPrime) post = null;
    }
  } catch (error) {
    console.error('[PrimePlanSelectionPage] Error in fetching post', error);
  }

  if (!post) {
    return {
      redirect: {
        destination: '/buy?source=PLAN_SELECTION_ALREADY_PRIME',
        permanent: false,
        basePath: false,
      },
    };
  }

  const [stats, walletData] = await Promise.all([
    getMonetizationStats({
      axiosInstance: axios,
      source: 'PLAN_SELECTION',
      includePlans: true,
      sections: ['prime', 'listing', 'app'],
    }),
    getWalletData({
      axiosInstance: axios,
    }),
  ]);

  let campaignDiscountPlan;
  if (utmCampaign) {
    campaignDiscountPlan = await internalApi(
      'monetization',
      'prime.service',
      'getDiscountedPrimePlanBasedOnUtmCampaign',
      [{ userId: Number(animallUserId), postId, utmCampaign }],
    );
  }

  if (campaignDiscountPlan) {
    const plans = stats.prime?.plans || [];
    plans.forEach((plan) => {
      // No other discounts should be applied
      delete plan.discount;
    });
    const { extraInfo } = campaignDiscountPlan.discount;
    delete extraInfo.isExitDiscount;
    campaignDiscountPlan.discount.extraInfo = extraInfo;
    plans[extraInfo.replaceIndex] = campaignDiscountPlan;
    stats.prime.plans = plans;

    return {
      props: {
        post,
        monetizationProps: {
          // This object is passed to the `MonetizationProvider` in `_app.js`
          stats,
          source: 'PLAN_SELECTION',
        },
        exitPrimeDiscount: null,
        walletData,
        isMicroTxnUser: false,
        isCwaCoinsExpUser: isCwaCoinsDistrictUser(userData),
        ...(await serverSideTranslations(selectedLanguage(lang), ['common'])),
      },
    };
  }

  if (plan === 'GUARANTEED_PRIME') {
    const plans = stats.prime?.plans?.map((planEl) => {
      if (
        planEl.name === 'PRIME_BOOSTER_1000' ||
        planEl.name === 'PRIME_BOOSTER_1000_V2'
      ) {
        planEl.discount = {
          name: `${planEl.name}_DISCOUNT_50_PERCENT`,
          price: 500,
        };
      }
      return planEl;
    });

    stats.prime.plans = plans;
  }

  if (
    stats.prime?.plans &&
    stats.prime.plans?.length > 0 &&
    stats.prime?.primePostsInLast90Days === 0 &&
    !isGrowthUser
  ) {
    // add 30% discount to all plans
    stats.prime.plans = stats.prime?.plans?.map((planEl) => {
      planEl.discount = {
        name: `${planEl.name}_DISCOUNT_30_PERCENT`,
        price: Math.round(planEl.price * 0.7),
      };
      return planEl;
    });
  }

  let exitPrimeDiscount = null;
  stats.prime?.plans?.forEach((plan) => {
    if (!plan.discount) return;
    if (plan.discount.extraInfo?.isExitDiscount) {
      exitPrimeDiscount = plan.discount;
      delete plan.discount;
    }
  });

  if (exitPrimeDiscount) {
    const { data: rejectionData } = await axios
      .get('/api/monetization/prime/plan-rejected', {
        params: {
          postId,
          planName: exitPrimeDiscount.name,
        },
      })
      .catch((error) => {
        console.error(
          '[PrimePlanSelectionPage] Error in fetching rejection data',
          error,
        );
        return {};
      });

    const { rejected } = rejectionData;
    if (rejected) {
      exitPrimeDiscount = null;
    }
  }

  return {
    props: {
      post,
      stats,
      exitPrimeDiscount,
      monetizationProps: {
        // This object is passed to the `MonetizationProvider` in `_app.js`
        stats,
        source: 'PLAN_SELECTION',
      },
      walletData,
      isMicroTxnUser: false,
      isCwaCoinsExpUser: isCwaCoinsDistrictUser(userData),
      ...(await serverSideTranslations(selectedLanguage(lang), [
        'common',
        'classified_plans',
        'prime_plan_selection',
      ])),
    },
  };
};
