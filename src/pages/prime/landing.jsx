import { createContext, useEffect, useState } from 'react';

import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

import useScrollBlock from 'hooks/useScrollBlock';

import { axiosSsr, internalApi } from 'lib/axios';
import { isCwaCoinsDistrictUser, selectedLanguage } from 'lib/utils';

import ChakraProvider from 'components/ChakraProvider';

import { getMonetizationStats } from 'data-fetchers/monetization';
import LandingPage from 'modules/prime/LandingPage';
import { isPrimePost } from 'modules/prime/utils';

export const WalletContext = createContext({
  wallet: { balance: 0, showWallet: false },
});

const Landing = ({
  postInUrl,
  walletBalance,
  postsEligibleForPrime,
  isMicroTxnUser,
  isCwaCoinsExpUser,
  exitPrimeDiscount,
}) => {
  const [showWallet, setShowWallet] = useState(
    !(isMicroTxnUser || isCwaCoinsExpUser),
  );

  const [_, allowScroll] = useScrollBlock();

  useEffect(() => {
    // Users can land on this page from several popups
    // hence allow scroll on mount as a precautionary measure
    allowScroll();
    if (isMicroTxnUser || isCwaCoinsExpUser) {
      setShowWallet(false);
      return;
    }

    if (!sessionStorage.getItem('amartZone')) {
      setShowWallet(true);
    }
  }, [isMicroTxnUser, isCwaCoinsExpUser, allowScroll]);

  return (
    <ChakraProvider>
      <WalletContext.Provider
        value={{
          wallet: { balance: walletBalance || 0, showWallet },
        }}
      >
        <LandingPage
          postInUrl={postInUrl}
          postsEligibleForPrime={postsEligibleForPrime}
          isMicroTxnUser={isMicroTxnUser}
          isCwaCoinsExpUser={isCwaCoinsExpUser}
          exitPrimeDiscount={exitPrimeDiscount}
        />
      </WalletContext.Provider>
    </ChakraProvider>
  );
};

export default Landing;

export const getServerSideProps = async (context) => {
  const axios = axiosSsr(context);
  const { req, query } = context;
  const { lang, accessToken } = req.cookies;
  let { postId, uuid } = query;

  let postInUrl = null;

  if (!accessToken) {
    return {
      redirect: {
        destination: '/login?redirect=/next/prime/landing',
        permanent: false,
        baseUrl: false,
      },
    };
  }

  const { data: userData } = await axios.get('/api/user/me');
  const isCwaCoinsExpUser = isCwaCoinsDistrictUser(userData);

  try {
    const identifier = postId || uuid;
    if (identifier) {
      postInUrl = await internalApi('post', 'post', 'getPostByIdentifier', [
        identifier,
      ]);
      if (postInUrl) {
        postId = postInUrl._id;
        uuid = postInUrl.uuid;
      }
      const isAlreadyPrime = isPrimePost(postInUrl);

      if (isAlreadyPrime) postInUrl = null;
    }
  } catch (error) {
    console.error('[PrimeLandingPage] Error in fetching post', error);
  }

  if (postInUrl) {
    return {
      redirect: {
        destination: `/prime/plan-selection?postId=${postId}`,
        permanent: false,
      },
    };
  }

  let postsEligibleForPrime = [];
  let walletBalance = 0;
  let stats = null;

  try {
    const postsEligibleForPrimePromise = axios.get(
      '/api/monetization/prime/post/eligible',
    );
    const walletPromise = axios.get('/api/payment/wallet', {
      params: { type: 'DISCOVERY' },
    });
    const statsPromise = getMonetizationStats({
      axiosInstance: axios,
      source: 'PRIME_LANDING',
      includePlans: true,
      sections: ['prime', 'listing'],
    });

    const [postsEligibleForPrimeRes, walletRes, statsData] = await Promise.all([
      postsEligibleForPrimePromise,
      walletPromise,
      statsPromise,
    ]);

    stats = statsData;
    postsEligibleForPrime = postsEligibleForPrimeRes.data?.posts;
    walletBalance = walletRes.data?.wallet?.tokens || 0;
  } catch (error) {
    console.error(
      '[PrimeLandingPage] Error in fetching prime eligible posts or wallet data',
      error,
    );
  }

  let exitPrimeDiscount = null;
  stats?.prime?.plans?.forEach((plan) => {
    if (!plan.discount) return;
    if (plan.discount.extraInfo?.isExitDiscount) {
      exitPrimeDiscount = plan.discount;
      delete plan.discount;
    }
  });

  return {
    props: {
      postInUrl,
      monetizationProps: {
        // This object is passed to the `MonetizationProvider` in `_app.js`
        stats,
        source: 'PRIME_LANDING',
      },
      isMicroTxnUser: false,
      isCwaCoinsExpUser,
      walletBalance,
      postsEligibleForPrime,
      exitPrimeDiscount,
      ...(await serverSideTranslations(selectedLanguage(lang), [
        'common',
        'prime_plan_selection',
        'classified_plans',
      ])),
    },
  };
};
