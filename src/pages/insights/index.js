import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

import { axiosSsr } from 'lib/axios';
import { selectedLanguage } from 'lib/utils';

import ChakraProvider from 'components/ChakraProvider';

import InsightsModule from 'modules/insights';

function ChatbotPage(props) {
  return (
    <>
      <ChakraProvider>
        <title>Insights</title>
        <InsightsModule {...props} />
      </ChakraProvider>
    </>
  );
}

export default ChatbotPage;

export async function getServerSideProps(context) {
  const { cookies } = context.req;
  const { lang, accessToken } = cookies;
  const promises = [];
  const axios = axiosSsr(context);

  // Only fetch user data if accessToken exists
  if (accessToken) {
    promises.push(
      axios.get('/api/user/marketing-insights').then((res) => res.data || null),
    );
  } else {
    promises.push(Promise.resolve(null));
  }
  const [insightsData] = await Promise.all(promises);

  if (!insightsData) {
    return {
      redirect: {
        destination: '/next/home',
        permanent: false,
        basePath: false,
      },
    };
  }

  return {
    props: {
      source: 'INSIGHTS',
      insightsData,
      monetizationProps: {
        stats: null,
        source: 'INSIGHTS',
        sections: ['app', 'listing'], // No need to fetch sections for this page
        includePlans: true,
      },
      ...(await serverSideTranslations(selectedLanguage(lang), [
        'common',
        'sellPopups',
        'trialPage',
      ])),
    },
  };
}
