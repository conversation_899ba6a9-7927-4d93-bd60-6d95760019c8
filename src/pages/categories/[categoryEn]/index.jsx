// pages/categories/[categoryEn]/index.jsx
import { useRouter } from 'next/router';

import AllCategoryFooter from 'components/categories/AllCategoriesFooter';
import CategoryHeader from 'components/categories/CategoryHeader';
import { SecondaryBody } from 'components/ui/typography';

import categoriesData from 'data/categoriesqn/categoriesqn';

export default function CategoryPage() {
  const router = useRouter();
  const { categoryEn } = router.query;

  if (!router.isReady) return null;

  // ✅ Find by categoryEn, not Hindi category
  const categoryBlock = categoriesData.find((c) => c.categoryEn === categoryEn);

  if (!categoryBlock) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <SecondaryBody className="text-lg text-text-primary">
          Category not found
        </SecondaryBody>
      </div>
    );
  }

  const handleCardClick = (idx) => {
    // ✅ Push to /categories/[categoryEn]/[id]
    router.push(`/categories/${categoryEn}/${idx}`);
  };

  return (
    <>
      <CategoryHeader
        title={categoryBlock.category}
        onBack={() => router.back()}
      />

      <div className="flex flex-col px-4 py-7 bg-surface-0 min-h-screen">
        <div className="grid grid-cols-[repeat(auto-fit,minmax(98px,1fr))] gap-3 sm:gap-4">
          {categoryBlock.questions.map((item, idx) => {
            const imageUrl = `https://static-assets.animall.in/static/images/category/${encodeURIComponent(
              item.q,
            )}_v2.png`;

            return (
              <div
                key={idx}
                onClick={() => handleCardClick(idx)}
                className="w-full rounded-lg overflow-hidden border-2 border-surface-3 
                     shadow-[0px_0px_8px_0px_rgba(0,0,0,0.1)] cursor-pointer"
                style={{ aspectRatio: '7 / 10' }} // keeps card shape while width grows/shrinks
              >
                <img
                  src={imageUrl}
                  alt={item.q}
                  className="w-full h-full object-cover"
                  loading="lazy"
                />
              </div>
            );
          })}
        </div>

        <AllCategoryFooter />
      </div>
    </>
  );
}
