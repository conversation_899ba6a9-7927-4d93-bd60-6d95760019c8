import { useEffect, useRef, useState } from 'react';

import { useRouter } from 'next/router';

import { logAmplitudeEvent } from 'lib/log-event';
import { getCookie } from 'lib/storage';
import { compareAppVersions } from 'lib/utils';

import { AnimallLoader } from 'components/AnimallLoader';
import AnswerBottomBar from 'components/categories/AnswerBottomBar';
import { PrimaryBody } from 'components/ui/typography';

import categoriesData from 'data/categoriesqn/categoriesqn';
import { toJpeg } from 'html-to-image';

export default function AnswerPage() {
  const router = useRouter();
  const shareRef = useRef(null);
  const [isSharing, setIsSharing] = useState(false);
  const nextFrame = () => new Promise(requestAnimationFrame);
  useEffect(() => {
    if (!router.isReady || !qa) return;

    logAmplitudeEvent('LANDED', 'SOLUTION', 'CATEGORIES', {
      SOURCE: router.query?.source || 'NA',
      CATEGORY: categoryBlock?.category || 'NA',

      QUESTION: qa?.q || 'NA',
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router.isReady, qa, categoryEn, idx]);

  const [overlayBg, setOverlayBg] = useState('#FFFCF4E5');

  useEffect(() => {
    const palette = ['#F9F3E1', '#F1FFDD', '#E5F0F9', '#F8ECFF'];
    const base = palette[Math.floor(Math.random() * palette.length)];
    // keep alpha like your old `#FFFCF4E5` (E5 ≈ 90%)
    setOverlayBg(`${base}E5`);
  }, []);

  // ✅ Get slug + id
  const { id, categoryEn } = router.query;
  const idx = parseInt(id, 10);

  // ✅ Lookup by categoryEn, not Hindi
  const categoryBlock = categoriesData.find((c) => c.categoryEn === categoryEn);

  const qa = categoryBlock?.questions[idx];

  if (!qa) {
    return <div className="p-4 text-red-500">Question not found</div>;
  }

  // --- helpers ---
  const isMobile = () => /Android|iPhone|iPad|iPod/i.test(navigator.userAgent);

  const openWhatsAppText = (caption) => {
    const waUrl = isMobile()
      ? `whatsapp://send?text=${encodeURIComponent(caption)}`
      : `https://web.whatsapp.com/send?text=${encodeURIComponent(caption)}`;
    window.open(waUrl, '_blank', 'noopener');
  };

  const handleShare = async () => {
    if (!shareRef.current) return;
    logAmplitudeEvent('CLICKED', 'SOLUTION', 'SHARE', {
      SOURCE: router.query?.source || 'NA',
      CATEGORY: categoryBlock?.category || 'NA',

      QUESTION: qa?.q || 'NA',
    });

    const caption = `${qa.q}\n\n ${qa.a}\n\nऔर जानकारी के लिए Animall ऐप खोलें!`;
    setIsSharing(true);

    try {
      await nextFrame();
      const dataUrl = await toJpeg(shareRef.current, {
        quality: 0.9,
        cacheBust: true,
      });

      const isAndroidShareSupported = compareAppVersions(
        getCookie('appVersion'),
        '>=',
        '1.6.5',
      );
      if (isAndroidShareSupported && window.Android?.shareCardOnWhatsapp) {
        window.Android.shareCardOnWhatsapp(caption, dataUrl);
        return;
      }

      openWhatsAppText(caption);
    } catch (err) {
      console.error('Error sharing card:', err);
      openWhatsAppText(caption);
    } finally {
      setIsSharing(false);
    }
  };

  return (
    <div className="min-h-screen bg-primary-400 flex flex-col pb-28">
      {/* 🔙 Back Button */}
      {isSharing && <AnimallLoader />}
      <button
        onClick={() => router.back()}
        className="absolute top-3 mb-4 left-4 z-10 flex items-center"
      >
        <BackButton className="absolute top-3 left-4 z-10" />
      </button>

      {/* Outer Card (green) */}
      <div className="flex flex-col items-center justify-center flex-grow mt-5">
        <div className="w-[304px] h-[572px] rounded-3xl bg-primary-600 shadow-md flex flex-col items-center justify-between p-4">
          {/* Inner Card (white) */}
          <div
            ref={shareRef}
            className="relative w-[272px] h-[501px] rounded-xl overflow-hidden flex flex-col"
            style={{
              backgroundImage:
                'url(https://static-assets.animall.in/static/images/category/background.png)',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
            }}
          >
            <div
              className="absolute inset-0"
              style={{ backgroundColor: overlayBg }}
            />

            <div className="relative flex flex-col items-center mt-7 h-full">
              <AnimallIcon className="w-[90px] h-[28px] " />

              <div className="bg-[#F8D851] text-black px-4 mt-3 pt-1.5 py-1 rounded-3xl flex items-center justify-center">
                <PrimaryBody className="font-extrabold text-base text-center text-primary-900">
                  {qa.q}
                </PrimaryBody>
              </div>

              <div className="flex-grow flex items-center justify-center px-5 py-2">
                <PrimaryBody className="text-3xl font-bold font-Anek Devanagari leading-relaxed text-center">
                  {qa.a}
                </PrimaryBody>
              </div>

              <img
                src="https://static-assets.animall.in/static/images/category/Answerscows.png"
                alt="Answer cows"
                className="w-full"
                crossOrigin="anonymous"
              />
            </div>
          </div>

          {/* Share Button */}
          <button
            className="mt-3 flex items-center gap-1 text-sm font-medium text-primary-25"
            onClick={handleShare}
          >
            <ShareIcon />
            <PrimaryBody className="text-primary-25">शेयर करें</PrimaryBody>
          </button>
        </div>
      </div>

      {/* Bottom Action Bar */}

      <AnswerBottomBar
        qa={qa}
        category={categoryBlock?.category} // Hindi name ok
        robotHref={{
          pathname: '/robots/breed-advice',
          query: { q: qa?.q, a: qa?.a },
        }}
      />
    </div>
  );
}

function ShareIcon({ className = 'h-3 w-4' }) {
  return (
    <svg
      className={className}
      width="16"
      height="13"
      viewBox="0 0 16 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M15.5 6.1875L9.25 0.25V3.58336C3.41664 4.41668 1.33332 8.58336 0.5 12.75C2.58332 9.83336 5.08332 8.49992 9.25 8.49992V11.9167L15.5 6.1875Z"
        fill="#D9FFFC"
      />
    </svg>
  );
}

function BackButton() {
  return (
    <svg
      width="16"
      height="24"
      viewBox="0 0 24 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.20874 6.97146L23.7282 6.97146L23.7282 9.02854L4.20874 9.02854L9.72584 14.5456L8.27148 16L0.271484 8L8.27148 1.55766e-06L9.72584 1.45436L4.20874 6.97146Z"
        fill="#2E3C4D"
      />
    </svg>
  );
}

const AnimallIcon = ({}) => {
  return (
    <svg
      width="92"
      height="29"
      viewBox="0 0 92 29"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M10.2628 11.9727C8.97592 11.6496 8.72247 10.5092 9.17356 9.47046C9.26528 9.23328 9.41546 9.02725 9.60805 8.87437C10.7214 8.0489 11.0579 6.82605 11.1408 5.48203C11.1401 5.20563 11.1883 4.93158 11.2827 4.67433C11.3737 4.38375 11.6285 4.18841 11.9148 4.18971C12.2392 4.18971 12.4217 4.3981 12.5303 4.68402C12.8004 5.40126 12.6571 6.10396 12.4202 6.79697C12.3357 7.05382 12.1999 7.29451 12.1396 7.55621C12.0792 7.8179 12.0491 8.12321 12.3598 8.26375C12.648 8.3946 12.826 8.16844 12.9301 7.95198C13.5562 6.65966 13.8036 5.36734 13.1127 3.98617C12.7415 3.23986 12.1592 2.77624 11.4652 2.42246C11.0352 2.20761 10.5932 2.01377 10.2583 1.63738C9.97013 1.3143 9.8253 0.968602 10.0471 0.53729H10.1572C10.5329 0.761831 10.9462 0.81837 11.349 0.891063C12.8758 1.18668 14.036 1.98469 14.7556 3.4967C15.0573 4.14286 15.2519 4.54187 15.3591 5.23972C15.4057 5.6148 15.4087 5.99462 15.3681 6.3705C15.3375 6.74319 15.2871 7.11371 15.2172 7.48028C14.819 9.38484 13.9379 10.8726 12.1592 11.5996C11.7969 11.7658 11.4191 11.8903 11.0322 11.9711C10.7781 12.0231 10.5171 12.0237 10.2628 11.9727Z"
        fill="#14776F"
      />
      <path
        d="M5.84605 11.9719C7.13466 11.6488 7.38664 10.508 6.93548 9.46912C6.84377 9.23234 6.69417 9.02638 6.50242 8.87291C5.38885 8.04727 5.05236 6.82415 4.96937 5.47985C4.96857 5.20354 4.92049 4.92973 4.82753 4.67198C4.73587 4.38108 4.4805 4.18576 4.19379 4.18726C3.90953 4.19109 3.66122 4.39396 3.58268 4.68652C3.3141 5.40391 3.45744 6.10837 3.6883 6.79991C3.7728 7.05682 3.90861 7.29756 3.96896 7.55931C4.02932 7.82106 4.0595 8.12644 3.74866 8.26701C3.43783 8.40758 3.2809 8.17168 3.17678 7.95517C2.55059 6.66258 2.30162 5.36998 2.99421 3.98852C3.37596 3.24366 3.95689 2.77994 4.65401 2.42609C5.08405 2.2112 5.52616 2.01731 5.86114 1.64084C6.14783 1.32254 6.29872 0.971925 6.06786 0.53729H5.95771C5.58802 0.761879 5.17005 0.81843 4.76567 0.891138C3.23563 1.18359 2.07377 1.98177 1.35402 3.4941C1.05224 4.1404 0.862115 4.54757 0.750456 5.2391C0.704194 5.61426 0.700647 5.99405 0.739893 6.37013C0.771361 6.74225 0.821733 7.11227 0.890784 7.47853C1.29517 9.3851 2.17487 10.8781 3.95086 11.6003C4.31314 11.7668 4.69101 11.8914 5.07801 11.9719C5.33177 12.0234 5.59229 12.0234 5.84605 11.9719Z"
        fill="#14776F"
      />
      <path
        d="M8.20884 6.53448C8.49754 6.53448 8.90709 6.60168 8.96416 7.11685C9.0078 7.50243 8.53783 7.9168 8.08127 7.9248C7.65326 7.9392 7.16146 7.53922 7.14299 7.16165C7.12453 6.78407 7.48541 6.53448 8.20884 6.53448Z"
        fill="#14776F"
      />
      <path
        d="M10.3563 4.93171C10.3638 5.10752 10.2962 5.27818 10.1705 5.40078C10.0448 5.52337 9.87296 5.58625 9.69818 5.5736C9.51112 5.59617 9.32346 5.53644 9.18351 5.40977C9.04355 5.28309 8.96496 5.10185 8.96795 4.91267C8.94525 4.53619 9.23039 4.21236 9.60524 4.18888H9.64507C10.0453 4.16793 10.3355 4.4003 10.3563 4.93171Z"
        fill="#14776F"
      />
      <path
        d="M7.14224 4.93171C7.14973 5.10752 7.08212 5.27818 6.95641 5.40078C6.83071 5.52337 6.65886 5.58625 6.48408 5.5736C6.29702 5.59617 6.10936 5.53644 5.96941 5.40977C5.82945 5.28309 5.75086 5.10185 5.75385 4.91267C5.73115 4.53619 6.01629 4.21236 6.39114 4.18888H6.43098C6.83118 4.16793 7.12138 4.4003 7.14224 4.93171Z"
        fill="#14776F"
      />
      <path
        d="M26.6557 11.096L25.9076 8.65024H22.1671L21.419 11.096H19.074L22.6994 0.780736H25.3609L29.0008 11.096H26.6557ZM24.6416 4.43495C24.5936 4.2719 24.5313 4.06569 24.4546 3.81632C24.3778 3.56695 24.3011 3.31279 24.2244 3.05383C24.1476 2.79487 24.0853 2.56948 24.0374 2.37766C23.9894 2.56948 23.9223 2.80925 23.8359 3.09699C23.7592 3.37513 23.6825 3.64368 23.6058 3.90264C23.5386 4.15201 23.4859 4.32944 23.4475 4.43495L22.7138 6.82313H25.3897L24.6416 4.43495Z"
        fill="#14776F"
      />
      <path
        d="M34.589 3.09699C35.433 3.09699 36.1091 3.32717 36.6175 3.78755C37.1258 4.23833 37.38 4.96725 37.38 5.97432V11.096H35.2364V6.50663C35.2364 5.94075 35.1356 5.51874 34.9342 5.2406C34.7328 4.95287 34.4115 4.809 33.9703 4.809C33.3181 4.809 32.8721 5.03439 32.6324 5.48517C32.3926 5.92636 32.2727 6.56417 32.2727 7.3986V11.096H30.1291V3.24085H31.7692L32.0569 4.24792H32.172C32.3446 3.97937 32.5556 3.76357 32.805 3.60052C33.0544 3.42788 33.3325 3.3032 33.6394 3.22647C33.9464 3.14015 34.2629 3.09699 34.589 3.09699Z"
        fill="#14776F"
      />
      <path
        d="M41.728 3.24085V11.096H39.5844V3.24085H41.728ZM40.6634 0.162109C40.9799 0.162109 41.2532 0.238838 41.4834 0.392296C41.7136 0.536163 41.8287 0.809509 41.8287 1.21234C41.8287 1.60557 41.7136 1.87892 41.4834 2.03237C41.2532 2.18583 40.9799 2.26256 40.6634 2.26256C40.3373 2.26256 40.0591 2.18583 39.829 2.03237C39.6084 1.87892 39.4981 1.60557 39.4981 1.21234C39.4981 0.809509 39.6084 0.536163 39.829 0.392296C40.0591 0.238838 40.3373 0.162109 40.6634 0.162109Z"
        fill="#14776F"
      />
      <path
        d="M53.1897 3.09699C54.0816 3.09699 54.753 3.32717 55.2038 3.78755C55.6642 4.23833 55.8944 4.96725 55.8944 5.97432V11.096H53.7508V6.50663C53.7508 5.94075 53.6548 5.51874 53.463 5.2406C53.2712 4.95287 52.9739 4.809 52.571 4.809C52.0052 4.809 51.6023 5.01041 51.3626 5.41324C51.1228 5.81607 51.0029 6.39633 51.0029 7.15403V11.096H48.8593V6.50663C48.8593 6.13257 48.8161 5.82086 48.7298 5.57149C48.6435 5.32212 48.514 5.1351 48.3414 5.01041C48.1687 4.87614 47.9481 4.809 47.6796 4.809C47.2863 4.809 46.9746 4.90971 46.7444 5.11112C46.5143 5.31253 46.3512 5.60506 46.2553 5.98871C46.1594 6.37235 46.1114 6.84231 46.1114 7.3986V11.096H43.9678V3.24085H45.6079L45.8956 4.24792H46.0107C46.1738 3.97937 46.3752 3.76357 46.615 3.60052C46.8547 3.42788 47.1185 3.3032 47.4062 3.22647C47.7036 3.14015 48.0009 3.09699 48.2982 3.09699C48.8737 3.09699 49.3628 3.1929 49.7656 3.38472C50.1685 3.56695 50.4754 3.85469 50.6864 4.24792H50.8734C51.1132 3.84509 51.4441 3.55257 51.8661 3.37033C52.2977 3.1881 52.7389 3.09699 53.1897 3.09699Z"
        fill="#14776F"
      />
      <path
        d="M61.3242 3.0826C62.3792 3.0826 63.1849 3.31279 63.7412 3.77316C64.307 4.22394 64.59 4.9193 64.59 5.85923V11.096H63.0938L62.6766 10.0314H62.619C62.3984 10.3095 62.1682 10.5397 61.9284 10.7219C61.6983 10.9041 61.4297 11.0336 61.1228 11.1104C60.8255 11.1967 60.4562 11.2398 60.015 11.2398C59.5546 11.2398 59.1374 11.1535 58.7634 10.9809C58.3989 10.7986 58.1112 10.5253 57.9002 10.1608C57.6892 9.78678 57.5837 9.31682 57.5837 8.75094C57.5837 7.91652 57.8762 7.30269 58.4613 6.90945C59.0463 6.50663 59.9239 6.28603 61.094 6.24767L62.4607 6.20451V5.85923C62.4607 5.44681 62.3505 5.14469 62.1299 4.95287C61.9189 4.76104 61.6215 4.66513 61.2379 4.66513C60.8542 4.66513 60.4802 4.72268 60.1157 4.83777C59.7513 4.94328 59.3868 5.07755 59.0223 5.2406L58.3174 3.78755C58.7394 3.56695 59.2046 3.39431 59.7129 3.26963C60.2308 3.14494 60.7679 3.0826 61.3242 3.0826ZM61.6263 7.48492C60.9358 7.5041 60.4562 7.62879 60.1877 7.85897C59.9191 8.08916 59.7848 8.39128 59.7848 8.76533C59.7848 9.09143 59.8807 9.32641 60.0726 9.47028C60.2644 9.60455 60.5138 9.67169 60.8207 9.67169C61.281 9.67169 61.6695 9.53742 61.986 9.26886C62.3025 8.99072 62.4607 8.60228 62.4607 8.10355V7.45614L61.6263 7.48492Z"
        fill="#14776F"
      />
      <path
        d="M68.9278 11.096H66.7842V0.162109H68.9278V11.096Z"
        fill="#14776F"
      />
      <path
        d="M73.3112 11.096H71.1676V0.162109H73.3112V11.096Z"
        fill="#14776F"
      />
      <path
        d="M75.2489 10.0889C75.2489 9.64771 75.3688 9.3408 75.6086 9.16816C75.8484 8.98593 76.1409 8.89481 76.4862 8.89481C76.8219 8.89481 77.1096 8.98593 77.3494 9.16816C77.5891 9.3408 77.709 9.64771 77.709 10.0889C77.709 10.5109 77.5891 10.8178 77.3494 11.0096C77.1096 11.1919 76.8219 11.283 76.4862 11.283C76.1409 11.283 75.8484 11.1919 75.6086 11.0096C75.3688 10.8178 75.2489 10.5109 75.2489 10.0889Z"
        fill="#14776F"
      />
      <path
        d="M81.7971 3.24085V11.096H79.6535V3.24085H81.7971ZM80.7325 0.162109C81.049 0.162109 81.3223 0.238838 81.5525 0.392296C81.7827 0.536163 81.8978 0.809509 81.8978 1.21234C81.8978 1.60557 81.7827 1.87892 81.5525 2.03237C81.3223 2.18583 81.049 2.26256 80.7325 2.26256C80.4064 2.26256 80.1282 2.18583 79.8981 2.03237C79.6775 1.87892 79.5672 1.60557 79.5672 1.21234C79.5672 0.809509 79.6775 0.536163 79.8981 0.392296C80.1282 0.238838 80.4064 0.162109 80.7325 0.162109Z"
        fill="#14776F"
      />
      <path
        d="M88.4968 3.09699C89.3408 3.09699 90.017 3.32717 90.5253 3.78755C91.0336 4.23833 91.2878 4.96725 91.2878 5.97432V11.096H89.1442V6.50663C89.1442 5.94075 89.0435 5.51874 88.8421 5.2406C88.6406 4.95287 88.3194 4.809 87.8782 4.809C87.226 4.809 86.78 5.03439 86.5402 5.48517C86.3004 5.92636 86.1805 6.56417 86.1805 7.3986V11.096H84.0369V3.24085H85.677L85.9647 4.24792H86.0798C86.2525 3.97937 86.4635 3.76357 86.7128 3.60052C86.9622 3.42788 87.2404 3.3032 87.5473 3.22647C87.8542 3.14015 88.1707 3.09699 88.4968 3.09699Z"
        fill="#14776F"
      />
      <path
        d="M6.26185 23.784C6.42191 23.6804 6.54902 23.5015 6.64317 23.2473C6.73733 22.9836 6.7844 22.6965 6.7844 22.3858C6.7844 21.9338 6.69025 21.5101 6.50194 21.1147C6.32305 20.7098 6.07825 20.3473 5.76754 20.0272H4.00216V19.1233H12.2077V20.0272H10.9366V28.0209H9.83499V20.0272H6.91151C7.02449 20.1308 7.13748 20.2626 7.25046 20.4227C7.37286 20.5827 7.48114 20.771 7.57529 20.9876C7.67886 21.2041 7.75889 21.4442 7.81539 21.7079C7.88129 21.9715 7.91425 22.2587 7.91425 22.5694C7.91425 23.0025 7.85776 23.3838 7.74477 23.7133C7.6412 24.0429 7.49526 24.3206 7.30696 24.5466C7.12806 24.7632 6.92093 24.9279 6.68554 25.0409C6.45957 25.1539 6.2289 25.2104 5.99351 25.2104C5.82403 25.2104 5.66397 25.1821 5.51333 25.1256C5.36268 25.0692 5.22616 24.9891 5.10376 24.8856C4.98136 24.782 4.8825 24.6596 4.80717 24.5184C4.74126 24.3771 4.70831 24.2171 4.70831 24.0382C4.70831 23.784 4.78834 23.5862 4.9484 23.445C5.11788 23.2944 5.30619 23.219 5.51333 23.219L6.26185 23.784Z"
        fill="#14776F"
      />
      <path
        d="M13.1997 20.0272H11.9286V19.1233H15.5723V20.0272H14.3013V28.0209H13.1997V20.0272Z"
        fill="#14776F"
      />
      <path
        d="M22.2804 20.0272V28.0209H21.1788V25.5211C21.0188 25.6623 20.7881 25.7941 20.4868 25.9165C20.1855 26.0389 19.7806 26.1001 19.2722 26.1001C18.8579 26.1001 18.4578 26.0295 18.0717 25.8883C17.6857 25.7471 17.342 25.5352 17.0408 25.2528C16.7395 24.9609 16.4947 24.6031 16.3064 24.1794C16.118 23.7463 16.0192 23.2473 16.0098 22.6824L16.4193 22.6682C17.1255 22.6494 17.6151 22.5364 17.8881 22.3293C18.1706 22.1127 18.3118 21.7926 18.3118 21.3689C18.3118 21.1053 18.2553 20.8652 18.1424 20.6486C18.0294 20.4227 17.8834 20.2155 17.7045 20.0272H15.3036V19.1233H23.5515V20.0272H22.2804ZM21.1788 20.0272H18.8061C18.9662 20.1873 19.1121 20.3944 19.244 20.6486C19.3758 20.8934 19.4417 21.1759 19.4417 21.496C19.4417 22.108 19.2392 22.5929 18.8344 22.9507C18.4295 23.2991 17.8552 23.4968 17.1114 23.5439C17.2055 24.024 17.4409 24.4101 17.8175 24.702C18.1941 24.9938 18.6932 25.1398 19.3146 25.1398C19.71 25.1398 20.0725 25.0692 20.402 24.9279C20.741 24.7867 20.9999 24.6031 21.1788 24.3771V20.0272Z"
        fill="#14776F"
      />
      <path
        d="M30.3096 25.7471C30.2626 25.8036 30.1778 25.8601 30.0554 25.9165C29.933 25.973 29.7871 26.0013 29.6176 26.0013C29.4293 26.0013 29.2457 25.9683 29.0668 25.9024C28.8973 25.8365 28.742 25.7424 28.6007 25.62C28.4689 25.4976 28.3606 25.3516 28.2759 25.1821C28.1912 25.0127 28.1488 24.8244 28.1488 24.6172C28.1488 24.3724 28.2335 24.1794 28.403 24.0382C28.5725 23.8875 28.7938 23.8122 29.0668 23.8122H29.2222V20.8605C29.2222 20.5498 29.1657 20.3097 29.0527 20.1402C28.9397 19.9707 28.7608 19.886 28.516 19.886C28.2618 19.886 28.0546 19.9566 27.8946 20.0978C27.7439 20.2297 27.6686 20.4509 27.6686 20.7616C27.6686 21.0064 27.7298 21.2277 27.8522 21.4254C27.984 21.6231 28.1582 21.7832 28.3748 21.9056L27.9652 22.6965C27.7957 22.64 27.6262 22.5553 27.4568 22.4423C27.2873 22.3199 27.1366 22.1739 27.0048 22.0044C26.873 21.835 26.7647 21.6467 26.68 21.4395C26.6047 21.223 26.567 20.9923 26.567 20.7475C26.567 20.4933 26.6094 20.2579 26.6941 20.0413C26.7883 19.8248 26.9154 19.6365 27.0754 19.4764C27.2449 19.3069 27.4426 19.1798 27.6686 19.0951C27.904 19.0009 28.1629 18.9539 28.4454 18.9539C29.0856 18.9539 29.5564 19.1328 29.8577 19.4905C30.159 19.8389 30.3096 20.3097 30.3096 20.9028V23.8122H32.8235V20.0272H31.3265V19.1233H35.1962V20.0272H33.9251V28.0209H32.8235V24.7302H30.3096V25.7471Z"
        fill="#14776F"
      />
      <path
        d="M34.268 17.0896C34.268 16.9013 34.3433 16.7318 34.494 16.5812C34.6352 16.4211 34.8188 16.3411 35.0448 16.3411C35.2802 16.3411 35.4638 16.4211 35.5956 16.5812C35.7368 16.7318 35.8074 16.9013 35.8074 17.0896C35.8074 17.3062 35.7368 17.4898 35.5956 17.6404C35.4544 17.7817 35.2708 17.8523 35.0448 17.8523C34.8188 17.8523 34.6352 17.7817 34.494 17.6404C34.3433 17.4898 34.268 17.3062 34.268 17.0896ZM33.7878 19.547H33.1099L31.9095 18.6149C31.7212 18.4737 31.5517 18.3607 31.401 18.276C31.2504 18.1818 31.1092 18.1112 30.9773 18.0641C30.8455 18.0076 30.7137 17.9747 30.5819 17.9653C30.4407 17.9464 30.29 17.937 30.13 17.937C29.9887 17.937 29.8334 17.9558 29.6639 17.9935C29.485 18.0312 29.3344 18.0735 29.212 18.1206L28.9154 17.245C29.0566 17.1697 29.2355 17.1085 29.452 17.0614C29.6592 17.0049 29.8757 16.9766 30.1017 16.9766C30.5348 16.9766 30.9161 17.0661 31.2457 17.245C31.5752 17.4239 31.9048 17.6875 32.2343 18.0359L32.8698 18.7279H32.9263L32.5168 17.5981C32.3096 17.0331 32.0272 16.6424 31.6694 16.4258C31.3022 16.1999 30.9067 16.0869 30.483 16.0869C30.3606 16.0869 30.2429 16.0916 30.13 16.101C30.0076 16.1104 29.8946 16.1245 29.791 16.1434L29.678 15.183C29.7722 15.1642 29.9087 15.1453 30.0876 15.1265C30.2665 15.1077 30.4171 15.0983 30.5395 15.0983C31.2457 15.0983 31.8106 15.2772 32.2343 15.6349C32.6486 15.9833 32.9499 16.5012 33.1382 17.1885L33.7878 19.547Z"
        fill="#14776F"
      />
      <path
        d="M39.5598 27.5266L38.5995 28.1621L35.8737 24.1653C35.7231 23.9487 35.6195 23.784 35.563 23.671C35.5159 23.5486 35.4924 23.4356 35.4924 23.332C35.4924 23.1343 35.5677 22.9836 35.7183 22.8801C35.869 22.7765 36.0432 22.7247 36.2409 22.7247L36.5375 23.106C36.6505 23.2567 36.7682 23.3603 36.8906 23.4168C37.013 23.4732 37.1683 23.5015 37.3566 23.5015C37.7238 23.5015 38.0345 23.3603 38.2887 23.0778C38.543 22.7953 38.6701 22.3999 38.6701 21.8915C38.6701 21.4678 38.6042 21.1006 38.4723 20.7899C38.3405 20.4792 38.1946 20.2249 38.0345 20.0272H34.9275V19.1233H44.63V20.0272H43.3589V28.0209H42.2573V24.363C41.8807 24.5513 41.3535 24.6455 40.6755 24.6455C40.346 24.6455 40.0165 24.589 39.6869 24.476C39.3574 24.363 39.0702 24.2218 38.8254 24.0523C38.4582 24.3065 38.0157 24.4336 37.4979 24.4336C37.4508 24.4336 37.4131 24.4336 37.3849 24.4336C37.3566 24.4336 37.319 24.4289 37.2719 24.4195L39.5598 27.5266ZM40.5343 23.6568C40.9298 23.6568 41.2687 23.6098 41.5512 23.5156C41.8431 23.4215 42.0784 23.3038 42.2573 23.1625V20.0272H39.122C39.2727 20.1967 39.4233 20.4556 39.5739 20.804C39.7246 21.1429 39.7999 21.5243 39.7999 21.948C39.7999 22.5223 39.6681 23.0166 39.4045 23.4309C39.574 23.5062 39.7481 23.5627 39.927 23.6004C40.1059 23.638 40.3083 23.6568 40.5343 23.6568Z"
        fill="#14776F"
      />
      <path
        d="M50.9579 21.383C51.4664 21.383 51.8901 21.4725 52.229 21.6514C52.5774 21.8303 52.8457 22.028 53.034 22.2445V20.0272H47.4978V19.1233H55.4067V20.0272H54.1356V28.0209H53.034V25.8036C52.8646 25.973 52.6198 26.1425 52.2996 26.312C51.9889 26.4815 51.5652 26.5662 51.0286 26.5662C50.6425 26.5662 50.28 26.5097 49.9411 26.3967C49.6021 26.2743 49.3008 26.1001 49.0372 25.8742C48.783 25.6388 48.5806 25.3563 48.4299 25.0268C48.2793 24.6878 48.2039 24.2971 48.2039 23.8546C48.2039 23.4497 48.2698 23.0966 48.4017 22.7953C48.5429 22.4846 48.7359 22.2257 48.9807 22.0186C49.2349 21.8114 49.5268 21.6561 49.8563 21.5525C50.1953 21.4395 50.5625 21.383 50.9579 21.383ZM53.034 23.3744C52.874 23.1108 52.6292 22.8707 52.2996 22.6541C51.9701 22.4376 51.5935 22.3293 51.1698 22.3293C50.6331 22.3293 50.1953 22.4611 49.8563 22.7247C49.5268 22.9789 49.362 23.3697 49.362 23.8969C49.362 24.5278 49.5315 24.9703 49.8705 25.2245C50.2094 25.4787 50.6661 25.6058 51.2404 25.6058C51.4852 25.6058 51.7159 25.5682 51.9324 25.4928C52.149 25.4081 52.3373 25.2998 52.4974 25.168C52.6668 25.0268 52.7986 24.862 52.8928 24.6737C52.987 24.4854 53.034 24.283 53.034 24.0664V23.3744Z"
        fill="#14776F"
      />
      <path
        d="M56.4057 20.0272H55.1346V19.1233H58.7783V20.0272H57.5073V28.0209H56.4057V20.0272Z"
        fill="#14776F"
      />
      <path
        d="M63.1843 23.1202C63.0243 22.9507 62.8219 22.8236 62.5771 22.7388C62.3417 22.6541 62.1251 22.6117 61.9274 22.6117C61.466 22.6117 61.0847 22.7388 60.7834 22.9931C60.4916 23.2473 60.3456 23.6521 60.3456 24.2076C60.3456 24.5937 60.4068 24.942 60.5292 25.2528C60.661 25.554 60.8352 25.8318 61.0518 26.086C61.2683 26.3402 61.5178 26.5709 61.8003 26.778C62.0922 26.9852 62.3982 27.1735 62.7183 27.343L62.0404 28.148C61.6544 27.9408 61.2872 27.7055 60.9388 27.4418C60.5998 27.1688 60.3032 26.8675 60.049 26.538C59.7948 26.2084 59.5924 25.8459 59.4417 25.4505C59.2911 25.055 59.2158 24.6219 59.2158 24.1512C59.2158 23.7181 59.2864 23.3461 59.4276 23.0354C59.5688 22.7247 59.7572 22.4705 59.9925 22.2728C60.2279 22.0656 60.4963 21.915 60.7976 21.8209C61.1083 21.7173 61.4284 21.6655 61.7579 21.6655C62.1157 21.6655 62.45 21.7361 62.7607 21.8773C63.0808 22.0092 63.3585 22.1975 63.5939 22.4423C63.7634 22.2069 63.9894 22.0233 64.2718 21.8915C64.5637 21.7597 64.8744 21.6937 65.2039 21.6937C65.3546 21.6937 65.5099 21.7079 65.67 21.7361C65.8395 21.7549 65.9666 21.7879 66.0513 21.835V20.0272H58.5096V19.1233H68.424V20.0272H67.1529V28.0209H66.0513V22.7388C65.976 22.7012 65.8819 22.6729 65.7689 22.6541C65.6653 22.6259 65.5335 22.6117 65.3734 22.6117C64.8932 22.6117 64.5449 22.8189 64.3283 23.2332C64.1212 23.638 64.0035 24.2406 63.9752 25.0409H62.8878C62.8878 24.589 62.916 24.2076 62.9725 23.8969C63.0384 23.5768 63.109 23.3179 63.1843 23.1202Z"
        fill="#14776F"
      />
      <path
        d="M69.4173 20.0272H68.1463V19.1233H71.79V20.0272H70.5189V28.0209H69.4173V20.0272Z"
        fill="#14776F"
      />
      <path
        d="M82.2883 19.1233V20.0272H81.1585V22.3717C81.1585 22.7577 81.1208 23.0966 81.0455 23.3885C80.9702 23.6804 80.8572 23.944 80.7065 24.1794C80.5559 24.4054 80.3676 24.6078 80.1416 24.7867C79.9156 24.9562 79.652 25.1162 79.3507 25.2669L78.6869 24.3348C79.6002 23.9676 80.0569 23.3085 80.0569 22.3575V20.0272H76.8933V22.7953C76.8933 23.4638 76.9639 24.0335 77.1052 24.5042C77.2464 24.9656 77.4677 25.361 77.7689 25.6906C78.0797 26.0201 78.4657 26.3026 78.927 26.538C79.3978 26.7639 79.958 26.9711 80.6077 27.1594L80.1416 28.148C79.4072 27.9503 78.767 27.7055 78.2209 27.4136C77.6748 27.1123 77.2229 26.7498 76.8651 26.3261C76.5073 25.9024 76.2389 25.4081 76.0601 24.8432C75.8812 24.2783 75.7917 23.6286 75.7917 22.8942V20.0272H74.6619V19.1233H79.9015L79.0965 17.9088C78.9647 17.711 78.8329 17.5369 78.7011 17.3862C78.5693 17.2261 78.4233 17.0943 78.2633 16.9908C78.1126 16.8778 77.9431 16.7977 77.7548 16.7507C77.5665 16.6942 77.35 16.6659 77.1052 16.6659C76.9169 16.6659 76.7333 16.6895 76.5544 16.7365C76.3849 16.7742 76.2342 16.8213 76.1024 16.8778L75.7211 15.875C75.8812 15.7903 76.0836 15.7197 76.3284 15.6632C76.5826 15.5973 76.8415 15.5643 77.1052 15.5643C77.7925 15.5643 78.3621 15.7385 78.814 16.0869C79.266 16.4258 79.652 16.9013 79.9721 17.5133L80.8195 19.1233H82.2883Z"
        fill="#14776F"
      />
      <path
        d="M89.5084 20.0272H88.2373V28.0209H87.1357V24.6172C86.9662 24.7679 86.7403 24.8997 86.4578 25.0127C86.1753 25.1256 85.8364 25.1821 85.4409 25.1821C85.1114 25.1821 84.8007 25.1351 84.5088 25.0409C84.2169 24.9468 83.958 24.8008 83.732 24.6031C83.5061 24.4054 83.3272 24.1606 83.1954 23.8687C83.0635 23.5768 82.9976 23.2332 82.9976 22.8377V20.0272H82.009V19.1233H89.5084V20.0272ZM84.0992 22.7247C84.0992 23.2614 84.2216 23.6474 84.4664 23.8828C84.7112 24.1182 85.0596 24.2359 85.5116 24.2359C85.8599 24.2359 86.1847 24.1559 86.486 23.9958C86.7873 23.8263 87.0039 23.6192 87.1357 23.3744V20.0272H84.0992V22.7247Z"
        fill="#14776F"
      />
    </svg>
  );
};
