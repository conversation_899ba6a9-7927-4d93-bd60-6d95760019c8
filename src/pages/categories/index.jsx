import { useEffect, useState } from 'react';

import { serverSideTranslations } from 'next-i18next/serverSideTranslations';
import { useRouter } from 'next/router';

import { logAmplitudeEvent } from 'lib/log-event';
import { selectedLanguage } from 'lib/utils';

import BottomBar from 'components/BottomBar';
import AllCategoryFooter from 'components/categories/AllCategoriesFooter';
import AllCategoryHeader from 'components/categories/AllCategoriesHeader';
import { H3, PrimaryBody } from 'components/ui/typography';

import categoriesData from 'data/categoriesqn/categoriesqn';
import useAppPaywall from 'modules/app-subscription/hooks/useAppPaywall';
import AppTrialPopup from 'modules/home/<USER>/components/AppTrialPopup';
import { FEATURES } from 'modules/home/<USER>/constant';

function shuffle(arr) {
  const a = arr.slice();
  for (let i = a.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [a[i], a[j]] = [a[j], a[i]];
  }
  return a;
}

export default function CategoriesIndex() {
  const router = useRouter();
  // start empty to avoid any pre-shuffle render; UI stays the same
  const [blocks, setBlocks] = useState([]);

  const { isPremiumUser } = useAppPaywall();
  const [showPaywall, setShowPaywall] = useState(false);
  const openPaywall = () => {
    setShowPaywall(true);
  };

  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Try to restore order for this session
    const stored = window.sessionStorage.getItem('catOrder');
    if (stored) {
      try {
        const orderMap = JSON.parse(stored); // { [categoryEn]: number[] }
        const rebuilt = categoriesData.map((cat) => {
          const baseQs = (cat.questions || []).map((q, i) => ({
            ...q,
            _originalIndex: i,
          }));
          const order = orderMap?.[cat.categoryEn];
          if (Array.isArray(order) && order.length) {
            const reordered = order.map((idx) => baseQs[idx]).filter(Boolean);
            return { ...cat, questions: reordered };
          }
          return { ...cat, questions: baseQs };
        });
        setBlocks(rebuilt);
        return;
      } catch {
        // fall through and create fresh shuffle
      }
    }

    // First time this session → shuffle, persist order, then render
    const shuffledBlocks = categoriesData.map((cat) => {
      const baseQs = (cat.questions || []).map((q, i) => ({
        ...q,
        _originalIndex: i,
      }));
      const orderIdx = shuffle(baseQs.map((_, i) => i)); // shuffle indices
      const reordered = orderIdx.map((i) => baseQs[i]);
      return { ...cat, questions: reordered };
    });

    const orderMap = {};
    shuffledBlocks.forEach((cat) => {
      orderMap[cat.categoryEn] = (cat.questions || []).map(
        (q) => q._originalIndex,
      );
    });
    window.sessionStorage.setItem('catOrder', JSON.stringify(orderMap));
    setBlocks(shuffledBlocks);
  }, []);

  const handleCardClick = (categoryEn, idx) => {
    if (!isPremiumUser) {
      openPaywall();
      return;
    }
    router.push({
      pathname: `/categories/${categoryEn}/${idx}`,
      query: { source: router.query?.source || 'AllCategories' },
    });
  };

  useEffect(() => {
    logAmplitudeEvent('LANDED', 'MAIN', 'CATEGORIES', {
      SOURCE: router.query?.source || 'NA',
    });
  }, [router.query?.source]);

  const handleSeeMore = (categoryEn) => {
    if (!isPremiumUser) {
      openPaywall();
      return;
    }
    router.push({
      pathname: `/categories/${categoryEn}`,
      query: { source: router.query?.source || 'AllCategories' },
    });
  };

  return (
    <>
      <AllCategoryHeader />

      <div className="flex flex-col gap-6 px-4 bg-surface-0 py-7 sm:max-w-md sm:mx-auto pb-24">
        {blocks.map((catBlock, catIdx) => (
          <div key={catIdx} className="flex flex-col gap-1">
            {/* Header + CTA */}
            <div className="flex items-center justify-between">
              <H3 className="text-lg sm:text-lg font-bold text-primary-900">
                {catBlock.category}
              </H3>
              <button onClick={() => handleSeeMore(catBlock.categoryEn)}>
                <PrimaryBody className="font-bold text-base sm:text-base text-primary-600">
                  और देखें
                </PrimaryBody>
              </button>
            </div>

            {/* Horizontal scroll cards */}
            <div className="flex gap-2 overflow-x-auto no-scrollbar">
              {(catBlock.questions || [])
                .slice(0, Math.min(8, catBlock.questions?.length || 0))
                .map((item, idx) => {
                  const imageUrl = `https://static-assets.animall.in/static/images/category/${encodeURIComponent(
                    item.q,
                  )}_v2.png`;
                  return (
                    <div
                      key={`${catIdx}-${item._originalIndex ?? idx}`}
                      onClick={() =>
                        handleCardClick(
                          catBlock.categoryEn,
                          item._originalIndex ?? idx,
                        )
                      }
                      className="flex-shrink-0 w-[30%] max-w-[98px] h-[140px] rounded-lg overflow-hidden border-2 border-surface-3 shadow-[0px_0px_8px_rgba(0,0,0,0.1)] cursor-pointer bg-white"
                    >
                      <img
                        src={imageUrl}
                        alt={item.q}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  );
                })}
            </div>
          </div>
        ))}

        <AllCategoryFooter />
      </div>

      <BottomBar />

      <AppTrialPopup
        show={showPaywall}
        setShow={setShowPaywall}
        featureName={FEATURES.TOP_CATEGORIES}
      />
    </>
  );
}

export async function getServerSideProps(context) {
  const { cookies } = context.req;
  const { accessToken, animallUserId, lang } = cookies;

  if (!accessToken || !animallUserId) {
    return {
      redirect: {
        destination: '/login?redirect=/categories',
        permanent: false,
      },
    };
  }

  return {
    props: {
      userId: animallUserId,
      source: 'CATEGORIES_PAGE',
      monetizationProps: {
        stats: null,
        source: 'CATEGORIES_PAGE',
        sections: ['app', 'listing'],
        includePlans: true,
      },
      ...(await serverSideTranslations(selectedLanguage(lang), [
        'common',
        'app_subscription',
        'trialPage',
        'categories',
      ])),
    },
  };
}
