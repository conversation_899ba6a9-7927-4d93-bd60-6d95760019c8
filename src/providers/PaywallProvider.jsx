import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from 'react';

import { getCookie } from 'lib/storage';
import { isInBuckets } from 'lib/utils';

import PaymentLoaderScreen from 'modules/app-subscription/PaymentLoaderScreen';
import PaymentSuccessScreen from 'modules/app-subscription/PaymentSuccessScreen';
import AppTrialPopupV2 from 'modules/home/<USER>/components/AppTrialPopupV2';
import MultiPlanAppTrialPopup from 'modules/home/<USER>/components/MultiPlanAppTrial';
import { parseCookies } from 'nookies';

import MultiPlanPopup from '../modules/app-subscription/MultiPlanPopup';
import RepeatPopup from '../modules/app-subscription/RepeatPopup';
import TrialPopup from '../modules/app-subscription/TrialPopup';
import useAppPaywall, {
  PaywallStatus,
} from '../modules/app-subscription/hooks/useAppPaywall';
import { useGrowthUser } from './GrowthUserProvider';

const PaywallContext = createContext({
  showPaywall: ({ onClose, onSuccess, onFailure, source }) => {},
  hidePaywall: () => {},
  setShowSuccessScreen: () => {},
  setShowLoaderScreen: () => {},
  showLoaderScreen: false,
  showSuccessScreen: false,
});

export const useShowPaywall = () => {
  return useContext(PaywallContext);
};

const NO_OP = async () => {};

const PaywallProvider = ({ children }) => {
  const { bucketId } = parseCookies();
  const { isGrowthUser, isLoaded } = useGrowthUser();
  const { status } = useAppPaywall();
  const {
    app,
    isEligible,
    isTrial,
    isPremiumUser,
    plans,
    listingRemainingLimit,
  } = useAppPaywall();

  const [showTrialPopupV2, setShowTrialPopupV2] = useState(false);
  const [paywallType, setPaywallType] = useState(null);
  const [paywallSource, setPaywallSource] = useState(null);
  const [paywallOnClose, setPaywallOnClose] = useState(() => NO_OP);
  const [paywallOnSuccess, setPaywallOnSuccess] = useState(() => NO_OP);
  const [paywallOnFailure, setPaywallOnFailure] = useState(() => NO_OP);
  const [showLoaderScreen, setShowLoaderScreen] = useState(false);
  const [showSuccessScreen, setShowSuccessScreen] = useState(false);

  const noOverlayScreenVisible = !showLoaderScreen && !showSuccessScreen;
  const showPaywall = useCallback(
    ({
      onClose = () => {},
      onSuccess = () => {},
      onFailure = () => {},
      source,
    } = {}) => {
      if (!isEligible || (isPremiumUser && listingRemainingLimit > 0)) return;

      setPaywallSource(source);
      setPaywallOnClose(() => onClose);
      setPaywallOnSuccess(() => onSuccess);
      setPaywallOnFailure(() => onFailure);

      // if (plans.length > 1) {
      //   setPaywallType('multi-plan');
      // } else if (isX1X2ExpUser) {
      //   setShowTrialPopupV2(true);
      // } else if (status === PaywallStatus.NO_TRIAL) {
      //   setPaywallType('no-trial');
      // } else if (isTrial) {
      //   setPaywallType('trial');
      // } else {
      //   setPaywallType('repeat');
      // }
      setShowTrialPopupV2(true);
    },
    [isEligible, isPremiumUser, listingRemainingLimit],
  );

  const hidePaywall = useCallback(() => {
    setPaywallType(null);
    setShowTrialPopupV2(false);
  }, []);

  const handleClose = useCallback(async () => {
    await paywallOnClose();
    hidePaywall();
  }, [paywallOnClose, hidePaywall]);

  const handleSuccessScreenClose = useCallback(() => {
    setShowSuccessScreen(false);
    // Reset all paywall states when success screen is closed
    setPaywallType(null);
    setShowTrialPopupV2(false);
    window.location.reload();
  }, []);

  useEffect(() => {
    if (paywallType) return;

    // Reset states when paywall is closed
    setPaywallSource(null);
    setPaywallOnClose(() => NO_OP);
    setPaywallOnSuccess(() => NO_OP);
    setPaywallOnFailure(() => NO_OP);
  }, [paywallType]);
  const isX4_X5User = isInBuckets([40, 59], getCookie('bucketId'));

  return (
    <PaywallContext.Provider
      value={{
        showPaywall,
        hidePaywall,
        setShowSuccessScreen,
        setShowLoaderScreen,
        showLoaderScreen,
        showSuccessScreen,
      }}
    >
      {/* Loader screen - shown during payment processing */}
      {showLoaderScreen && <PaymentLoaderScreen />}

      {/* Success screen - shown after successful payment */}
      {showSuccessScreen && (
        <PaymentSuccessScreen onClose={handleSuccessScreenClose} />
      )}

      {/* Multi-plan popup */}
      {paywallType === 'multi-plan' && noOverlayScreenVisible && (
        <MultiPlanPopup
          source={paywallSource}
          app={app}
          plans={plans}
          onClose={handleClose}
          onSuccess={paywallOnSuccess}
          onFailure={paywallOnFailure}
        />
      )}

      {/* Trial popup */}
      {(paywallType === 'trial' || paywallType === 'no-trial') &&
        noOverlayScreenVisible && (
          <TrialPopup
            source={paywallSource}
            onClose={handleClose}
            onSuccess={paywallOnSuccess} // Fixed: removed the arrow function
            onFailure={paywallOnFailure}
          />
        )}

      {noOverlayScreenVisible &&
        (isX4_X5User ? (
          <MultiPlanAppTrialPopup
            source={paywallSource}
            show={showTrialPopupV2}
            onClose={handleClose}
            onSuccess={paywallOnSuccess}
            onFailure={paywallOnFailure}
          />
        ) : (
          <AppTrialPopupV2
            source={paywallSource}
            show={showTrialPopupV2}
            onClose={handleClose}
            onSuccess={paywallOnSuccess}
            onFailure={paywallOnFailure}
          />
        ))}

      {/* Repeat popup */}
      {paywallType === 'repeat' && noOverlayScreenVisible && (
        <RepeatPopup
          isOpen={paywallType === 'repeat'}
          source={paywallSource}
          onClose={handleClose}
          onSuccess={paywallOnSuccess}
          onFailure={paywallOnFailure}
        />
      )}

      {children}
    </PaywallContext.Provider>
  );
};

export default PaywallProvider;
