import { useContext, useEffect, useMemo, useRef, useState } from 'react';

import { useTranslation } from 'next-i18next';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';

import useIpLocation from 'hooks/useIpLocation';
import useRouterDisclosure from 'hooks/useRouterDisclosure';
import useURLActionHandler from 'hooks/useURLActionHandler';

import { logAmplitudeEvent, logFbEvent } from 'lib/log-event';
import { getCookie } from 'lib/storage';
import { compareAppVersions, isInBuckets } from 'lib/utils';

import { AnimallLoaderContext } from 'components/AnimallLoader';

import AdFreePlanPopup from './components/AdFreePlanPopup';
import AppTrialToast from './components/AppTrialToast';
import BuyerTipsFlow from './components/BuyerTipsFlow';
import CWASnooze from './components/CWASnooze';
import ContactFeed from './components/ContactFeed';
import Feed from './components/Feed/Feed';
import Filter from './components/Filter';
import IncompletePost from './components/IncompletePost/IncompletePost';
import RatingPopup from './components/RatingPopup';
import SkippedLocationFlow from './components/SkippedLocationFlow';
import Tabs from './components/Tabs';
import Toast from './components/Toast/Toast';
import TopCTA from './components/TopCTA';
import VipBuyerPopup from './components/VipBuyerPopup';

import { useScrollPosition } from '@n8tb1t/use-scroll-position';
import useAppPaywall from 'modules/app-subscription/hooks/useAppPaywall';
import AppTrialPopup from 'modules/home/<USER>/components/AppTrialPopup';
import { FEATURES } from 'modules/home/<USER>/constant';
import BuyPageListingNudge from 'modules/sell/components/BuyPageListingNudge';
import PreferencesPopup from 'modules/vip-buyer/components/PreferencesPopup';
import VipBuyerPage from 'modules/vip-buyer/components/VipBuyerPage';
import { PREFERENCES_CONFIG } from 'modules/vip-buyer/constants/preferences-config';
import { parseCookies } from 'nookies';
import { useGrowthUser } from 'providers/GrowthUserProvider';
import { MonetizationContext } from 'providers/Monetization';

import { getTabItems } from './constants';

const Buy = ({
  postInUrl,
  userData,
  streakData,
  setIsScrolled = () => {},
  isScrolled = false,
}) => {
  const [showTipsDefaultValue, setShowTipsDefaultValue] = useState(false);
  const [showPreferencePopup, setShowPreferencePopup] = useState(false);
  const [showAdFreePopup, setShowAdFreePopup] = useState(false);

  const [showVipBuyerPopup, setShowVipBuyerPopup] = useState(false);
  const [vipBuyerPopupActionType, setVipBuyerPopupActionType] = useState(null);
  const [toggleLocationPopup, LocationPopup, , isLocationPopupOpen] =
    useRouterDisclosure({
      Modal: dynamic(() => import('./components/Location/LocationModal')),
      name: 'locationPopup',
    });
  const bucketId = getCookie('bucketId');

  const { isGrowthUser, isLoaded } = useGrowthUser();
  const { vipBuyer } = useContext(MonetizationContext);
  const { t } = useTranslation();
  const { appVersion } = parseCookies();
  const { openAnimallLoader } = useContext(AnimallLoaderContext);
  const isSubscriptionSupported = true; // Allow subscriptions on both web and app
  const [showAppTrialToast, setShowAppTrialToast] = useState(false);
  const { isPremiumUser } = useAppPaywall();
  const tabItems = useMemo(() => {
    const { bucketId, appVersion } = parseCookies();
    const isAppVersionSupported = compareAppVersions(appVersion, '>=', '3.1.6');
    const isIncontactedFeedBuckets = isInBuckets([80, 99], bucketId);
    const isContactedTabSupported =
      false &&
      isIncontactedFeedBuckets &&
      isAppVersionSupported &&
      isGrowthUser &&
      isLoaded;

    return getTabItems({
      t,
      bucketId,
      isVipBuyer: vipBuyer?.premiumUser,
      isGrowthUser,
      isContactedTabSupported,
    });
  }, [isGrowthUser, isLoaded, t, vipBuyer?.premiumUser]);

  const [showTabs, setShowTabs] = useState(false);

  useEffect(() => {
    if (tabItems.length > 1) {
      setShowTabs(true);
    } else if (showTabs) {
      setShowTabs(false);
    }
  }, [tabItems]);

  const [coordinates, setCooridnates] = useState({ lat: null, lng: null });
  const [isSortByDistance, setIsSortByDistance] = useState(false);
  const [activeTab, setActiveTab] = useState(tabItems[0].value);
  const [showAdFreePlanPopupGrowthUser, setShowAdFreePlanPopupGrowthUser] =
    useState(false);

  const [isToastVisible, setIsToastVisible] = useState(false);

  const filtertopRef = useRef(null);
  useScrollPosition(
    ({ currPos }) => {
      if (!isScrolled) {
        // Get the offset of the filter component, if the page is not scrolled past the threshold yet
        filtertopRef.current =
          document.getElementById('home-filter')?.offsetTop || 0;
      }
      const filterTop = filtertopRef.current;
      const threshold = -1 * (filterTop + 20);
      if (currPos.y < threshold) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    },
    [isScrolled],
  );

  useIpLocation();

  useURLActionHandler();

  useEffect(() => {
    logAmplitudeEvent('LANDED', 'HOMEM', 'HOME');
    logFbEvent('LANDED_HOMEM_HOME');
  }, []);

  const router = useRouter();

  const isNewVipBuyerExpUser = useMemo(
    () =>
      isGrowthUser &&
      isPremiumUser &&
      isInBuckets([40, 59], getCookie('bucketId')),
    [isGrowthUser, isPremiumUser],
  );

  const isEligibleForVipBuyerRef = useRef(false);

  useEffect(() => {
    isEligibleForVipBuyerRef.current =
      (!isGrowthUser && isSubscriptionSupported) || isNewVipBuyerExpUser;
  }, [isGrowthUser, isSubscriptionSupported, isNewVipBuyerExpUser]);

  const showVipBuyerPopupHandler = (actionType) => {
    if (!isEligibleForVipBuyerRef.current) {
      return;
    }
    openAnimallLoader();
    setVipBuyerPopupActionType(actionType);
    setShowVipBuyerPopup(true);
  };

  const handleAddfreePlan = () => {
    if (isEligibleForVipBuyerRef?.current) {
      showVipBuyerPopupHandler('AD');
      return;
    } else if (isGrowthUser && !isPremiumUser) {
      setShowAdFreePlanPopupGrowthUser(true);
      return;
    }
    setShowAdFreePopup(true);
  };

  useEffect(() => {
    // register the handler globally so it can be called from anywhere
    if (typeof window !== 'undefined') {
      window.showVipBuyerPopupHandler = showVipBuyerPopupHandler;
    }
  }, []);

  const vipBuyerTabItemsBeforeSwitch = (tabValue, tabSwitch) => {
    if (tabValue === 'vip-buyer') {
      const sessionData = sessionStorage.getItem('vip_buyer_pref');
      let openVipTab = true;
      if (sessionData) {
        // check if the user has already selected a preference
        const preferences = JSON.parse(sessionData);
        if (
          Object.keys(preferences).length <
          Object.keys(PREFERENCES_CONFIG).length
        ) {
          setShowPreferencePopup(true);
          openVipTab = false;
        }
      } else {
        if (isEligibleForVipBuyerRef?.current) {
          router.push('/buyer-plans?source=BUY_PAGE');
        } else {
          setShowPreferencePopup(true);
        }
        openVipTab = false;
      }

      if (openVipTab) {
        tabSwitch(tabValue, true);
      }
    } else {
      tabSwitch(tabValue, true);
    }
  };

  return (
    <div className="bg-surface-0 min-h-screen w-screen">
      {isGrowthUser && isInBuckets([30, 49], Number(bucketId)) && (
        <BuyPageListingNudge />
      )}

      {activeTab === 'all' && (
        <TopCTA userData={userData} className="pt-[76px]" />
      )}
      <Filter
        isScrolled={isScrolled}
        openLocationPopup={toggleLocationPopup}
        setIsSortByDistance={setIsSortByDistance}
        isSortByDistance={isSortByDistance}
        show={['all', 'contacts'].includes(activeTab)}
        setShowAdFreePopup={handleAddfreePlan}
        showFilterHistory={true}
        activeTab={activeTab}
      />
      {showTabs && (
        <Tabs
          isScrolled={isScrolled}
          tabItems={tabItems}
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          beforeTabChange={
            tabItems.some((el) => el.value === 'vip-buyer')
              ? vipBuyerTabItemsBeforeSwitch
              : null
          }
        />
      )}
      <SkippedLocationFlow
        enableTutorial
        page="BUY"
        onLocationFetch={(data) => {
          setCooridnates({ lat: data?.lat, lng: data?.lng });
        }}
      />
      {activeTab === 'all' && <IncompletePost />}
      <Feed
        isScrolled={isScrolled}
        postInUrl={postInUrl}
        forceUpdateCoords={coordinates}
        show={activeTab === 'all'}
        sortByDistance={isSortByDistance}
        setShowAdFreePopup={handleAddfreePlan}
        // key={feedKey}
      />
      <ContactFeed
        isScrolled={isScrolled}
        forceUpdateCoords={coordinates}
        show={activeTab === 'contacts'}
        sortByDistance={isSortByDistance}
        setShowAdFreePopup={handleAddfreePlan}
      />
      {isLoaded && isGrowthUser && !isPremiumUser ? (
        <AppTrialToast
          show={true}
          onClose={() => setShowAppTrialToast(false)}
          source="BUY_PAGE"
        />
      ) : (
        <Toast
          setIsToastVisible={setIsToastVisible}
          show={activeTab === 'all'}
        />
      )}

      <LocationPopup
        isOpen={isLocationPopupOpen}
        onClose={toggleLocationPopup}
        callbackFunc={(lat, lng, locationStr) => {
          logAmplitudeEvent('SUCCESS', 'LOCATION', 'GLOBAL', {
            PAGE: 'BUY_PAGE',
          });
          console.log('fetchFeedForNewCoords');
          console.log({ lat, lng });
          // setFeedKey((prev) => prev + 1); // completely re-renders feed component (performance issue)
          setCooridnates({ lat, lng });
        }}
      />
      {/* Buyer Tips Flow */}
      <BuyerTipsFlow
        showTipsDefaultValue={showTipsDefaultValue}
        setShowTipsDefaultValue={setShowTipsDefaultValue}
      />
      {/* CWA Snooze */}
      <CWASnooze />
      <RatingPopup />
      <AdFreePlanPopup show={showAdFreePopup} setShow={setShowAdFreePopup} />
      <VipBuyerPopup
        pageSource="BUY_PAGE"
        actionType={vipBuyerPopupActionType}
        show={showVipBuyerPopup}
        setShow={setShowVipBuyerPopup}
        isNewVipBuyerExpUser={isNewVipBuyerExpUser}
      />
      <AppTrialPopup
        show={showAdFreePlanPopupGrowthUser}
        setShow={setShowAdFreePlanPopupGrowthUser}
        pageSource={'MAIN_FEED'}
        featureName={FEATURES.AFTER_AD_FREE_PLAN}
      />
      <PreferencesPopup
        isOpen={showPreferencePopup}
        onClose={() => setShowPreferencePopup(false)}
      />
      <VipBuyerPage show={activeTab === 'vip-buyer'} />
    </div>
  );
};

export default Buy;
