import { useCallback, useContext, useEffect, useMemo, useState } from 'react';

import usePhonepe from 'hooks/usePhonepe';

import { logAmplitudeEvent, logFbEvent } from 'lib/log-event';
import { cn } from 'lib/utils';

import Image from 'components/UnoptimizedImage';

import { BorderedCloseIcon } from 'modules/buy/Icons';
import { MonetizationContext } from 'providers/Monetization';
import { useShowPaywall } from 'providers/PaywallProvider';

const priceText = {
  APP_SUBSCRIPTION_149: 'फिर ₹149/महिना',
  APP_REPEAT_SUBSCRIPTION_149: 'फिर ₹149/महिना',
  APP_SUBSCRIPTION_39: 'फिर ₹39/हफ़्ता',
};

const AppTrialToast = ({ show, onClose, source = 'NA' }) => {
  const [isOpen, setIsOpen] = useState(show);

  const monetizationStats = useContext(MonetizationContext);
  const plan = useMemo(
    () => monetizationStats?.app?.plans?.[0],
    [monetizationStats],
  );

  useEffect(() => {
    if (show) {
      setIsOpen(show);
    } else if (isOpen) {
      setIsOpen(false);
    }
  }, [show]);

  useEffect(() => {
    logAmplitudeEvent('LANDED', 'TRIALSUBSCRIPTIOIN', 'TOAST', {
      SOURCE: source,
    });
    logFbEvent('LANDED_TRIALSUBSCRIPTIOIN_TOAST');
  }, []);

  const { isPhonepeSupported } = usePhonepe();
  const trialPrice = isPhonepeSupported ? 2 : 1;
  const planPrice = !!plan?.extraInfo?.trialPlan ? trialPrice : plan?.price;

  const displayPrice = planPrice;

  const { showPaywall } = useShowPaywall();

  const onSubscription = useCallback(() => {
    showPaywall({ source: 'APP_TRIAL_TOAST_HOME_PAGE' });
  }, [showPaywall]);

  return (
    <div
      className={cn(
        'fixed inset-x-0 bottom-[67px] h-16 py-2.5 pl-3 pr-4 z-[2] shadow-all rounded-t-[10px] bg-primary-50',
        {
          'animate-fade-out-down': !isOpen,
          'animate-fade-in-up': isOpen,
        },
      )}
    >
      <div className="relative">
        <div className="absolute -right-2 -top-5 z-[2]" onClick={onClose}>
          <BorderedCloseIcon />
        </div>
        <div>
          <div className="flex items-center justify-between">
            <div className="flex gap-1.5 items-center justify-start">
              <Image
                src={
                  'https://static-assets.animall.in/static/images/Home_page/buy_pashu.png'
                }
                width={30}
                height={32}
                alt="buypashu"
              />
              <div>
                <p className="font-semibold text-base leading-5 mb-1 text-left">
                  <span className="text-primary-900">पशु ख़रीदें - बेचें</span>
                  <span className="text-primary-600 font-bold">
                    , अब ₹{displayPrice} में
                  </span>
                </p>
                <p className="text-left text-text-secondary font-medium text-xs leading-4">
                  {priceText?.[plan?.name] || `फिर ₹${plan?.price}/महिना`}
                </p>
              </div>
            </div>
            <div
              className="rounded bg-primary-600 text-white font-normal text-base leading-5 py-1 px-2"
              onClick={onSubscription}
            >
              प्लान लें
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AppTrialToast;
