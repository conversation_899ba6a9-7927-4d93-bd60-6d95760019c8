import { useMemo, useState } from 'react';

import { cn } from 'lib/utils';

import CancelAutoPayInfoPopup from 'components/AutoPay/CancelAutoPayInfoPopup';
import {
  H1,
  H2,
  H4,
  PrimaryBody,
  TertiaryBody,
} from 'components/ui/typography';

import { GoldenCalenderIcon } from 'modules/buy/Icons';
import { vipUserPrimePlanPageBenifits } from 'modules/classifieds-plans/enums';
import AddCartIcon from 'modules/prime/icons/AddCart';
import AnimallIcon from 'modules/prime/icons/AnimallIcon';
import CattleFaceIcon from 'modules/prime/icons/CattleFaceIcon';
import RupeeIcon from 'modules/prime/icons/RupeeIcon';

import VipBuyerBadge from './VipBuyerBadge';
import VipUserBadge from './VipUserBadge';
import { Tick } from './icons';

const infoByAction = {
  DailyCWA: [
    '3-5 हज़ार ज़्यादा मुनाफ़े वाले सौदे',
    'कोई ऐड नहीं',
    'फिर ₹199/महिना',
  ],
  MonhtlyCWA: [
    '3-5 हज़ार ज़्यादा मुनाफ़े वाले सौदे',
    'कोई ऐड नहीं',
    'फिर ₹199/महिना',
  ],
  CWA: ['3-5 हज़ार ज़्यादा मुनाफ़े वाले सौदे', 'कोई ऐड नहीं', 'फिर ₹199/महिना'],
  AD: [
    '3-5 हज़ार ज़्यादा मुनाफ़े वाले सौदे',
    'UNLIMITED कॉल करें',
    'फिर ₹199/महिना',
  ],
  RateChecker: [
    '3-5 हज़ार ज़्यादा मुनाफ़े वाले सौदे',
    'कोई ऐड नहीं',
    'फिर ₹199/महिना',
  ],
};

const titleByAction = {
  CWA: () => (
    <>
      <span className="text-primary-500">UNLIMITED</span> कॉल करें
    </>
  ),
  DailyCWA: () => (
    <>
      <span className="text-primary-500">UNLIMITED</span> कॉल करें
    </>
  ),
  MonthlyCWA: () => (
    <>
      <span className="text-primary-500">UNLIMITED</span> कॉल करें
    </>
  ),
  AD: () => (
    <>
      <span className="text-primary-500">कोई ऐड नहीं,</span> सिर्फ़ पशु
    </>
  ),
  RateChecker: () => (
    <>
      <span className="text-primary-500">कीमत जाने,</span> मुनाफे के सौदे करे
    </>
  ),
};

const PlanCard = ({ actionType, plan, planPrice, isEligibleForVipUser }) => {
  const [showAutoPayInfoPopup, setShowAutoPayInfoPopup] = useState(false);
  const info = useMemo(() => {
    if (isEligibleForVipUser) {
      return vipUserPrimePlanPageBenifits(plan);
    }
    let _info = infoByAction[actionType] || infoByAction.CWA;
    if (!plan?.extraInfo?.isSubscription) {
      _info = _info.slice(0, _info.length - 1);
    }
    return _info;
  }, [actionType, plan, isEligibleForVipUser]);

  const Title = useMemo(() => {
    return titleByAction[actionType] || titleByAction.CWA;
  }, [actionType]);
  const benefitIcons = [
    <AddCartIcon key={1} />,
    <CattleFaceIcon key={2} />,
    <AnimallIcon key={3} />,
    <RupeeIcon key={4} />,
  ];
  return (
    <div
      className={cn(
        'px-4 py-3 border-[3px] border-navActive w-full rounded-lg',
        isEligibleForVipUser && 'border-[#E2B65C]',
      )}
    >
      <div className="flex items-center justify-between">
        {/* vip buyer badge */}
        {isEligibleForVipUser ? (
          <VipUserBadge actionType={actionType} />
        ) : (
          <VipBuyerBadge />
        )}
        {/* Radio input */}
        <div className="flex items-center justify-center">
          <span
            className={cn(
              'w-5 h-5 rounded-full border-2  flex justify-center items-center border-primary-600',
              isEligibleForVipUser && 'border-[#DC9400]',
            )}
          >
            <span
              className={cn(
                'w-3 h-3 rounded-full bg-primary-600',
                isEligibleForVipUser && 'bg-[#DC9400]',
              )}
            ></span>
          </span>
          <input
            className="sr-only"
            type="radio"
            name="plan"
            checked={true}
            readOnly
          />
        </div>
      </div>
      <div className="flex items-end justify-between mt-3">
        <div>
          <div>
            {!isEligibleForVipUser && (
              <PrimaryBody className="bg-gradient-to-r from-[#D9FFFC] to-white p-2 rounded-md gap-1">
                <Title />
              </PrimaryBody>
            )}
          </div>
          <div className="mt-2">
            <div className="font-mukta font-normal text-sm text-vipPage-surface-2">
              साथ में:
            </div>
            {info.map((item, index) => (
              <div key={`${item}_${index}`} className="flex items-center gap-2">
                {isEligibleForVipUser ? (
                  <span className=" bg-[linear-gradient(102.08deg,_#E2B65C_24.15%,_#F8D851_61.31%,_#E2B65C_92.1%)] w-4 h-4 rounded-full flex justify-center items-center">
                    {benefitIcons[index]}
                  </span>
                ) : (
                  <Tick />
                )}
                <TertiaryBody className="text-text-primary font-mukta font-medium text-sm">
                  {item}
                </TertiaryBody>
              </div>
            ))}
          </div>
        </div>
        <div>
          <div className="flex gap-1 items-baseline">
            {!isEligibleForVipUser ? (
              <>
                <H1>₹{planPrice || 1}</H1>
                <H4>में</H4>
              </>
            ) : (
              <H2>₹{planPrice || 1}</H2>
            )}
          </div>
          <p className="text-text-secondary font-bold font-mukta text-xs text-right">
            {isEligibleForVipUser
              ? 'हर महीना'
              : plan?.extraInfo?.isSubscription
              ? '1 दिन का ट्रायल'
              : plan?.days + ' दिन के लिए'}
          </p>
        </div>
      </div>
      {plan?.extraInfo?.isSubscription && (
        <div className="flex justify-start items-center gap-1 px-2 bg-gradient-prime-surface py-0.5 rounded-md mt-2">
          <GoldenCalenderIcon />
          <TertiaryBody>
            हर महीने ऑटोपेमेंट। कभी भी{' '}
            <span
              className="font-bold text-primary-600"
              onClick={() => setShowAutoPayInfoPopup(true)}
            >
              कैंसल
            </span>{' '}
            करें
          </TertiaryBody>{' '}
        </div>
      )}

      <CancelAutoPayInfoPopup
        show={showAutoPayInfoPopup}
        setShow={setShowAutoPayInfoPopup}
      />
    </div>
  );
};

export default PlanCard;
