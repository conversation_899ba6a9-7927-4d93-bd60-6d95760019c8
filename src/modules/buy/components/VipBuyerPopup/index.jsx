import { useCallback, useContext, useEffect, useMemo } from 'react';

import usePayment from 'hooks/monetization/usePayment';
import useSubscription from 'hooks/monetization/useSubscription';
import useCustomerSupport from 'hooks/useCustomerSupport';
import usePhonepe from 'hooks/usePhonepe';

import { logAmplitudeEvent } from 'lib/log-event';
import { getCookie } from 'lib/storage';
import { cn, isInBuckets } from 'lib/utils';

import { AnimallLoaderContext } from 'components/AnimallLoader';
import useBottomSheet from 'components/Popups/hooks/useBottomSheet';
import Button from 'components/ui/Button';
import { SecondaryBody } from 'components/ui/typography';

import { parseCookies } from 'nookies';
import { useGrowthUser } from 'providers/GrowthUserProvider';
import { MonetizationContext } from 'providers/Monetization';
import { usePaymentConfirmation } from 'providers/PaymentConfirmationProvider';

import PlanCard from './PlanCard';

const popupTitleByAction = {
  DailyCWA: 'आपने आज की 4 फ्री कॉल पूरी कर ली हैं',
  MonthlyCWA: 'आपने इस महीने की 25 फ्री कॉल पूरी कर ली हैं',
  CWA: 'आपने इस महीने की 25 फ्री कॉल पूरी कर ली हैं',
  AD: 'अब AD नहीं, बिना रुकावट पशु देखे',
  RateChecker: 'उस्ताद से जाने पशु की सही कीमत',
};

const VipBuyerPopup = ({ show, setShow, pageSource, actionType = 'CWA' }) => {
  const supportPhoneNumber = useCustomerSupport();
  const { onToggle, isOpen, BottomSheet } = useBottomSheet('vip-buyer-plan');
  const { bucketId } = parseCookies();
  const isInVipUserBucket = isInBuckets([40, 59], bucketId);
  const isGrowthUser = useGrowthUser();
  const isEligibleForVipUser =
    isGrowthUser &&
    isInVipUserBucket &&
    ['DailyCWA', 'RateChecker'].includes(actionType);
  const { closeAnimallLoader } = useContext(AnimallLoaderContext);

  const togglePopup = () => {
    onToggle();
    setShow((prev) => !prev);
  };

  useEffect(() => {
    if (show) {
      closeAnimallLoader();
      onToggle();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [show]);

  useEffect(() => {
    if (!isOpen && show) {
      setShow(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  useEffect(() => {
    if (!isOpen) {
      return;
    }

    logAmplitudeEvent('VIEWED', 'VIPBUYER', 'POPUP', {
      PAGE: pageSource,
      ACTION_TYPE: actionType,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  const { vipBuyer, prime } = useContext(MonetizationContext);
  const plan = useMemo(() => {
    const { plans } = vipBuyer || {};
    const { vipUserPlan } = prime || {};
    if (isInVipUserBucket) {
      return vipUserPlan.filter(
        (plan) => plan?.name === 'APP_SUBSCRIPTION_399_VIP_USER',
      )[0];
    }
    if (!plans || plans.length === 0) return null;

    return plans.filter((p) => !!p.extraInfo?.trialPlan)?.[0];
  }, [vipBuyer, prime, isInVipUserBucket]);

  const { isPhonepeSupported } = usePhonepe();
  const trialPrice = isPhonepeSupported ? 2 : 1;
  const planPrice = !!plan?.extraInfo?.trialPlan
    ? trialPrice
    : plan?.discount?.price || plan?.price;

  const successCallback = useCallback(async () => {
    closeAnimallLoader();
    showSuccessPopup({
      amount: planPrice,
      onOk: () => {
        window.location.reload();
      },
      onClose: () => {
        window.location.reload();
      },
    });
    return;
  }, [closeAnimallLoader, planPrice, showSuccessPopup]);

  const failureCallback = useCallback(() => {
    closeAnimallLoader();
    showErrorPopup({
      onSupport: () => {
        logAmplitudeEvent('CLICKED', 'SUPPORT', 'PAYMENTFAILURE', {
          PLAN_TYPE: 'VIP_BUYER',
          SOURCE: pageSource,
        });
        window.open(`https://wa.me/91${supportPhoneNumber}`, '_blank');
      },
    });
  }, [closeAnimallLoader, pageSource, showErrorPopup, supportPhoneNumber]);

  const { showSuccessPopup, showErrorPopup } = usePaymentConfirmation();
  const { startSubscription } = useSubscription({
    source: pageSource,
    onSubscriptionSuccess: successCallback,
    onSubscriptionFailure: failureCallback,
  });

  const onPaymentClick = useCallback(() => {
    if (plan?.extraInfo?.isSubscription) {
      startSubscription(plan);
    } else {
      startPayment('ORDER', plan, {});
    }
  }, [plan, startPayment, startSubscription]);

  const title = useMemo(() => {
    return popupTitleByAction[actionType] || popupTitleByAction.CWA;
  }, [actionType]);

  const { startPayment } = usePayment({
    source: pageSource,
    onPaymentSuccess: successCallback,
    onPaymentFailure: failureCallback,
  });

  return (
    <BottomSheet
      isOpen={isOpen}
      onClose={togglePopup}
      className="py-6 px-4 bg-gradient-to-b from-[#FFE8E8] via-[#EEFEFC] to-white  rounded-t-[32px] shadow-lg"
    >
      <div className="flex flex-col items-center gap-6">
        {/* title */}
        <SecondaryBody className="font-medium font-mukta text-base text-text-primary">
          {title}
        </SecondaryBody>

        <PlanCard
          actionType={actionType}
          plan={plan}
          planPrice={planPrice}
          isEligibleForVipUser={isEligibleForVipUser}
        />

        <Button
          size="xl"
          textClassName={cn(
            'w-full flex justify-between items-center text-surface-3',
            isEligibleForVipUser &&
              'bg-[linear-gradient(102.08deg,_#E2B65C_24.15%,_#F8D851_61.31%,_#E2B65C_92.1%)] text-black',
          )}
          onClick={onPaymentClick}
          className={cn(
            'shine',
            isEligibleForVipUser &&
              'bg-[linear-gradient(102.08deg,_#E2B65C_24.15%,_#F8D851_61.31%,_#E2B65C_92.1%)] text-black',
          )}
        >
          <span>प्लान लें</span>
          <span>₹{planPrice}</span>
        </Button>
      </div>
    </BottomSheet>
  );
};

export default VipBuyerPopup;
