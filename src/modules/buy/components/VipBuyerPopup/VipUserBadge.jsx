import { TertiaryBody } from 'components/ui/typography';

import { GoldCrownIcon } from 'modules/home/<USER>';

const VipUserBadge = ({ actionType }) => {
  const getTitle = () =>
    actionType === 'RateChecker'
      ? 'कीमत जाने, मुनाफे के सौदे करे'
      : 'UNLIMITED कॉल करें';
  return (
    <div className="flex items-center gap-2 bg-gradient-to-r from-[#E2B65C] to-[#f9f4f4] rounded-[6px] py-[6px] px-2 w-fit">
      <GoldCrownIcon w={22} h={22} />
      <TertiaryBody className="font-bold text-xl font-mukta text-text-primary">
        {getTitle()}
      </TertiaryBody>
    </div>
  );
};
export default VipUserBadge;
