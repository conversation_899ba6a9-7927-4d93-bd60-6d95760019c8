import { useCallback, useContext, useMemo } from 'react';

import usePayment from 'hooks/monetization/usePayment';
import useSubscription from 'hooks/monetization/useSubscription';
import useCustomerSupport from 'hooks/useCustomerSupport';
import usePhonepe from 'hooks/usePhonepe';

import { logAmplitudeEvent } from 'lib/log-event';

import { AnimallLoaderContext } from 'components/AnimallLoader';
import Button from 'components/ui/Button';
import { H2, H3, SecondaryBody } from 'components/ui/typography';

import { MonetizationContext } from 'providers/Monetization';
import { usePaymentConfirmation } from 'providers/PaymentConfirmationProvider';

const VipBuyerBanner = ({ pageSource }) => {
  const { vipBuyer } = useContext(MonetizationContext);
  const { closeAnimallLoader } = useContext(AnimallLoaderContext);
  const supportPhoneNumber = useCustomerSupport();

  const plan = useMemo(() => {
    const { plans } = vipBuyer || {};

    if (!plans || plans.length === 0) return null;

    return plans.filter((p) => !!p.extraInfo?.trialPlan)?.[0] || null;
  }, [vipBuyer]);

  const { isPhonepeSupported } = usePhonepe();
  const trialPrice = isPhonepeSupported ? 2 : 1;
  const planPrice = !!plan?.extraInfo?.trialPlan
    ? trialPrice
    : plan?.discount?.price || plan?.price;

  const successCallback = useCallback(async () => {
    closeAnimallLoader();
    showSuccessPopup({
      amount: planPrice,
      onOk: () => {
        window.location.reload();
      },
      onClose: () => {
        window.location.reload();
      },
    });
    return;
  }, [closeAnimallLoader, planPrice, showSuccessPopup]);

  const failureCallback = useCallback(() => {
    closeAnimallLoader();
    showErrorPopup({
      onSupport: () => {
        logAmplitudeEvent('CLICKED', 'SUPPORT', 'PAYMENTFAILURE', {
          PLAN_TYPE: 'VIP_BUYER',
          SOURCE: pageSource,
        });
        window.open(`https://wa.me/91${supportPhoneNumber}`, '_blank');
      },
    });
  }, [closeAnimallLoader, pageSource, showErrorPopup, supportPhoneNumber]);

  const { showSuccessPopup, showErrorPopup } = usePaymentConfirmation();
  const { startSubscription, isEligibleForSubscription } = useSubscription({
    source: pageSource,
    onSubscriptionSuccess: successCallback,
    onSubscriptionFailure: failureCallback,
  });

  const { startPayment } = usePayment({
    source: pageSource,
    onPaymentSuccess: successCallback,
    onPaymentFailure: failureCallback,
  });

  const onPaymentClick = useCallback(() => {
    if (plan?.extraInfo?.isSubscription) {
      startSubscription(plan);
    } else {
      startPayment('ORDER', plan, {});
    }
  }, [plan, startPayment, startSubscription]);
  if (!plan || vipBuyer?.premiumUser) {
    return null;
  }
  return (
    <div className="bg-primary-25 border border-primary-100 rounded-lg p-4 my-2">
      <div>
        <H3 className="flex items-center justify-center text-center gap-1 text-text-primary">
          ये सिर्फ़ <H2 className="text-navActive">VIP खरीदारों</H2> के लिए है
        </H3>
        <SecondaryBody className="text-text-secondary font-medium text-base text-center">
          पहली कॉल, सही क़ीमत, खूँटे के पशु और
          <br /> जल्दी डील जैसे फ़ायदे पाने के लिए प्लान लें
        </SecondaryBody>
        <Button size="xl" className="mt-4" onClick={onPaymentClick}>
          प्लान लें सिर्फ़ ₹{planPrice} में
        </Button>

        <div className="text-center flex flex-col items-center mt-2">
          <SecondaryBody className="text-text-secondary font-medium font-mukta text-base">
            फिर ₹199/महिना
          </SecondaryBody>
          {/* <TertiaryBody className="text-xs font-mukta font-normal text-text-secondary">
            कभी भी <span className="font-bold">कैंसल</span> करें
          </TertiaryBody> */}
        </div>
      </div>
    </div>
  );
};

export default VipBuyerBanner;
