import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import InfiniteScroll from 'react-infinite-scroll-component';
import { SlideDown } from 'react-slidedown';
import 'react-slidedown/lib/slidedown.css';

import dynamic from 'next/dynamic';
import Image from 'next/image';
import { useRouter } from 'next/router';

import useAdMob from 'hooks/useAdmob';
import usePostActions from 'hooks/usePostActions';
import useRouterDisclosure from 'hooks/useRouterDisclosure';

import axios, { zaxios } from 'lib/axios';
import { logAmplitudeEvent } from 'lib/log-event';
import { getCookie } from 'lib/storage';
import { cn, compareAppVersions, isInBuckets } from 'lib/utils';

import { AnimallLoaderContext } from 'components/AnimallLoader';
import { LoaderGif } from 'components/Loader';
import PostCard from 'components/PostCard/PostCard';
import { PostCardActions } from 'components/PostCard/constants';
import CwaErrPopup from 'components/cwaBanners';

import { useToast } from '@chakra-ui/react';
import { debounce } from 'lodash';
import isNil from 'lodash/isNil';
import xor from 'lodash/xor';
import { AdMobAdType, AdMobAdapters, AdUnitId } from 'modules/ads/constants';
import useAppPaywall from 'modules/app-subscription/hooks/useAppPaywall';
import { FEED_EXPERIMENT_STATES } from 'modules/buy/constants';
import {
  getAnimalFamily,
  updateResourceAdsViewCounter,
} from 'modules/buy/helper';
import { parseCookies } from 'nookies';
import { useGrowthUser } from 'providers/GrowthUserProvider';
import { MonetizationContext } from 'providers/Monetization';

import Switch from '../Common/Switch';
import ShortlistedPostToast from '../Feed/ShortlistedPostToast';
import AnimalTypeSelection from '../Filter/AnimalTypeSelection';
import { ANIMAL_TYPE_FILTER_OPTIONS } from '../Filter/constants';
import EmptyFeedState from './Components/EmptyFeedState';
import PartialMatchesHeading from './Components/PartialMatchesHeading';
import PrimeFilterBanners from './Components/PrimeFilterBanners';
import VipBuyerBanner from './Components/VipBuyerBanner';
import {
  FEED_LIMIT,
  FEED_POSTS_CONTAINER,
  FILTERS,
  PRIME_FILTERED_LISTING_URL,
  PRIME_LISTING_URL,
  ZAP_FEED_URL,
  breedTypes,
  partialMatchFeedBackgroud,
} from './constants';
import { LockIcon } from './icons';
import { getFilterOptionTranslation, isFiltersEmpty } from './utils';

const initialFilterValues = {
  breed: null,
  milk: null,
  lactation: null,
  milkCapacity: null,
  price: null,
};
const FilteredFeedModal = ({
  isOpen,
  onClose,
  defaultAnimalType,
  defaultGender,
  animalTypeFilterVariant,
  setShowAdFreePopup,
  defaultFilterValues,
  activeTab,
}) => {
  const { accessToken, locationName } = parseCookies();
  const [posts, setPosts] = useState([]);
  const [primePosts, setPrimePosts] = useState([]);
  const [showVipBanner, setShowVipBanner] = useState(false);
  const [openedFilter, setOpenedFilter] = useState(null);
  const [isAnimalTypeActive, setIsAnimalTypeActive] = useState(false);
  const [isSortAscOrder, setIsSortAscOrder] = useState(false);
  const [animalType, setAnimalType] = useState(defaultAnimalType);
  const [gender, setGender] = useState(defaultGender);
  const [filterValues, setFilterValues] = useState(
    defaultFilterValues || initialFilterValues,
  );
  const partialMatchesPostsStartIdx = useRef(null);
  const nextToken = useRef(undefined);
  const [loading, setLoading] = useState(false);
  const headerEl = useRef(null);
  const [showFilters, setShowFilters] = useState(true);
  const isUserPrefSaved = useRef(false);
  const isScrollEventLogged = useRef(false);
  const partialFeedRef = useRef(null);
  const observerRef = useRef(null);
  const scrollContainerRef = useRef(null);
  const [shouldRender, setShouldRender] = useState(true);
  const { t } = useTranslation('common');
  const { openAnimallLoader, closeAnimallLoader } =
    useContext(AnimallLoaderContext);
  const { latitude, longitude, userState } = parseCookies();
  const { isGrowthUser } = useGrowthUser();
  const { isPremiumUser } = useAppPaywall();

  const { appVersion } = parseCookies();
  const isSubscriptionSupported = true; // Allow subscriptions on both web and app
  // const isX0X1Bucket = useMemo(() => {
  //   return isInBuckets([0, 19], bucketId);
  // }, [bucketId]);

  const isEligibleForVIPBuyerV2 = !isGrowthUser && isSubscriptionSupported;
  const { showInterstitial } = useAdMob();

  const router = useRouter();

  useEffect(() => {
    // If partial feed should not be shown, disconnect observer
    if ([null, undefined].includes(partialMatchesPostsStartIdx.current)) {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
      return;
    }

    // Only initialize the observer if it doesn't already exist
    if (!observerRef.current && partialFeedRef.current) {
      observerRef.current = new IntersectionObserver(
        (entries) => {
          const [entry] = entries;
          if (entry.isIntersecting) {
            observerRef.current.unobserve(partialFeedRef.current);

            // Log the event once the section is fully visible
            logAmplitudeEvent('VIEWSIMILAR', 'FILTER', 'HOME', {
              FILTER_CURRENT_MILK: filterValues.milk,
              FILTER_MILK_CAPACITY: filterValues.milkCapacity,
              FILTER_BREED: filterValues.breed,
              FILTER_PRICE: filterValues.price,
              FILTER_LACTATION: filterValues.lactation,
            });
          }
        },
        {
          threshold: 0.1,
        },
      );
      observerRef.current.observe(partialFeedRef.current);
    }

    // Cleanup function to disconnect observer on unmount
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [
    filterValues.breed,
    filterValues.lactation,
    filterValues.milk,
    filterValues.milkCapacity,
    filterValues.price,
    posts,
  ]);

  useEffect(() => {
    if (scrollContainerRef.current) {
      if (animalType === 'PRIME') {
        scrollContainerRef.current.style.marginTop = '0px';
      } else if (isEligibleForVIPBuyerV2) {
        scrollContainerRef.current.style.marginTop = '130px';
      } else {
        scrollContainerRef.current.style.marginTop = '102px';
      }
    }
  }, [animalType, isEligibleForVIPBuyerV2]);

  const { vipBuyer } = useContext(MonetizationContext);

  const isVipBuyer = useMemo(() => {
    return !!vipBuyer?.premiumUser;
  }, [vipBuyer]);

  const resetFeedState = () => {
    setPosts([]);
    nextToken.current = undefined;
    partialMatchesPostsStartIdx.current = null;
  };

  const animalFamily = useMemo(() => getAnimalFamily(animalType), [animalType]);

  const fetchFilteredPost = useCallback(
    async (
      fetchOnScroll = false,
      partialMatch = false,
      existingPostsLength = primePosts.length + posts.length,
    ) => {
      if (
        partialMatchesPostsStartIdx.current === 0 ||
        partialMatchesPostsStartIdx.current
      ) {
        partialMatch = true;
      }
      const categories = ['c2c_experiment', 'prime'];
      if (false && !isGrowthUser) {
        categories.push('expired');
      }

      const payload = {
        lat: latitude,
        long: longitude,
        count: FEED_LIMIT,
        offset: nextToken.current || 0,
        animalType: animalFamily,
        gender: gender,
        categories: categories,
        partialMatch: partialMatch,
        isDelayed: false,
      };

      if (activeTab === 'contacts') {
        payload.contactsOnly = true;
      } else {
        delete payload.contactsOnly;
      }

      if (!fetchOnScroll) {
        setLoading(true);
      }

      try {
        for (const filterName in filterValues) {
          if (![null, undefined].includes(filterValues[filterName])) {
            payload[filterName] = filterValues[filterName];
          }
        }

        if (['BACHIYA', 'PAADI'].includes(animalType)) {
          payload.isCalf = true;
        }

        const primePayload = {
          lat: latitude,
          lng: longitude,
        };

        if (isSortAscOrder) {
          payload.sortBy = 'distance:asc';
          primePayload.sortBy = 'distance:asc';
        }

        let apiCalls = [];
        if (animalType === 'PRIME') {
          primePayload.offset = nextToken.current || 0;
          primePayload.long = primePayload.lng;
          delete primePayload.lng;
          primePayload.limit = FEED_LIMIT;
          apiCalls = [
            axios.get(PRIME_FILTERED_LISTING_URL, { params: primePayload }),
          ];
        } else if (animalType === 'OTHER') {
          apiCalls = [
            zaxios.get(ZAP_FEED_URL, {
              params: payload,
            }),
          ];
        } else {
          primePayload.lastXHours = 24;
          primePayload.isFilterFeed = true;
          primePayload.animalType = animalFamily;
          apiCalls = [zaxios.get(ZAP_FEED_URL, { params: payload })];
          if (nextToken.current === undefined) {
            // fetch on first load, don't call on feed scroll
            apiCalls.push(
              axios.get(PRIME_LISTING_URL, { params: primePayload }),
            );
          }
        }
        const [mainPostsRes, additionalPrimePostsRes] = await Promise.all(
          apiCalls,
        );
        const mainPosts = mainPostsRes?.data?.data?.posts ?? [];
        const primePosts = additionalPrimePostsRes?.data?.data ?? [];
        const mainPostsOffsetToken = mainPostsRes?.data?.data?.nextOffset;

        if (partialMatch && mainPosts.length) {
          if (
            !partialMatchesPostsStartIdx.current &&
            partialMatchesPostsStartIdx.current !== 0
          )
            partialMatchesPostsStartIdx.current = existingPostsLength;
        } else {
          partialMatchesPostsStartIdx.current = null;
        }
        let updatedPostsLength = 0;
        if (nextToken.current || nextToken.current === 0) {
          setPosts((prevPosts) => {
            const updatedPosts = [...prevPosts, ...mainPosts];
            updatedPostsLength = primePosts.length + updatedPosts.length;
            return updatedPosts;
          });
        } else {
          updatedPostsLength = mainPosts.length + primePosts.length;
          setPosts(mainPosts);
          logAmplitudeEvent('UPDATE', 'FILTER', 'HOME', {
            ...getDefaultEventParams(),
            DISTANCE: 100,
            SOURCE: 'NORMAL',
            VALUE: 'NA',
          });
        }
        if (nextToken.current === undefined) {
          setPrimePosts(primePosts);
        }
        nextToken.current = mainPostsOffsetToken || null;
        if (animalType !== 'PRIME' && !partialMatch) {
          if (mainPostsOffsetToken) {
            if (mainPosts.length < 2) {
              nextToken.current = 0;
              fetchFilteredPost(fetchOnScroll, true, updatedPostsLength);
              return;
            }
          } else {
            nextToken.current = 0;
            fetchFilteredPost(fetchOnScroll, true, updatedPostsLength);
            return;
          }
        } else if (!mainPostsOffsetToken && !updatedPostsLength) {
          // animalType === 'PRIME' || partialMatch
          logAmplitudeEvent('NOPOST', 'FILTER', 'HOME', {
            ...getDefaultEventParams(),
            DISTANCE: 100,
            SOURCE: 'NORMAL',
          });
        }
      } catch (err) {
        console.log(err);
      } finally {
        setLoading(false);
      }
    },
    [
      latitude,
      longitude,
      animalFamily,
      gender,
      isSortAscOrder,
      animalType,
      filterValues,
      nextToken,
      isGrowthUser,
      activeTab,
    ],
  );

  const onChangeAnimalType = (_animalType, gender) => {
    if (animalType === _animalType) {
      setIsAnimalTypeActive(false);
      return;
    }
    setIsSortAscOrder(false);
    resetFeedState();
    setPrimePosts([]);
    setFilterValues(initialFilterValues);
    setOpenedFilter(null);
    setAnimalType(_animalType);
    setGender(gender);
    setIsAnimalTypeActive(false);
  };

  const handleScrollEnd = useMemo(
    // it work with useMemo only not with useCallback
    () => debounce(() => setShowFilters(true), 200),
    [],
  );

  const getDefaultEventParams = useCallback(() => {
    const { breed, milk, lactation, milkCapacity, price } = filterValues;
    return {
      POST_LOCATION: locationName,
      ANIMAL_TYPE: animalType,
      POSITION: 'CLEAN_FILTER_V2',
      BREED: breed || 'NA',
      MILK: milk || 'NA',
      LACTATION: lactation || 'NA',
      MILK_CAPACITY: milkCapacity || 'NA',
      PRICE: price || 'NA',
      TYPE: openedFilter || 'animalType',
      VARIANT: animalTypeFilterVariant || 'NORMAL',
    };
  }, [
    filterValues,
    locationName,
    animalType,
    openedFilter,
    animalTypeFilterVariant,
  ]);

  const onFeedScroll = (evnt) => {
    const screenHeight = window.innerHeight;
    const popupHeight = screenHeight * 0.8;
    const scrolledHeight = evnt.target.scrollTop;
    const scrollRemaining = screenHeight - popupHeight;
    if (scrollRemaining >= scrolledHeight) {
      if (!isUserPrefSaved.current) {
        isUserPrefSaved.current = true;
        saveUserFilterPreference(animalType, filterValues);
      }
    } else {
      if (!isScrollEventLogged.current) {
        isScrollEventLogged.current = true;
        logAmplitudeEvent('SCROLLED', 'FILTER', 'HOME', {
          ...getDefaultEventParams(),
          DISTANCE: 100,
          VARIANT: !openedFilter ? animalTypeFilterVariant : 'NORMAL',
        });
      }
    }
    if (
      scrolledHeight === 0 &&
      (animalType !== 'PRIME' || isEligibleForVIPBuyerV2)
    ) {
      const marginTop = isEligibleForVIPBuyerV2 ? '130px' : '102px';
      setTimeout(() => {
        evnt.target.style.marginTop = marginTop;
      }, 1000);
    } else {
      evnt.target.style.marginTop = '0px';
    }
    if (scrolledHeight > 50 && showFilters) {
      setShowFilters(false);
      setIsAnimalTypeActive(false);
      setOpenedFilter(null);
    }
    handleScrollEnd();
  };

  const closeFilterModal = () => {
    setShouldRender(false);
    setTimeout(() => {
      onClose();
      resetFeedState();
      saveUserFilterPreference(animalType, filterValues);
      setAnimalType(null);
      setIsAnimalTypeActive(false);
      setFilterValues(initialFilterValues);
      setOpenedFilter(null);
    }, 200);
  };

  useEffect(() => {
    if (!latitude || !longitude || !animalType) return;
    fetchFilteredPost(false, false, false);
  }, [fetchFilteredPost]);

  const animalImage = useMemo(() => {
    const _animalImage =
      !['PRIME', 'OTHER'].includes(animalType) &&
      ANIMAL_TYPE_FILTER_OPTIONS.find(
        (animalInfo) => animalInfo.animalType === animalType,
      )?.bgImage;

    return _animalImage;
  }, [animalType]);

  const filterType = useMemo(() => {
    return FILTERS[openedFilter]?.type || 'checkbox';
  }, [openedFilter]);

  const filterOptions = useMemo(() => {
    let _filterOptions = FILTERS[openedFilter]?.subTypes?.[animalFamily];
    const { milkCapacity } = filterValues;
    if (milkCapacity?.length && openedFilter === 'milk') {
      const maxValue = milkCapacity.slice(-1)[0].split('-')[0];
      _filterOptions = _filterOptions.filter(
        (option) => option.min <= maxValue,
      );
    }
    return _filterOptions;
  }, [openedFilter, filterValues, animalFamily]);

  const selectFilterOption = (event, value, type = 'checkbox') => {
    isUserPrefSaved.current = false;
    partialMatchesPostsStartIdx.current = null;
    nextToken.current = null;
    setPosts([]);
    event.preventDefault();
    let currentVal = filterValues[openedFilter];

    if (type === 'radio') {
      if (isNil(currentVal) || currentVal !== value) {
        currentVal = value;
      } else if (currentVal === value) {
        currentVal = null;
      } else {
        currentVal = value;
      }
    } else {
      if (isNil(currentVal)) {
        currentVal = [value];
      } else {
        currentVal = xor(currentVal, [value]);
      }
    }

    if (type !== 'radio') {
      currentVal = currentVal.sort(
        (val1, val2) =>
          filterOptions.findIndex((item) => item.value === val1) -
          filterOptions.findIndex((item) => item.value === val2),
      );
    }

    const newValues = { ...filterValues, [openedFilter]: currentVal };
    if (openedFilter === 'breed' && isSelectedMaleBreed(currentVal)) {
      // FOR OTHER PASHU
      newValues.milk = null;
    }
    setFilterValues(newValues);
  };

  const selectFilter = (filterName) => {
    if (isEligibleForVIPBuyerV2 && !isVipBuyer) {
      // return router.push('/buyer-plans?source=BUY_PAGE');
      // showing vip buyer banner
      setShowVipBanner(true);
    } else if (filterName === openedFilter) {
      setShowVipBanner(false);
      setOpenedFilter(null);
    } else {
      setShowVipBanner(false);
      setOpenedFilter(filterName);
      logAmplitudeEvent('OPEN', 'SUBFILTER', 'HOME', {
        ...getDefaultEventParams(),
        TYPE: filterName,
        SOURCE: 'NORMAL',
      });
    }
  };

  useEffect(() => {
    if (!animalType) return;

    const filterPreferences = {
      animalType,
      gender,
      ...filterValues,
    };

    if (isFiltersEmpty(filterValues)) return;

    localStorage.setItem(
      'filteredFeedPreferences',
      JSON.stringify(filterPreferences),
    );
  }, [animalType, filterValues, gender]);

  const saveUserFilterPreference = async (_animalType, _filterValues) => {
    if (!accessToken || !_animalType) return;
    const filterPreferences = {
      animalType: _animalType || null,
      ..._filterValues,
    };

    const userPreference = {
      type: 'SMART_FILTER',
      source: 'USER',
      value: JSON.stringify(filterPreferences),
    };

    try {
      await axios.post('/api/user/preference', userPreference, {
        headers: {
          'Content-Type': 'application/json',
        },
      });
    } catch (err) {
      console.error(
        'Error Saving User Preferences:',
        err?.response?.data?.error,
      );
    }
  };

  const isSelectedMaleBreed = useCallback(
    (breedValues) => {
      // FOR OTHER PASHU
      const breeds = breedValues || filterValues.breed;
      if (animalType === 'OTHER' && breeds) {
        const selectedMaleBreed = breeds.some(
          (breed) =>
            breedTypes['OTHER'].find((item) => item.value === breed)?.gender ===
            'MALE',
        );
        return selectedMaleBreed;
      }
      return false;
    },
    [filterValues, animalType],
  );

  const renderPosts = useCallback(() => {
    const _posts = [...primePosts, ...posts];
    let completeMatchPosts = _posts;
    let partialMatchPosts;

    const showPartialMatchFeed =
      partialMatchesPostsStartIdx.current ||
      partialMatchesPostsStartIdx.current === 0;

    if (showPartialMatchFeed) {
      completeMatchPosts = _posts.slice(0, partialMatchesPostsStartIdx.current);
      partialMatchPosts = _posts.slice(partialMatchesPostsStartIdx.current);
    }

    return (
      <>
        {/* Render Complete Match Posts */}
        {completeMatchPosts?.length ? (
          completeMatchPosts.map((post, index) => (
            <div
              key={`${post?.id} - ${index}`}
              id={`filtered_feed_post_${index}`}
            >
              <PostCard
                triggerAction={performAction}
                triggerToast={triggerToast}
                postIndex={index}
                post={post}
                className="!mx-1.5 my-3"
                source="FILTER_FEED"
                isVipBuyer={isVipBuyer}
              />
            </div>
          ))
        ) : !showPartialMatchFeed ? (
          <EmptyFeedState animalType={animalType} />
        ) : null}

        {/* Render Partial Match Posts */}
        {partialMatchPosts?.length && (
          <div
            className="mt-3"
            style={{ background: partialMatchFeedBackgroud }}
            ref={partialFeedRef}
          >
            <PartialMatchesHeading />
            {partialMatchPosts.map((post, index) => (
              <div
                key={`${post?.id} - ${index}`}
                id={`filtered_feed_post_${
                  partialMatchesPostsStartIdx.current + index
                }`}
              >
                <PostCard
                  triggerAction={performAction}
                  triggerToast={triggerToast}
                  postIndex={partialMatchesPostsStartIdx.current + index}
                  post={post}
                  className="!mx-1.5 my-3"
                  source="FILTER_FEED"
                />
              </div>
            ))}
          </div>
        )}
      </>
    );
  }, [primePosts, posts, animalType, performAction, triggerToast, isVipBuyer]);

  const showAdOnClosingResourceViewer = useCallback(
    (isResourceViewerOpen) => {
      console.log('showAdOnClosingResourceViewer', { isResourceViewerOpen });
      if (!isResourceViewerOpen) {
        console.log('Showing interstitial ad');
        function resourceAdCallback() {
          var updatedCount = updateResourceAdsViewCounter();
          if (
            // updatedCount % 3 === 0 &&
            // !getCookie('adFreePopupShown') &&
            !isGrowthUser
          ) {
            setShowAdFreePopup(true);
          }
        }
        showInterstitial({
          type: AdMobAdType.INTERSTITIAL,
          adUnitId: AdUnitId.RESOURCE_VIEWER_INTERSTITIAL,
          adapter: AdMobAdapters.NONE,
          extras: {},
          adCloseCallback: () => {
            console.log('closing ad');
            resourceAdCallback();
          },
          successCallback: () => {
            console.log('ad success');
          },
          failureCallback: () => {
            resourceAdCallback();
            console.log('ad failure');
          },
        });
      }
    },
    [showInterstitial, isGrowthUser, setShowAdFreePopup],
  );

  const [toggleReportPopup, ReportPopup, , isOpenReportPopup] =
    useRouterDisclosure({
      Modal: dynamic(() => import('components/PostCard/ReportPopup')),
      name: 'reportPopup',
    });

  const [toggleResourceViewer, ResourceViewer, , isOpenResourceViewer] =
    useRouterDisclosure({
      Modal: dynamic(() => import('components/PostCard/ResourceViewer')),
      name: 'resource-viewer',
      toggleCallback: showAdOnClosingResourceViewer,
    });

  const togglePopup = useCallback(
    (popup, props = {}) => {
      const popupMap = {
        [PostCardActions.REPORT]: toggleReportPopup,
        [PostCardActions.MEDIA]: toggleResourceViewer,
      };
      popupMap[popup]?.(props);
    },
    [toggleReportPopup, toggleResourceViewer],
  );

  const { triggerToast, toastProps } = useToast();
  const {
    performAction,
    currentPost,
    currentMediaIndex,
    resetData,
    showCwaError,
    setShowCwaError,
  } = usePostActions({
    openLoader: openAnimallLoader,
    closeLoader: closeAnimallLoader,
    t,
    togglePopup,
  });

  const getFilters = () => {
    if (!showFilters) return [];
    return Object.values(FILTERS)
      .filter((filter) => {
        // Only include filters applicable for the current animal type
        const isApplicable = filter?.includeFor?.includes(animalType);
        // Exclude 'milk' filter if selected breed is male
        if (filter.name === 'milk' && isSelectedMaleBreed()) {
          return false;
        }
        return isApplicable;
      })
      .filter((filter) => {
        // no conditional filter
        if (!filter.conditional) {
          return true;
        }

        // conditional flag filtering
        if (filter.conditionalFlag === 'isVipBuyer') {
          return isEligibleForVIPBuyerV2 ? true : isVipBuyer;
        }

        return false;
      });
  };

  const handleFilterSelect = (filter) => {
    if (showVipBanner) {
      setShowVipBanner(false);
    }
    selectFilter(filter.name);
  };

  const renderFilters = () => {
    const filteredFilters = getFilters();
    if (!filteredFilters?.length) return null;
    return (
      <SlideDown closed={false} className="ease-linear duration-[400]">
        <div className="grid grid-cols-3 gap-y-3 gap-x-2 mt-2 box-border">
          {filteredFilters.map((filter) => {
            const isRadioType = filter.type === 'radio';
            const valuesCnt = isRadioType
              ? ![null, undefined].includes(filterValues[filter.name])
                ? 1
                : 0
              : filterValues[filter.name]?.length;
            const isOpened = openedFilter === filter.name;
            const isActive = isOpened || !!valuesCnt;
            let lowestIdxValue = isRadioType
              ? filterValues[filter.name]
              : filterValues?.[filter.name]?.[0];

            // Handle special case for lactation filter
            if (
              filter.name === 'lactation' ||
              filter.conditionalFlag === 'isVipBuyer'
            ) {
              lowestIdxValue = FILTERS[filter.name]?.subTypes?.[
                animalType
              ]?.find((subType) => subType.value === lowestIdxValue)?.label;
            }

            return (
              <div
                key={filter.name}
                onClick={() => handleFilterSelect(filter)}
                className={cn(
                  'relative flex items-center justify-start w-full p-1 rounded-md border cursor-pointer',
                  isActive
                    ? 'bg-primary-600 border-primary-600 text-white'
                    : 'bg-white border-primary-600 text-[#2E3C4C]',
                )}
              >
                {/* Sub-Filter Count */}
                {valuesCnt > 1 && (
                  <div className="rounded-full bg-white p-[3px] absolute top-[-10px] right-[2px] z-10 border border-primary-600 text-[10px] leading-[10px] text-[#2e3c4c]">
                    +{valuesCnt - 1}
                  </div>
                )}

                {isEligibleForVIPBuyerV2 && !isVipBuyer && (
                  <div className="absolute -top-2.5 right-1 z-10 leading-[10px]">
                    <LockIcon />
                  </div>
                )}

                {/* Filter Icon */}
                <div className="mr-0.5 mb-1">
                  {isActive ? filter.icons['active'] : filter.icons['inactive']}
                </div>

                {/* Filter Label */}
                <div className="truncate leading-5">
                  {lowestIdxValue
                    ? getFilterOptionTranslation(
                        filter.name,
                        lowestIdxValue,
                        true,
                        t,
                      )
                    : t(`filterOptions.${filter.name}`)}
                </div>

                {/* Arrow Indicator */}
                <span
                  className={cn(
                    'border-x-transparent border-r-4 border-l-4 ml-auto mr-1 inline-flex align-middle',
                    isOpened
                      ? 'border-t-transparent border-t-0 border-b-4'
                      : 'border-b-transparent border-b-0 border-t-4',
                    isActive ? 'border-b-white' : 'border-t-neutral-500',
                  )}
                ></span>
              </div>
            );
          })}
        </div>
      </SlideDown>
    );
  };

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 z-30 h-screen bg-black/50 w-screen">
        <div
          className={cn(
            'w-full top-14 absolute inset-x-0 mx-auto duration-[400] rounded-t-2xl bg-trueGray-100',
            shouldRender ? 'animate-slide-up' : 'animate-slide-down',
          )}
        >
          <header
            ref={headerEl}
            className="bg-white relative p-4 pb-2 z-20 shadow rounded-t-2xl"
          >
            <div
              className="absolute right-2 -top-10 cursor-pointer"
              onClick={closeFilterModal}
            >
              <Image
                src="https://static-assets.animall.in/static/images/close-white.svg"
                width={32}
                height={32}
                alt="close"
              />
            </div>
            <div className="flex justify-between items-center h-10">
              <div className="flex gap-4 items-center">
                {animalImage && (
                  <div className="relative w-10 h-10 bg-primary-25 rounded">
                    <Image
                      src={animalImage}
                      layout="fill"
                      objectFit="cover"
                      alt="close"
                    />
                  </div>
                )}
                <span
                  className="font-semibold text-2xl text-neutral-900"
                  onClick={() => setIsAnimalTypeActive((val) => !val)}
                >
                  {t(`cattleNames.${animalType}`)}
                  <span
                    className={cn(
                      'border-x-transparent border-r-4 border-l-4 ml-1 inline-flex align-middle',
                      isAnimalTypeActive
                        ? 'border-b-primary-500 border-t-transparent border-t-0 border-b-4'
                        : 'border-t-primary-500 border-b-transparent border-b-0 border-t-4',
                    )}
                  ></span>
                </span>
              </div>
              <Switch
                label={t('nearbyCattle')}
                isChecked={isSortAscOrder}
                onChange={() => {
                  nextToken.current = null;
                  setPosts([]);
                  partialMatchesPostsStartIdx.current = null;
                  setIsSortAscOrder((val) => !val);
                }}
              />
            </div>
            {!isAnimalTypeActive && animalType !== 'PRIME' ? (
              <div className="w-full bg-white absolute top-16 -mt-0.5 -mx-4 px-4 pb-2">
                {renderFilters()}
                <SlideDown
                  className="ease-linear duration-300"
                  closed={!showVipBanner}
                >
                  <VipBuyerBanner />
                </SlideDown>
                {/* {showVipBanner && <VipBuyerBanner />} */}
                <SlideDown
                  closed={!filterOptions}
                  className="ease-linear duration-[400]"
                >
                  <div className="overflow-y-scroll max-h-56 transition-all">
                    <div
                      className={`${
                        openedFilter === 'lactation'
                          ? 'grid-cols-3'
                          : 'grid-cols-2'
                      } grid gap-x-3 gap-y-2 mt-4 overflow-scroll`}
                    >
                      {filterOptions?.map((subType, indx) => (
                        <label
                          className={cn(
                            'rounded-lg border-solid border box-border cursor-pointer font-medium text-sm leading-4 py-2 pr-3 pl-8 relative select-none truncate',
                            (filterType === 'radio' &&
                              filterValues[openedFilter] === subType.value) ||
                              (filterType === 'checkbox' &&
                                filterValues[openedFilter]?.includes(
                                  subType.value,
                                ))
                              ? 'bg-cyan-100 border-cyan-200 text-teal-600'
                              : 'bg-white border-neutral-200 text-trueGray-500',
                          )}
                          key={subType?.value}
                          onClick={(e) =>
                            selectFilterOption(e, subType.value, filterType)
                          }
                        >
                          <span
                            className={cn(
                              'rounded-sm border-solid border-2 h-4 w-4 absolute top-[7px] left-2.5',
                              (filterType === 'radio' &&
                                filterValues[openedFilter] === subType.value) ||
                                (filterType === 'checkbox' &&
                                  filterValues[openedFilter]?.includes(
                                    subType.value,
                                  ))
                                ? 'bg-teal-600 border-teal-600'
                                : 'bg-neutral-50 border-neutral-400',
                            )}
                          >
                            <span
                              className="border-solid border-t-0 border-l-0 border-r-2 border-b-2 h-2 absolute w-1 left-1 top-px border-cyan-100 transform rotate-45"
                              hidden={
                                (filterType === 'radio' &&
                                  filterValues[openedFilter] !==
                                    subType.value) ||
                                (filterType === 'checkbox' &&
                                  !filterValues[openedFilter]?.includes(
                                    subType.value,
                                  ))
                              }
                            ></span>
                          </span>
                          {getFilterOptionTranslation(
                            openedFilter,
                            subType.label,
                            false,
                            t,
                          )}
                          <input
                            type={filterType}
                            name={
                              filterType === 'radio'
                                ? openedFilter
                                : subType.label
                            }
                            value={subType.value}
                            className="peer hidden"
                            hidden
                          />
                        </label>
                      ))}
                    </div>
                  </div>
                </SlideDown>
              </div>
            ) : (
              <></>
            )}
            <div className="w-full bg-white absolute top-16 -mt-0.5 -mx-4 px-4 pb-2">
              <SlideDown
                closed={!isAnimalTypeActive}
                className="ease-linear duration-[400]"
              >
                <div className="mt-2">
                  <AnimalTypeSelection
                    type="SMALL"
                    onSelect={(option) =>
                      onChangeAnimalType(option.animalType, option.gender)
                    }
                    highlightCls="!bg-primary-600 !text-white"
                    selectedAnimalType={animalType}
                  />
                </div>
              </SlideDown>
            </div>
          </header>
          {loading ? (
            <div className="absolute block z-[15] left-0 top-0 bg-[#ffffffb3] w-full h-full rounded-t-2xl">
              <LoaderGif w={64} h={64} className="m-[70%auto]" />
            </div>
          ) : null}
          <div
            className={cn(
              'bg-trueGray-100 overflow-auto',
              showFilters && 'pb-24',
            )}
            style={{
              height: 'calc(100vh - 4rem - 56px)',
            }}
            id={FEED_POSTS_CONTAINER}
            ref={scrollContainerRef}
            onScroll={(evnt) => onFeedScroll(evnt)}
          >
            {animalType === 'PRIME' && <PrimeFilterBanners />}
            <InfiniteScroll
              className="overflow-hidden h-full"
              dataLength={primePosts?.length + posts?.length}
              next={() => fetchFilteredPost(true)}
              hasMore={nextToken.current !== null}
              loader={
                posts.length || primePosts.length ? (
                  <LoaderGif className="pb-10" />
                ) : null
              }
              scrollThreshold={0.8}
              scrollableTarget={FEED_POSTS_CONTAINER}
              endMessage={
                !loading && (posts.length || primePosts.length) ? (
                  <div className="pt-10 pb-28 text-center">
                    <b>{t('noMoreAnimals')}</b>
                  </div>
                ) : null
              }
            >
              {posts?.length || primePosts?.length ? (
                renderPosts()
              ) : !loading ? (
                <EmptyFeedState animalType={animalType} />
              ) : null}
            </InfiniteScroll>
          </div>
        </div>
      </div>

      <ShortlistedPostToast {...toastProps} />
      <ReportPopup
        isOpen={isOpenReportPopup}
        onClose={() => {
          toggleReportPopup();
          resetData();
        }}
        postId={currentPost?._id}
      />

      <ResourceViewer
        isOpen={isOpenResourceViewer}
        onClose={() => {
          toggleResourceViewer();
          resetData();
        }}
        post={currentPost}
        resourceIndex={currentMediaIndex}
        triggerAction={performAction}
        triggerToast={triggerToast}
      />

      {showCwaError && (
        <div>
          <CwaErrPopup
            error={showCwaError}
            pageName="FILTERED_FEED"
            postData={currentPost}
            setShowCwaError={setShowCwaError}
          />
        </div>
      )}
    </>
  );
};

export default FilteredFeedModal;
