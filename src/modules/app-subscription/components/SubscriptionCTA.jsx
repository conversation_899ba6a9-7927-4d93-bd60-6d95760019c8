import { useCallback, useContext } from 'react';

import { useTranslation } from 'next-i18next';

import useRazorpay from 'hooks/useRazorpay';

import axios from 'lib/axios';
import { logAmplitudeEvent, logFbEvent } from 'lib/log-event';
import { getCookie } from 'lib/storage';
import { cn } from 'lib/utils';

import { AnimallLoaderContext } from 'components/AnimallLoader';
import Button from 'components/ui/Button';
import { PrimaryBody, TertiaryBody } from 'components/ui/typography';

import { motion } from 'framer-motion';
import { MonetizationContext } from 'providers/Monetization';
import { usePaymentConfirmation } from 'providers/PaymentConfirmationProvider';
import { useShowPaywall } from 'providers/PaywallProvider';

import useAppPaywall, { PaywallStatus } from '../hooks/useAppPaywall';

const SubscriptionCTA = ({
  className = '',
  source,
  onAfterSuccess = () => {},
  onAfterFailure = () => {},
  selectedPlan = null,
  buttonText = null,
  isMultiPlan = false,
}) => {
  const { app, plan, isTrial: isTrialAvailable, status } = useAppPaywall();
  const { setShowLoaderScreen, setShowSuccessScreen } = useShowPaywall();

  // Use selectedPlan if provided (for multi-plan), otherwise use plan from hook
  const activePlan = selectedPlan || plan;

  const { refreshStats } = useContext(MonetizationContext);
  const { showErrorPopup } = usePaymentConfirmation();
  const { openAnimallLoader, closeAnimallLoader } =
    useContext(AnimallLoaderContext);
  const { initPayment } = useRazorpay();
  const { t: as } = useTranslation('app_subscription');

  // Calculate pricing for multi-plan mode
  const originalPrice = selectedPlan?.price;
  const currentPrice = selectedPlan?.discount?.price || selectedPlan?.price;
  const hasDiscount =
    isMultiPlan &&
    originalPrice &&
    currentPrice &&
    originalPrice > currentPrice;

  const successCallback = useCallback(
    async ({
      razorpaySubscriptionId,
      razorpayPaymentId,
      razorpaySignature,
    }) => {
      try {
        setShowLoaderScreen(true);
        await axios.post('/api/payment/result', {
          razorpaySubscriptionId,
          razorpayPaymentId,
          razorpaySignature,
        });

        logAmplitudeEvent('SUCCESS', 'PAYMENT', 'SUBSCRIPTION', {
          SOURCE: source,
          TRIAL: isTrialAvailable ? 'YES' : 'NO',
          PLAN: activePlan?.name,
          RESTART: status === PaywallStatus.HALTED ? 'YES' : 'NO',
        });
        logFbEvent('SUCCESS_PAYMENT_SUBSCRIPTION');

        // Hide loader and show success screen
        setShowLoaderScreen(false);
        setShowSuccessScreen(true);
        refreshStats({
          includePlans: true,
          source: source,
        });
        onAfterSuccess();
      } catch (error) {
        console.error('Error in success callback:', error);
        setShowLoaderScreen(false);
        showErrorPopup({
          text: as('planStartError'),
        });
      } finally {
        setShowLoaderScreen(false);
      }
    },
    [
      source,
      isTrialAvailable,
      activePlan,
      status,
      onAfterSuccess,
      refreshStats,
      showErrorPopup,
      setShowLoaderScreen,
      setShowSuccessScreen,
      as,
    ],
  );

  const failureCallback = useCallback(() => {
    logAmplitudeEvent('FAILURE', 'PAYMENT', 'SUBSCRIPTION', {
      SOURCE: source,
      TRIAL: isTrialAvailable ? 'YES' : 'NO',
      PLAN: activePlan?.name,
      RESTART: status === PaywallStatus.HALTED ? 'YES' : 'NO',
    });

    showErrorPopup({
      text: as('planStartError'),
    });
    onAfterFailure();
  }, [
    isTrialAvailable,
    onAfterFailure,
    activePlan,
    showErrorPopup,
    source,
    status,
    as,
  ]);

  const initSubscription = useCallback(async () => {
    try {
      openAnimallLoader();
      let rzpSubscription;
      if (status !== PaywallStatus.HALTED) {
        const { data } = await axios.post(
          '/api/monetization/subscription/init-subscription',
          {
            type: 'APP',
            planName: activePlan?.name,
            totalCount: 12,
            gateway: 'razorpay',
          },
        );

        rzpSubscription = data.data;
      } else {
        rzpSubscription = {
          id: app?.latestAppSubscription?.recurringSubscription?.id,
        };
      }

      logAmplitudeEvent('OPENING', 'RAZORPAY', 'PORTAL', {
        PLAN: activePlan?.name,
        RESTART: status === PaywallStatus.HALTED ? 'YES' : 'NO',
      });

      initPayment({
        subscriptionId: rzpSubscription.id,
        phone: getCookie('userPhone'),
        description: 'Animall Monthly Subscription',
        successCallback,
        failureCallback,
      });
    } catch (err) {
      console.error('Error initializing subscription:', err);
    } finally {
      closeAnimallLoader();
    }
  }, [
    status,
    closeAnimallLoader,
    failureCallback,
    initPayment,
    openAnimallLoader,
    activePlan,
    successCallback,
    app?.latestAppSubscription?.recurringSubscription?.id,
  ]);

  return (
    <div
      className={cn(
        'w-full flex flex-col items-center gap-3 bg-white pt-3 pb-4 px-4 rounded-t-lg border-t border-surface-0',
        className,
      )}
    >
      {/* Original trial/subscription text for non-multi-plan */}
      {!isMultiPlan &&
        isTrialAvailable &&
        status !== PaywallStatus.NO_TRIAL && (
          <TertiaryBody className="text-text-secondary">
            {as('startTrialCancelAnytime')}
          </TertiaryBody>
        )}
      {!isMultiPlan && status === PaywallStatus.NO_TRIAL && (
        <TertiaryBody className="text-text-secondary">
          {as('buyTicketToBenefitFromAnimall')}
        </TertiaryBody>
      )}

      {/* Updated button with pricing inside for multi-plan, regular button for single plan */}
      {isMultiPlan ? (
        <motion.button
          whileTap={{ scale: 0.98 }}
          onClick={initSubscription}
          className="w-full flex justify-between items-center px-4 py-4 gap-2.5 bg-[#14776F] hover:bg-[#0f5e57] rounded-md transition-colors"
          style={{
            background: '#14776F',
            borderRadius: '6px',
            padding: '16px',
            gap: '10px',
          }}
        >
          <span
            className="text-[#F4FFFE]"
            style={{
              fontWeight: 700,
              fontSize: '18px',
              lineHeight: '120%',
              color: '#F4FFFE',
            }}
          >
            {buttonText || as('buyTicket')}
          </span>
          <div className="flex items-center gap-2">
            {hasDiscount && (
              <PrimaryBody className="text-white/60 line-through">
                ₹{originalPrice}
              </PrimaryBody>
            )}
            <span className="text-white text-lg font-bold">
              ₹{currentPrice}
            </span>
          </div>
        </motion.button>
      ) : (
        <Button onClick={initSubscription} size="xl">
          {buttonText ||
            (status === PaywallStatus.NO_TRIAL
              ? as('buyTicket')
              : isTrialAvailable
              ? as('startTrial')
              : as('startAgain'))}
        </Button>
      )}
    </div>
  );
};

export default SubscriptionCTA;
