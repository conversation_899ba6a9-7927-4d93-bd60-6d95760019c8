import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

import usePhonepe from 'hooks/usePhonepe';

import { cn } from 'lib/utils';

import Button from 'components/ui/Button';
import { H4, SecondaryBody } from 'components/ui/typography';

import { useShowPaywall } from 'providers/PaywallProvider';

import useAppPaywall, { PaywallStatus } from '../hooks/useAppPaywall';

const RupeeIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10 16.25C13.4518 16.25 16.25 13.4518 16.25 10C16.25 6.54822 13.4518 3.75 10 3.75C6.54822 3.75 3.75 6.54822 3.75 10C3.75 13.4518 6.54822 16.25 10 16.25ZM8.2125 8.96875V9.61625H10.155C10.1258 9.80292 10.0208 9.96042 9.84 10.0887C9.665 10.2171 9.42 10.2812 9.105 10.2812C9 10.2812 8.90958 10.2754 8.83375 10.2638C8.76375 10.2463 8.68792 10.2229 8.60625 10.1937L8.34375 10.7625C8.41375 10.8325 8.48958 10.9113 8.57125 10.9987C8.64125 11.0746 8.72 11.165 8.8075 11.27C8.895 11.3692 8.98833 11.48 9.0875 11.6025L10.3563 13.2125L11.1263 12.6437L9.62125 10.9725C9.81958 10.9608 10.0092 10.92 10.19 10.85C10.3767 10.7742 10.5371 10.675 10.6713 10.5525C10.8113 10.43 10.9221 10.29 11.0037 10.1325C11.0854 9.975 11.1292 9.80292 11.135 9.61625H11.8875V8.96875H11.0563C11.0212 8.80542 10.9717 8.67125 10.9075 8.56625C10.8433 8.45542 10.785 8.365 10.7325 8.295H11.975V7.56H8.125V8.295H9.805C9.875 8.37667 9.93917 8.47583 9.9975 8.5925C10.0617 8.70333 10.1083 8.82875 10.1375 8.96875H8.2125Z"
      fill="#A0A6AE"
    />
  </svg>
);

const RightChevron = ({ className = '' }) => (
  <svg
    width="16"
    height="17"
    viewBox="0 0 16 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      d="M9.69067 8.85384L6.86225 6.02538C6.6019 5.76503 6.60191 5.34293 6.86226 5.08258C7.1226 4.82223 7.54471 4.82223 7.80506 5.08258L11.1049 8.38244C11.3653 8.64279 11.3653 9.0649 11.1049 9.32525L7.80506 12.6251C7.54471 12.8854 7.12261 12.8854 6.86226 12.6251C6.60191 12.3647 6.60191 11.9426 6.86226 11.6822L9.69067 8.85384Z"
      fill="#828A95"
    />
  </svg>
);

const ProfilePageEntry = ({ className = '' }) => {
  const { app, status, isEligible, plan, plans } = useAppPaywall();
  const { showPaywall } = useShowPaywall();
  const { isPhonepeSupported } = usePhonepe();

  const router = useRouter();

  const { t: as } = useTranslation('app_subscription');
  const multiPlan = plans && plans.length > 1;

  const getDurationText = (duration) => {
    return parseInt(duration) === 30
      ? as('oneMonthTicket')
      : as('dayTicketText', { duration });
  };

  const redirectToMyPlan = () => {
    let suffix = '';
    switch (status) {
      case PaywallStatus.TRIAL:
        suffix = '&status=trial';
        break;
      case PaywallStatus.NO_TRIAL:
        suffix = '&status=no-trial';
        break;
      case PaywallStatus.TRIAL_RUNNING:
        suffix = '&status=trial-running';
        break;
      default:
        suffix = '&status=plan-active';
    }
    router.push(`/myplan?source=profile${suffix}`);
  };

  if (!isEligible || [PaywallStatus.CANCELLED].includes(status)) return null;
  const trialPrice = isPhonepeSupported ? 2 : 1;
  // For PhonePe users, show ₹0 in UI but keep actual logic with trialPrice
  const displayPrice = trialPrice;

  if (status === PaywallStatus.TRIAL) {
    return (
      <>
        <div
          className={cn(
            'rounded border border-surface-0 px-4 py-3 flex items-center justify-between gap-2 bg-primary-50',
            className,
          )}
        >
          <H4>
            ₹{displayPrice} {as('in')} {as('ofApp')} {as('trial')}
          </H4>
          <Button size="sm" onClick={() => showPaywall({ source: 'PROFILE' })}>
            {as('doStart')}
          </Button>
        </div>
      </>
    );
  }

  if (status === PaywallStatus.NO_TRIAL) {
    return (
      <>
        <div
          className={cn(
            'rounded border border-surface-0 px-4 py-3 flex items-center justify-between gap-2 bg-primary-50',
            className,
          )}
        >
          <H4>
            {multiPlan
              ? as('multiPlanText')
              : `₹${plan?.price || 49} ${as('only')} ${as('oneMonthTicket')}`}
          </H4>
          <Button size="sm" onClick={() => showPaywall({ source: 'PROFILE' })}>
            {as('buyTicket')}
          </Button>
        </div>
      </>
    );
  }

  return (
    <div className="flex flex-col flex-1">
      <SecondaryBody className="px-1 mt-6 md:mt-0 md:mb-4">
        {as('myPlan')}
      </SecondaryBody>
      <div
        onClick={redirectToMyPlan}
        className={cn(
          'rounded-lg border px-2 py-2.5 flex items-center gap-0.5',
          status === PaywallStatus.AWAIT_CANCELLATION
            ? 'bg-gradient-light-red border-red-200'
            : 'bg-gradient-light-green border-primary-50',
        )}
      >
        <RupeeIcon />
        <SecondaryBody>
          {status === PaywallStatus.TRIAL_RUNNING
            ? as('daysTrial', {
                days: 1,
              })
            : as(plan?.days === 365 ? 'rsPerYear' : 'rsPerMonth', {
                amount: plan?.price || 49,
              })}
        </SecondaryBody>
        <RightChevron className="ml-auto" />
      </div>
    </div>
  );
};

export default ProfilePageEntry;
