import { useCallback, useContext, useMemo, useState } from 'react';

import { useRouter } from 'next/router';

import useSubscription from 'hooks/monetization/useSubscription';

import { AnimallLoaderContext } from 'components/AnimallLoader';
import Button from 'components/ui/Button';
import { H3, TertiaryBody } from 'components/ui/typography';

import useDynamicPricing from 'modules/silver-plan/hooks/useDynamicPricing';
import { MonetizationContext } from 'providers/Monetization';
import { usePaymentConfirmation } from 'providers/PaymentConfirmationProvider';

import { ExclamationIcon, GoldenCrownIcon } from './icons';

const VipBuyerNudge = ({ buyersCount = 200 }) => {
  const router = useRouter();
  const [isSubscriptionInProgress, setIsSubscriptionInProgress] =
    useState(false);
  const { closeAnimallLoader } = useContext(AnimallLoaderContext);

  const { vipBuyer } = useContext(MonetizationContext);
  const { visible } = useDynamicPricing();
  const plan = useMemo(() => {
    const { plans } = vipBuyer || {};

    if (!plans || plans.length === 0) return null;

    return plans.filter((p) => !!p.extraInfo?.trialPlan)?.[0] || null;
  }, [vipBuyer]);

  const { showSuccessPopup, showErrorPopup } = usePaymentConfirmation();

  const { startSubscription } = useSubscription({
    source: 'HOME',
    onSubscriptionSuccess: () => {
      isSubscriptionInProgress(false);
      closeAnimallLoader();
      showSuccessPopup({
        text: 'आप VIP ख़रीदार बन गए हैं।',
        amount: 1,
        onClose: () => {
          router.reload();
        },
        onOk: () => {
          router.reload();
        },
      });
    },
    onSubscriptionFailure: () => {
      setIsSubscriptionInProgress(false);
      closeAnimallLoader();
      showErrorPopup();
    },
  });

  const onSubscription = useCallback(() => {
    setIsSubscriptionInProgress(true);
    startSubscription(plan);
  }, [plan, startSubscription]);

  if (visible) return null;

  return (
    <div
      className="sm:mx-auto sm:max-w-md  mx-4 flex flex-col mb-4 p-3 rounded-xl bg-white shadow-sm border-[1px] border-primary-600"
      onClick={onSubscription}
    >
      <div className="flex gap-4 w-full items-center justify-between  whitespace-nowrap">
        <div className="p-0.5 rounded-2xl bg-[linear-gradient(96.48deg,#F8D851_3.88%,#E7B012_21.19%,#E2B65C_57.33%,#E6AA3B_97.72%)] w-fit">
          <div className="bg-gradient-prime-golden-border rounded-[15px] flex items-center gap-[6px]  px-1 py-0.5">
            <GoldenCrownIcon />
            <TertiaryBody className="text-text-primary font-bold w-fit ">
              VIP ख़रीदार
            </TertiaryBody>
            <div className=" bg-white rounded-full flex items-center justify-center  gap-1 py-0.5 px-0.5 whitespace-nowrap shadow-md">
              <ExclamationIcon />
              <p className="text-status-rejected-text font-mukta text-xs font-bold">
                नहीं बने
              </p>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-1">
          <div className="text-[10px] text-white font-rajdhani bg-accent-success px-1 rounded-sm">
            LIVE
          </div>
          <TertiaryBody>
            <span>{buyersCount} पशु</span> बिकाऊ हैं
          </TertiaryBody>
        </div>
      </div>
      <div className="mt-2 flex justify-between items-center w-full">
        <H3 className=" font-bold font-rajdhani text-2xl">
          ₹199 वाली सुविधा,<span className="text-primary-600">₹1 में</span>
        </H3>

        <Button
          size="xl"
          className="w-fit h-[46px] flex justify-between items-center shine"
          textClassName="flex items-center justify-between flex-grow text-lg font-bold"
          disabled={isSubscriptionInProgress}
        >
          <div>प्लान लें</div>
        </Button>
      </div>
    </div>
  );
};

export default VipBuyerNudge;
