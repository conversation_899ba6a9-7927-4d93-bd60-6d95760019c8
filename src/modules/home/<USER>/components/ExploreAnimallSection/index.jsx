import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

import { isInBuckets } from 'lib/utils';

import ChevronRightIcon from 'components/icons/ChevronRight';
import { PrimaryBody } from 'components/ui/typography';

import { DotLottieReact } from '@lottiefiles/dotlottie-react';
import { parseCookies } from 'nookies';
import { useGrowthUser } from 'providers/GrowthUserProvider';

import { FEATURES } from '../../constant';
import {
  IncreaseMilkIcon,
  PashuUstadIcon,
  RatePredictorIcon,
} from '../../icons/exploreAnimallIcons';
import ProtectedSection from '../ProtectedSection';

export const exploreAnimallSection = [
  {
    feature: FEATURES.RATE_PREDICTOR,
    title: 'predictRate',
    subtitle: 'rightRate',
    icon: <RatePredictorIcon />,
    navigateToUrl: '/chat/rate-predictor',
    imgCls: 'w-[80px] h-[63px]',
  },
  {
    feature: FEATURES.DIET_PLANNER,
    title: 'IncreaseMilk',
    subtitle: 'makeDiet',
    icon: (
      <DotLottieReact
        src="https://lottie.host/4abf96ee-eb2e-4d65-9a8a-b939b9b0cdf2/T92iwx5pzZ.lottie"
        autoplay
        loop
        style={{
          height: '43px',
          width: '93px',
        }}
      />
    ),
    navigateToUrl: '/chat/diet-planner',
    imgCls: 'w-[62px] h-[63px]',
  },
];
const ExploreAnimallSection = () => {
  const { bucketId } = parseCookies();
  const { isGrowthUser, isLoaded } = useGrowthUser();
  const router = useRouter();
  const { t } = useTranslation('landingPage');

  const navigationHandler = (url) => {
    router.push(url + '?source=HOME_PAGE');
  };
  return (
    <div className="flex gap-3 mx-4 mb-5 justify-center sm:max-w-md sm:mx-auto sm:justify-between">
      {exploreAnimallSection.map((card, index) => (
        <ProtectedSection
          key={card.title}
          className="flex-1"
          featureName={card.feature}
          shouldAuthenticate={isLoaded && isGrowthUser}
        >
          <div
            key={card.title}
            className="flex flex-col flex-1 items-center h-fit py-4 border-4 border-white rounded-2xl shadow-[0px_0px_6px_0px_#00000026] bg-gradient-to-b from-primary-50 to-primary-200 cursor-pointer"
            onClick={() => navigationHandler(card.navigateToUrl)}
          >
            <PrimaryBody className="text-primary-600 flex items-center gap-2">
              {t(card.title)}
              <ChevronRightIcon w={7} h={12} fill="#14776F" />
            </PrimaryBody>
            <p className="font-rajdhani text-primary-600 font-semibold text-sm">
              {t(card.subtitle)}
            </p>
            {card.icon}
          </div>
        </ProtectedSection>
      ))}
    </div>
  );
};

export default ExploreAnimallSection;
