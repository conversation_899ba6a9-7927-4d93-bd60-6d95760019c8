import React from 'react';

import { SecondaryBody } from 'components/ui/typography';

import InsightCards from './InsightCards';

export default function InsightSection({ insightsData }) {
  return (
    <>
      <SecondaryBody className="text-lg font-semibold mb-3 mx-4 sm:max-w-md sm:mx-auto">
        {insightsData?.user?.district} में भारी मांग
      </SecondaryBody>
      <div className="box-border w-full mb-5 overflow-scroll flex gap-2 items-start flex-nowrap sm:max-w-md sm:mx-auto px-2 sm:px-0 bg-surface-0">
        <InsightCards insightsData={insightsData} />
      </div>
    </>
  );
}
