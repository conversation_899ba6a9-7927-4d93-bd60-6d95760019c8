import React, { useMemo, useRef, useState } from 'react';
import Slider from 'react-slick';

import { i18n } from 'next-i18next';
import { useRouter } from 'next/router';

import { cn } from 'lib/utils';

import Image from 'components/UnoptimizedImage';
import { PrimaryBody } from 'components/ui/typography';

import { isNil } from 'lodash';
import { CWAPhoneIcon, CWAWAIcon } from 'modules/user/components/icons';
import 'slick-carousel/slick/slick-theme.css';
import 'slick-carousel/slick/slick.css';

import { ChevronRightIcon } from '../../icons/BuySellSection';
import {
  FireIcon,
  LocationIcon,
  TopBuyerAndSellerIcon,
  TopPashuIcon,
} from '../../icons/insights';
import Resource from '../Resource';
import { formatIndian, getCattleType, getCattleTypeImage } from './data';

export default function InsightCards({ insightsData }) {
  const { topBreeds, topPricedPosts, user } = insightsData || {};
  const router = useRouter();
  const settings = {
    dots: false,
    infinite: true,
    speed: 300,
    slidesToShow: 3,
    slidesToScroll: 1,
    swipeToSlide: true,
    touchMove: true,
    autoplay: true, // Disabled for better UX
    arrows: true,
    dotsClass: 'slick-dots custom-dots', // Custom class for styling
    beforeChange: (_, next) => setActive(next),
    responsive: [
      {
        breakpoint: 4000, // Extra large screens
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 1200, // Large screens/tablets
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 1024, // Medium tablets
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
        },
      },
      {
        breakpoint: 768, // Small tablets
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          // centerMode: true,
          centerPadding: '60px',
        },
      },
      {
        breakpoint: 640, // Large mobile
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          // centerMode: true,
          centerPadding: '40px',
        },
      },
      {
        breakpoint: 480, // Small mobile
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          // centerMode: true,
          centerPadding: '20px',
        },
      },
    ],
  };

  const sliderRef = useRef(null);
  const [active, setActive] = useState(0); // leftmost index
  const InsightCardLayout = ({ icon, children, title, onClick = () => {} }) => (
    <div className="px-2 h-[181px]">
      <div
        className="bg-surface-3 rounded-xl p-3 h-full cursor-pointer"
        onClick={onClick}
      >
        <div className="flex gap-1 items-center justify-between mb-3">
          <div className="flex gap-1 items-center">
            {icon}
            <PrimaryBody className="text-status-moderation-text truncate max-w-[210px]">
              {title}
            </PrimaryBody>
          </div>
          <p className="flex items-center justify-center gap-1.5 font-medium text-base text-primary-600 cursor-pointer">
            <span>और जानें</span>
            <span>
              <ChevronRightIcon w={7} h={12} fill="#14776F" />
            </span>
          </p>
        </div>
        {children}
      </div>
    </div>
  );

  function RoleStat({ isBuyer, item }) {
    const left = isBuyer ? 'खरीदार' : 'पशु पालक';
    const right = isBuyer ? 'काल किए' : 'पशु बेचें';
    const val = (isBuyer ? item?.count : item?.totalListings) ?? '0';
    const name = (isBuyer ? item?.name : item?.seller) || 'Unknown User';
    return (
      <>
        <p className=" font-bold text-base text-grey-400 truncate max-w-[140px]">
          {name}
        </p>
        <p className="text-xs font-normal text-grey-400 flex items-center">
          {left}
          <span className="inline-block h-4 mx-2 border-l border-grey-100 align-middle" />
          {right} :<span className=" font-bold ml-1">{val}</span>
        </p>
      </>
    );
  }

  // Card data array for cleaner code
  const cardData = useMemo(() => {
    const topSeller = insightsData?.topSellers?.[0] || {};
    const topBuyer = insightsData?.topBuyers?.[0] || {};

    const isTopBreedsAvailable = topBreeds && topBreeds?.length > 0;
    const topBreedsData = isTopBreedsAvailable ? topBreeds[0] : {};

    const isTopPricedPostsAvailable =
      topPricedPosts?.posts && topPricedPosts?.posts?.length > 0;
    const topPriceStats = isTopPricedPostsAvailable
      ? topPricedPosts?.stats
      : {};

    const breed =
      topBreedsData?.breed && !isNil(topBreedsData?.breed)
        ? i18n
            .t(`common:breedMap.${topBreedsData?.breed}`)
            .replace('होल्सटीन फ्रीसिएन (HF)', 'HF')
        : null;

    const cattleType = getCattleType(topBreedsData?.breed) || 'गाय';
    const genericImage = getCattleTypeImage(topBreedsData?.breed);

    const posts = topPricedPosts?.posts ?? [];
    const shown = posts.slice(0, 4);
    const fillCount = Math.max(4 - posts.length, 0);

    return [
      {
        id: 'favorite',
        title: `सबकी पसंद ${breed + ' ' + cattleType || ''}`,
        icon: <FireIcon />,
        onClick: () => router.push('/insights?section=favoriteCattle'),
        content: (
          <>
            <div className="flex gap-3 items-center">
              <Resource
                post={{
                  resources: [
                    {
                      isValid: false,
                      isVideo: false,
                      tag: 'image',
                      url: topBreedsData?.image || genericImage,
                    },
                  ],
                }}
                className="h-[78px] w-[82px]"
              />
              <div className="flex flex-col items-start gap-2">
                <p className="font-bold text-base">
                  {formatIndian(topBreedsData?.priceMin) || '0'} -{' '}
                  {formatIndian(topBreedsData?.priceMax) || '0'}
                </p>
                <p className="font-normal text-sm">
                  {topBreedsData?.highestMilkMin || '0'}L -{' '}
                  {topBreedsData?.highestMilkMax || '0'}L दूध क्षमता
                </p>
                <p className="font-normal text-sm flex items-center gap-1">
                  <LocationIcon /> {user?.district}, {user?.location_state}
                </p>
              </div>
            </div>
            <div className="mt-2 flex items-center border-t-[1px] border-surface-1 pt-2">
              <Image
                src="https://static-assets.animall.in/static/images/Group_of_people.png"
                alt="Paravet Service"
                layout="fixed"
                width={48}
                height={20}
              />
              <p className="text-xs ml-2">
                <span className="font-bold text-grey-400">
                  {topBreedsData?.total || '0'} पशु
                </span>{' '}
                पिछले 7 दिन में बिके
              </p>
            </div>
          </>
        ),
      },
      {
        id: 'top-cattle',
        title: 'आपके क्षेत्र के टॉप पशु',
        icon: <TopPashuIcon />,
        onClick: () => router.push('/insights?section=topCattle'),
        content: (
          <>
            <div className=" flex gap-3 justify-between">
              {shown.map((item, index) => (
                <div key={index} className=" flex gap-3 items-center">
                  <Resource post={item} className=" h-[60px] w-[65px]" />
                </div>
              ))}
              {Array.from({ length: fillCount }).map((item, index) => (
                <div key={`ph-${index}`} className=" flex gap-3 items-center">
                  <Resource post={item} className=" h-[60px] w-[65px]" />
                </div>
              ))}
            </div>
            <p className="font-semibold text-black text-base text-left mt-3">
              {formatIndian(topPriceStats?.minPrice) || '0'} -{' '}
              {formatIndian(topPriceStats?.maxPrice) || '0'}
            </p>
            <div className="flex items-center justify-between mt-1">
              <p className="text-grey-400 text-sm font-normal">
                {topPriceStats?.minHighestMilk || '0'}L -{' '}
                {topPriceStats?.maxHighestMilk || '0'}L दूध क्षमता
              </p>
              <p className="flex gap-1 items-center text-grey-400 text-sm font-normal">
                <LocationIcon />
                {user?.district}, {user?.location_state}
              </p>
            </div>
          </>
        ),
      },
      {
        id: 'buyers-sellers',
        title: 'टॉप खरीदार व पशु पालक',
        icon: <TopBuyerAndSellerIcon w={20} h={20} />,
        onClick: () => router.push('/insights?section=topBuyerAndSeller'),
        content: (
          <>
            {[topBuyer, topSeller].map((item, index) => (
              <div
                key={index}
                className="flex gap-3 items-center mt-[10px] border-b-[1px] border-dashed border-surface-1 pb-2 last:border-b-0"
              >
                <Image
                  src={
                    item?.img ||
                    'https://static-assets.animall.in/static/images/Home_page/defaultProfileAvtar.png'
                  }
                  alt="User"
                  width={34}
                  height={34}
                  className="rounded-full !border-2 !border-white !shadow-md"
                />
                <div className="flex flex-col items-start gap-1">
                  <RoleStat isBuyer={index === 0} item={item} />
                </div>
                <div
                  className={
                    'flex items-center ml-auto gap-1.5 h-12 bg-trueGray-100 rounded-lg p-1.5'
                  }
                >
                  <div className="flex justify-center items-center bg-gradient-to-b from-[#2F80ED] to-[#0385DC] w-9 h-9 rounded-md">
                    <CWAPhoneIcon w={18} h={18} fill="#FFFFFF" />
                  </div>
                  <div className="flex items-center justify-center bg-gradient-to-b from-[#5BD066] to-[#27B43E] w-9 h-9 rounded-md">
                    <CWAWAIcon w={20} h={20} fill="#FFFFFF" />
                  </div>
                </div>
              </div>
            ))}
          </>
        ),
      },
    ];
  }, [topBreeds, topPricedPosts, user, router, insightsData]);

  return (
    <div className="w-full overflow-hidden">
      <div className="insight-slider">
        <Slider {...settings}>
          {cardData.map((card) => (
            <InsightCardLayout
              key={card.id}
              title={card.title}
              icon={card.icon}
              onClick={card.onClick}
            >
              {card.content}
            </InsightCardLayout>
          ))}
        </Slider>
      </div>
      <div className="mt-3 flex items-center justify-center gap-2">
        {cardData.map((_, i) => (
          <button
            key={i}
            onClick={() => sliderRef.current?.slickGoTo(i)}
            className={cn(
              'h-2 rounded-full transition-all',
              active == i ? 'w-2 bg-primary-600' : 'w-2 bg-gray-300',
            )}
            aria-label={`Go to slide ${i + 1}`}
          />
        ))}
      </div>
    </div>
  );
}
