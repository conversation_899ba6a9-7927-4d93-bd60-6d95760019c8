export const breedToTypeMap = {
  MURRAH: 'BUFF<PERSON><PERSON>',
  HARYANVI: 'BU<PERSON><PERSON><PERSON>', // Note: This exists in both BUFFALO and COW
  DESI: 'BUFFALO', // Note: This also exists in both
  KALI: 'BUFFALO',
  <PERSON><PERSON><PERSON>: 'BUFF<PERSON><PERSON>',
  JAFFRABADI: 'BU<PERSON><PERSON><PERSON>',
  BANNI: 'BUFFALO',
  KUMBHI: 'BUFFALO',
  KUNNI: 'BUFFALO',
  'NILI RAVI': 'BUFFALO',
  BHADAWARI: 'BUFFALO',
  GUJARATI: 'BUFFALO', // Note: Also exists in COW
  GODAVARI: 'BUFFALO',
  SURTI: 'BUFFALO',
  MEHSANA: 'BUFFALO',
  PANDHARPURI: 'BUFFALO',
  NAGPURI: 'BUFFALO',

  GIR: 'COW',
  SAHIWAL: 'COW',
  DESI_COW: 'COW', // To handle duplicates
  'HOLSTEIN FRIESIAN': 'COW',
  JERSEY: 'COW',
  AMERICAN: 'COW',
  DO<PERSON>L<PERSON>: 'COW',
  RATHI: 'COW',
  THA<PERSON>ARKA<PERSON>: 'COW',
  HARYANVI_COW: 'COW', // To handle duplicates
  MARWARI: 'COW',
  KANKREJ: 'COW',
  KAPILA: 'COW',
  AYRSHIRE: 'COW',
  HARDHENU: 'COW',
  NAGORI: 'COW',
  GUJARATI_COW: 'COW', // To handle duplicates
  'RED SINDHI': 'COW',
  DEONI: 'COW',
  'RED DANE': 'COW',
  'BROWN SWISS': 'COW',
  SANCHORI: 'COW',
  MALVI: 'COW',

  BAKARI: 'OTHER',
  BAKRA: 'OTHER',
  'SHEEP - Female': 'OTHER',
  'SHEEP - Male': 'OTHER',
  HEN: 'OTHER',
  'DOG - Female': 'OTHER',
  'DOG - Male': 'OTHER',
  'CAMEL - Female': 'OTHER',
  'CAMEL - Male': 'OTHER',
  'HORSE - Female': 'OTHER',
  'HORSE - Male': 'OTHER',
  'ELEPHANT - Female': 'OTHER',
  'ELEPHANT - Male': 'OTHER',
};

// Simple lookup function for Option 3
export const getCattleType = (breed) => {
  return breedToTypeMap[breed?.toUpperCase()] === 'BUFFALO'
    ? 'भैंस'
    : breedToTypeMap[breed?.toUpperCase()] === 'COW'
    ? 'गाय'
    : '' || null;
};
export const getCattleTypeImage = (breed) => {
  return breedToTypeMap[breed?.toUpperCase()] === 'BUFFALO'
    ? 'https://static-assets.animall.in/static/images/buffalo_image.png'
    : breedToTypeMap[breed?.toUpperCase()] === 'COW'
    ? 'https://static-assets.animall.in/static/images/cow_image.png'
    : null;
};
export const formatIndian = (price) =>
  !isNaN(price)
    ? price.toLocaleString('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 0,
      })
    : '';
