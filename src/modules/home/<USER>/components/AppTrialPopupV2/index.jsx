import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { Trans, useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

import useSubscription from 'hooks/monetization/useSubscription';
import { useTrialPageConfig } from 'hooks/trialPage/useTrialPageConfig';
import usePhonepe from 'hooks/usePhonepe';
import useScrollBlock from 'hooks/useScrollBlock';

import { logAmplitudeEvent, logFbEvent } from 'lib/log-event';

import UpiDropDown from 'components/UpiDropDown';
import CloseFilled from 'components/icons/CloseFilled';
import Button from 'components/ui/Button';
import {
  H1,
  H2,
  H3,
  H4,
  SmallBody,
  TertiaryBody,
} from 'components/ui/typography';

import CancellationInfo from 'modules/app-subscription/components/CancellationInfo';
import { MonetizationContext } from 'providers/Monetization';
import { usePaymentConfirmation } from 'providers/PaymentConfirmationProvider';
import { useShowPaywall } from 'providers/PaywallProvider';

const testimonials = [
  <>
    अब तक <span className="text-secondary-100 font-bold">22,000+ पशु</span> ₹1
    प्लान से बेचे जा चुके हैं
  </>,
  <>
    पिछले <span className="text-secondary-100 font-bold">30 मिनट</span> में{' '}
    <span className="text-secondary-100 font-bold">180 लोगों</span> ने ₹1 में
    प्लान लिया
  </>,
  <>
    <span className="text-secondary-100 font-bold">सीकर के सुरेश जी</span> ने
    अभी ₹1 में प्लान चालू किया
  </>,
];

const priceText = {
  APP_SUBSCRIPTION_149: 'फिर ₹149/महिना',
  APP_REPEAT_SUBSCRIPTION_149: 'फिर ₹149/महिना',
  APP_SUBSCRIPTION_39: 'फिर ₹39/हफ़्ता',
};

let intervalId = null;
let cnt = 1;
const infoItemCnt = 3;
const AppTrialPopupV2 = ({
  show,
  source = 'NA',
  onSuccess = () => {},
  onClose = () => {},
  onFailure = () => {},
}) => {
  const { t } = useTranslation('trialPage');
  const useTrialOverrides = useTrialPageConfig();

  const defaultTexts = t('default', { returnObjects: true }) || {};
  const trialTexts = t('withTrialOverrides', { returnObjects: true }) || {};

  const heading =
    (useTrialOverrides ? trialTexts.heading : defaultTexts.heading) ||
    'पशु ख़रीदें - बेचें';
  const currentBenefits = useTrialOverrides
    ? trialTexts.benefits
    : defaultTexts.benefits;
  const showTagline = useTrialOverrides && !!trialTexts.tagline;
  const taglineText = useTrialOverrides ? trialTexts.tagline : undefined;
  const shouldBoldPrice = !!(useTrialOverrides && trialTexts.boldPrice);
  const [isOpen, setIsOpen] = useState(show);
  const marqueeRef = useRef();
  const [showCancelPopup, setShowCancelPopup] = useState(false);
  const [blockScroll, allowScroll] = useScrollBlock();
  const router = useRouter();

  const { showErrorPopup, showSuccessPopup } = usePaymentConfirmation();
  const monetizationStats = useContext(MonetizationContext);
  const plan = useMemo(
    () => monetizationStats?.app?.plans?.[0],
    [monetizationStats],
  );
  const { isPhonepeSupported } = usePhonepe();
  const trialPrice = isPhonepeSupported ? 2 : 1;
  const planPrice = !!plan?.extraInfo?.trialPlan ? trialPrice : plan?.price;

  const displayPrice = planPrice;
  useEffect(() => {
    if (isOpen) {
      blockScroll();
      logAmplitudeEvent('LANDED', 'TRIALPAGE', 'SUBSCRIPTION', {
        SOURCE: source,
      });
      logFbEvent('LANDED_TRIALPAGE_SUBSCRIPTION');
    } else {
      allowScroll();
    }

    return () => allowScroll();
  }, [source, allowScroll, blockScroll, isOpen]);

  useEffect(() => {
    setIsOpen(show);
  }, [show]);

  const {
    setShowLoaderScreen,
    setShowSuccessScreen,
    showLoaderScreen,
    showSuccessScreen,
  } = useShowPaywall();

  const successHandler = () => {
    onAfterSuccess();
    setShowLoaderScreen(false);

    if (!!plan?.extraInfo?.trialPlan) {
      setShowSuccessScreen(true);
    } else {
      showSuccessPopup({
        amount: displayPrice,
        onOk: () => {
          window.location.reload();
        },
        onClose: () => {
          window.location.reload();
        },
      });
    }
  };

  const failureHandler = () => {
    setShowLoaderScreen(false);
    showErrorPopup();
    onAfterFailure();
  };

  const onAfterSuccess = useCallback(async () => {
    await onSuccess();
    onClose();
  }, [onSuccess, onClose]);

  const onAfterFailure = useCallback(async () => {
    await onFailure();
    onClose();
  }, [onFailure, onClose]);

  const { startSubscription, setSelectedPackage } = useSubscription({
    source: source,
    onSubscriptionSuccess: successHandler,
    onSubscriptionFailure: failureHandler,
  });

  const onSubscription = useCallback(() => {
    setShowLoaderScreen(true);
    startSubscription(plan);
  }, [startSubscription, plan, setShowLoaderScreen]);

  useEffect(() => {
    if (intervalId) {
      clearInterval(intervalId);
    }
    intervalId = setInterval(() => {
      if (marqueeRef.current) {
        if (cnt > 2) {
          const paragraphsToRemove = marqueeRef.current.querySelectorAll(
            `.plan-info-item-${cnt - 1}`,
          );
          paragraphsToRemove.forEach((paragraph) => {
            paragraph.remove();
          });
        }
        const allParagraphs = Array.from(
          marqueeRef.current.querySelectorAll('p'),
        ).slice(0, infoItemCnt * 2);

        allParagraphs.forEach((paragraph) => {
          const clone = paragraph.cloneNode(true);
          clone.classList.replace('plan-info-item-0', `plan-info-item-${cnt}`);
          marqueeRef.current.appendChild(clone);
        });
        cnt++;
      }
    }, 10000);

    return () => clearInterval(intervalId);
  }, []);

  if (!isOpen || showLoaderScreen || showSuccessScreen) return null;

  const closeHandler = () => {
    if (onClose) {
      onClose();
    } else {
      setIsOpen(false);
    }
  };
  const startAtOffsetMins = plan?.extraInfo?.startAtOffsetMins;
  const trialDays = startAtOffsetMins ? startAtOffsetMins / (60 * 24) : 1;

  return (
    <div className="fixed inset-0 min-h-screen-mobile bg-primary-50 z-[99999] flex flex-col">
      <CancellationInfo
        isOpen={showCancelPopup}
        onClose={() => setShowCancelPopup(false)}
      />
      <div className="absolute top-4 right-4" onClick={closeHandler}>
        <CloseFilled />
      </div>
      <img
        src="https://static-assets.animall.in/static/images/app-subscription/trial-bg.jpg"
        alt="Trial Background"
        className="absolute top-0 inset-x-0 w-full object-contain z-[-2]"
      />

      {/* Soft circle overlay */}
      <div
        style={{
          background: `radial-gradient(
            rgba(173, 242, 237, 1) 0%,
            rgba(173, 242, 237, 1) 55%,
            rgba(173, 242, 237, 0.8) 60%,
            rgba(173, 242, 237, 0) 70%
          )`,
        }}
        className="absolute top-8 left-1/2 -translate-x-1/2 h-96 w-96 rounded-full scale-x-125 z-[-1]"
      ></div>

      <div className="flex-1 overflow-y-auto">
        <div
          className={'flex flex-col justify-center items-center mt-28  px-4'}
        >
          <div className="relative h-[52px] flex justify-center items-center px-4">
            <span className="absolute top-0 inset-x-0 h-0.5 bg-gradient-to-r from-primary-50 via-primary-300 to-primary-50"></span>
            <span className="absolute bottom-0 inset-x-0 h-0.5 bg-gradient-to-r from-primary-50 via-primary-300 to-primary-50"></span>
            <H3
              style={{
                WebkitTextFillColor: 'transparent',
                WebkitBackgroundClip: 'text',
              }}
              className="bg-gradient-primary bg-clip-text"
            >
              {heading}
            </H3>
          </div>

          <div className="flex flex-col justify-center items-center mb-8">
            <div className="flex justify-center items-center gap-1.5">
              <span className="font-rajdhani font-bold text-[120px] leading-none tracking-wide text-primary-700">
                ₹{displayPrice}
              </span>
              <H1>में</H1>
            </div>
            {plan?.extraInfo?.trialPlan && (
              <>
                <H2>{t('price.trial', { days: trialDays })}</H2>
                {isPhonepeSupported && (
                  <div className="flex items-center gap-1 mt-1">
                    <span className="text-xs text-text-secondary">
                      2 दिन में <span className="font-bold">₹2</span> रिफंड हो
                      जाएगा सीधा बैंक में।
                    </span>
                  </div>
                )}
              </>
            )}
          </div>

          <div className="flex flex-col gap-4 items-center p-4 bg-white rounded-lg mb-8">
            {/* Heading */}
            {useTrialOverrides ? (
              <div className="flex items-center justify-center text-xl font-rajdhani leading-6">
                <H4 className="text-primary-600">Animall</H4>
                <H4 className="ml-1">{t('benefits.titleSuffix')}</H4>
              </div>
            ) : (
              <div className="font-rajdhani text-xl leading-6 text-text-primary text-center">
                <span className="text-primary-500">Animall</span> {t('heading')}
              </div>
            )}

            {/* Benefit cards — pill style layout */}
            <div className="flex justify-between w-full gap-3">
              {currentBenefits.map((benefit) => (
                <div
                  key={benefit.highlightedText}
                  className="w-full max-w-[88px] bg-surface-0 py-2 px-1 rounded-lg flex flex-col items-center justify-center text-center shadow-sm"
                >
                  <div className="flex flex-wrap justify-center items-baseline gap-1">
                    <H4 className="text-primary-500">
                      {benefit.highlightedText}
                    </H4>
                    <TertiaryBody className="inline text-sm">
                      {benefit.normalText}
                    </TertiaryBody>
                  </div>
                </div>
              ))}
            </div>
            {useTrialOverrides && showTagline && (
              <SmallBody className="text-center leading-4">
                {taglineText}
              </SmallBody>
            )}
          </div>

          <div className="flex flex-col items-center gap-1 mx-auto mb-7">
            <H4
              className={`font-rajdhani text-xl leading-6 text-text-primary ${
                shouldBoldPrice ? 'font-bold' : ''
              }`}
            >
              {priceText[plan?.name] ||
                t('price.thenPerMonth', {
                  price: plan?.price,
                })}
            </H4>
            <p className="text-sm leading-4 text-text-secondary">
              <Trans
                i18nKey="cta.cancelAnytimeRich"
                t={t}
                components={{
                  b: (
                    <span
                      className="font-bold"
                      onClick={() => setShowCancelPopup(true)}
                    />
                  ),
                }}
              >
                <span
                  className="font-bold"
                  onClick={() => setShowCancelPopup(true)}
                >
                  Cancel
                </span>{' '}
                anytime
              </Trans>
            </p>
          </div>
        </div>
      </div>
      <div
        className={
          'w-full flex flex-col items-center gap-3 bg-white pt-3 pb-4 px-4 rounded-t-lg border-t border-surface-0'
        }
      >
        <div className="w-full overflow-hidden">
          <div
            className="py-1.5 pl-6 font-normal text-sm text-text-primary flex flex-shrink-0 items-center gap-6 animate-marquee"
            ref={marqueeRef}
            style={{ width: '1700px' }}
          >
            {testimonials.map((item) => (
              <>
                <p className="whitespace-nowrap plan-info-item-0">{item}</p>
                <p className="p-0.5 rounded-full bg-text-secondary plan-info-item-0"></p>
              </>
            ))}
          </div>
        </div>

        <div className="w-full flex flex-row gap-4">
          {isPhonepeSupported && <UpiDropDown onChange={setSelectedPackage} />}
          <Button
            size="xl"
            textClassName="w-full flex justify-between items-center text-surface-3"
            onClick={onSubscription}
            className="shine"
          >
            <span>प्लान लें</span>
            <span>₹{displayPrice}</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AppTrialPopupV2;
