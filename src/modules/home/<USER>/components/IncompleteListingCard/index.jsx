import { useEffect, useMemo, useRef, useState } from 'react';

import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

import usePlans from 'hooks/monetization/usePlans';

import { getCommaSeparatedNumber, isInBuckets } from 'lib/utils';

import { H3, H4, SecondaryBody, TertiaryBody } from 'components/ui/typography';

import {
  getDraftedPostBlockedDate,
  isDiscountTimerExpired,
} from 'modules/classifieds-plans/utils';
import { getRecentlyPurchasedPlanCount } from 'modules/home/<USER>';
import { NewPlansPageBuckets } from 'modules/plans/constants';
import { parseCookies } from 'nookies';

import { BlockedIcon } from '../../icons/IncompleteListingCardIcon';
import Resource from '../Resource';

let intervalId = null;
let cnt = 1;
const IncompleteListingCard = ({
  blockedDraftedListing,
  districtsRecentTxn,
  monetizationStats,
  buyersCount,
  lastOrder,
}) => {
  const marqueeRef = useRef(null);
  const router = useRouter();
  const isEligibleForVIPUserPlansPage =
    monetizationStats?.listing?.isEligibleForVIPUserPlansPage;

  const { t } = useTranslation('landingPage');

  const { bucketId, discountTimerStartDate } = parseCookies();
  const [recentlyPurchasedPlanCount, setRecentlyPurchasedPlanCount] =
    useState(0);

  useEffect(() => {
    setRecentlyPurchasedPlanCount(getRecentlyPurchasedPlanCount());
  }, []);

  const {
    primaryPlans,
    otherPlans,
    extraDiscountConfig,
    setExtraDiscountConfig,
  } = usePlans({ blockedDraftedListing });

  const { totalPaidPosts, totalPaidUsers } =
    monetizationStats?.listing?.stats || {};

  const infoItemCnt = useMemo(() => {
    let cnt = 1;
    if (districtsRecentTxn) {
      cnt++;
    }
    if (totalPaidPosts && totalPaidUsers) {
      cnt++;
    }
    return cnt;
  }, [districtsRecentTxn, totalPaidPosts, totalPaidUsers]);

  const planDetails = useMemo(() => {
    const allPlans = [...primaryPlans, ...otherPlans];
    if (!allPlans.length || !lastOrder || lastOrder?.status !== 'created') {
      return null;
    }

    const lastOrderPlanNames = [
      lastOrder?.notes?.planName,
      lastOrder?.notes?.originalPlanName,
    ].filter(Boolean);

    return allPlans.find((plan) => {
      const planNames = [plan?.name, plan?.discount?.name].filter(Boolean);
      return planNames.some((name) => lastOrderPlanNames.includes(name));
    });
  }, [lastOrder, primaryPlans, otherPlans]);

  const shouldShowExtraDiscountSection = useMemo(() => {
    if (!extraDiscountConfig || !discountTimerStartDate) return false;

    const blockedDate = getDraftedPostBlockedDate(blockedDraftedListing);
    const startDate = new Date(discountTimerStartDate);
    if (new Date(blockedDate) <= startDate && !isDiscountTimerExpired()) {
      return true;
    }
    return false;
  }, [extraDiscountConfig, discountTimerStartDate, blockedDraftedListing]);

  useEffect(() => {
    if (intervalId) {
      clearInterval(intervalId);
    }
    intervalId = setInterval(
      () => {
        if (marqueeRef.current) {
          if (cnt > 2) {
            const paragraphsToRemove = marqueeRef.current.querySelectorAll(
              `.info-item-${cnt - 1}`,
            );
            paragraphsToRemove.forEach((paragraph) => {
              paragraph.remove();
            });
          }
          const allParagraphs = Array.from(
            marqueeRef.current.querySelectorAll('p'),
          ).slice(0, infoItemCnt * 2);

          allParagraphs.forEach((paragraph) => {
            const clone = paragraph.cloneNode(true);
            clone.classList.replace('info-item-0', `info-item-${cnt}`);
            marqueeRef.current.appendChild(clone);
          });
          cnt++;
        }
      },
      infoItemCnt === 3 ? 10000 : 4500,
    );

    return () => clearInterval(intervalId);
  }, [infoItemCnt]);

  useEffect(() => {
    if (marqueeRef.current) {
      marqueeRef.current.style.width = `${
        marqueeRef.current.scrollWidth + 12
      }px`;
      const classes = [
        0,
        'animate-marquee-10s',
        'animate-marquee-15s',
        'animate-marquee-20s',
      ];
      marqueeRef.current.classList.add(classes[infoItemCnt]);
    }
  }, [infoItemCnt, marqueeRef]);

  const { uiInfo } = planDetails || {};
  const { title } = uiInfo || {};

  const onClick = () => {
    let url = '/plans?initialPlanType=listing&source=Homescreen';

    if (planDetails && !isEligibleForVIPUserPlansPage) {
      if (isInBuckets(NewPlansPageBuckets, bucketId)) {
        // new plans page
        url += `&preSelectedPlan=${planDetails?.id}`;
      } else {
        // old plans page
        url += `&preSelectedPlan=${planDetails?.name}`;
      }
    }
    const postId = lastOrder?.notes?.postId;
    if (postId) {
      url += `&postId=${postId}`;
    }
    if (shouldShowExtraDiscountSection && !isEligibleForVIPUserPlansPage) {
      url += '&extraDiscount=true';
    }
    router.push(url);
  };

  return (
    <div
      className="sm:mx-auto sm:max-w-md  mx-4 flex flex-col mb-4 p-3 rounded-xl bg-white shadow-sm border-[1px] border-primary-600"
      onClick={onClick}
    >
      <div className="flex gap-3">
        <div>
          <Resource
            post={blockedDraftedListing}
            className=" h-[83px] w-[81px] rounded-[6px] shadow-md"
          >
            <div className="absolute bottom-[-4px] px-1 py-[2px] shadow-sm rounded-[100px] bg-white w-full h-fit flex items-center gap-[2px]">
              <BlockedIcon />
              <p className=" text-status-rejected-text font-bold text-xs">
                {t('pending')}
              </p>
            </div>
          </Resource>
        </div>
        <div className=" flex flex-col justify-between w-full">
          {!!planDetails && title ? (
            <H3>
              <span className="text-primary-600">{t('planLeker')}</span> {title}{' '}
              {t('sell')}
            </H3>
          ) : (
            <H3 className=" text-primary-600">{t('takePlanToSell')} </H3>
          )}
          <div className=" bg-primary-600 w-full text-lg font-bold rounded-md py-3 shine">
            <SecondaryBody className=" text-center text-primary-25 font-bold">
              {t('takePlan')}
            </SecondaryBody>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IncompleteListingCard;
