import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import usePhonepe from 'hooks/usePhonepe';

import { getCommaSeparatedNumber } from 'lib/utils';

import Image from 'components/UnoptimizedImage';
import Button from 'components/ui/Button';
import { H3, SmallBody, TertiaryBody } from 'components/ui/typography';

import { getRecentlyPurchasedPlanCount } from 'modules/home/<USER>';
import { useShowPaywall } from 'providers/PaywallProvider';

let intervalId = null;
let cnt = 1;
const AppTrialNudge = ({
  availableCattles,
  districtsRecentTxn,
  monetizationStats,
}) => {
  const marqueeRef = useRef(null);
  const [recentlyPurchasedPlanCount, setRecentlyPurchasedPlanCount] =
    useState(107);

  const { showPaywall } = useShowPaywall();

  const plan = useMemo(
    () => monetizationStats?.app?.plans?.[0],
    [monetizationStats],
  );

  let { totalPaidPosts, totalPaidUsers } =
    monetizationStats?.listing?.stats || {};

  totalPaidPosts = totalPaidPosts || 23300;
  totalPaidUsers = totalPaidUsers || 4600;

  useEffect(() => {
    setRecentlyPurchasedPlanCount(getRecentlyPurchasedPlanCount());
  }, []);

  const infoItemCnt = useMemo(() => {
    let cnt = 1;
    if (districtsRecentTxn) {
      cnt++;
    }
    if (totalPaidPosts && totalPaidUsers) {
      cnt++;
    }
    return cnt;
  }, [districtsRecentTxn, totalPaidPosts, totalPaidUsers]);

  useEffect(() => {
    if (intervalId) {
      clearInterval(intervalId);
    }
    intervalId = setInterval(
      () => {
        if (marqueeRef.current) {
          if (cnt > 2) {
            const paragraphsToRemove = marqueeRef.current.querySelectorAll(
              `.plan-info-item-${cnt - 1}`,
            );
            paragraphsToRemove.forEach((paragraph) => {
              paragraph.remove();
            });
          }
          const allParagraphs = Array.from(
            marqueeRef.current.querySelectorAll('p'),
          ).slice(0, infoItemCnt * 2);

          allParagraphs.forEach((paragraph) => {
            const clone = paragraph.cloneNode(true);
            clone.classList.replace(
              'plan-info-item-0',
              `plan-info-item-${cnt}`,
            );
            marqueeRef.current.appendChild(clone);
          });
          cnt++;
        }
      },
      infoItemCnt === 3 ? 10000 : 4500,
    );

    return () => clearInterval(intervalId);
  }, [infoItemCnt]);

  const { isPhonepeSupported } = usePhonepe();
  const trialPrice = isPhonepeSupported ? 2 : 1;
  const planPrice = !!plan?.extraInfo?.trialPlan ? trialPrice : plan?.price;

  const displayPrice = planPrice;

  const onSubscription = useCallback(() => {
    showPaywall({ source: 'APP_TRIAL_NUDGE' });
  }, [showPaywall]);

  return (
    <div
      className="sm:mx-auto sm:max-w-md  mx-4 flex flex-col mb-4 p-3 rounded-xl bg-white shadow-sm border-[1px] border-primary-600"
      onClick={onSubscription}
    >
      <div className=" flex items-center gap-3">
        <div className="relative w-20 h-[74px] rounded-xl overflow-clip">
          <Image
            src={
              'https://static-assets.animall.in/static/images/intro_cattle_img.png'
            }
            layout="fill"
            alt="pashu"
            objectFit="cover"
            objectPosition="center"
          />
          <div className="absolute -bottom-1 inset-x-0 bg-white rounded-full flex items-center justify-center gap-[0.5px] py-1 px-0.5 whitespace-nowrap shadow-md">
            <div className=" w-fit bg-accent-success px-1 text-white rounded-3xl font-rajdhani font-bold text-[10px]">
              LIVE
            </div>
            <SmallBody className="font-bold">
              {' '}
              {availableCattles} पशु है
            </SmallBody>
          </div>
        </div>
        <div className=" flex flex-col justify-between gap-1 w-full">
          <H3 className=" text-primary-600">
            <span className="text-primary-600">बेचो-खरीदो</span>{' '}
            <span className="text-text-primary">जितना चाहो</span>
          </H3>
          <Button
            size="xl"
            textClassName="w-full text-lg font-bold flex justify-between items-center text-surface-3"
            className="shine h-12"
          >
            <span>प्लान लें</span>
            <span>₹{displayPrice}</span>
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AppTrialNudge;
