export const BILLING_CYCLE = {
  MONTHLY: 'MONTHLY',
  YEARLY: 'YEARLY',
};

export const appendFeatures = (plans) => {
  return plans.map((plan) => {
    const extraInfo = plan.extraInfo || {};
    const featureInfo = extraInfo.featureInfo || {};
    const { dailyCwaLimit, billingCycle, listingLimit, cashbackCoins, isVip } =
      featureInfo;
    const features = [
      { icon: 'restricted', text: 'कोई ऐड नहीं दिखेगा' },
      {
        icon: 'call',
        text: `दिन में ${dailyCwaLimit} किसानों को कॉल कर पाएंगे`,
      },
      {
        icon: 'cattle',
        text: `${
          billingCycle === BILLING_CYCLE.MONTHLY ? 'महीने' : 'साल'
        } में ${listingLimit} पशु दर्ज कर सकेंगे`,
      },
      isVip
        ? { icon: 'primeCattle', text: 'सारे पशु बनेंगे प्राइम पशु' }
        : null,
      { icon: 'lock', text: 'ऐप की सारी सुविधाएं चालू होंगी' },
      {
        icon: 'rupee',
        text: `${cashbackCoins ?? plan.price} कॉइन्स मिलेंगे`,
      },
    ];
    plan.features = features.filter(Boolean);
    return plan;
  });
};
