import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import useSubscription from 'hooks/monetization/useSubscription';
import useScrollBlock from 'hooks/useScrollBlock';

import { logAmplitudeEvent, logFbEvent } from 'lib/log-event';

import CloseFilled from 'components/icons/CloseFilled';
import { H3, SecondaryBody } from 'components/ui/typography';

import Tabs from './components/Tabs';
import PlanCard from './components/planCard';

import CancellationInfo from 'modules/app-subscription/components/CancellationInfo';
import { MonetizationContext } from 'providers/Monetization';
import { usePaymentConfirmation } from 'providers/PaymentConfirmationProvider';
import { useShowPaywall } from 'providers/PaywallProvider';

import { BILLING_CYCLE, appendFeatures } from './helper';

const benefits = [
  { highlightedText: '0%', normalText: 'कमीशन' },
  { highlightedText: '3000+', normalText: 'रोज़ बिकते है' },
  { highlightedText: '1 करोड़', normalText: 'किसान' },
];

const MultiPlanAppTrialPopup = ({
  show,
  source = 'NA',
  onSuccess = () => {},
  onClose = () => {},
  onFailure = () => {},
}) => {
  const [tab, setTab] = useState(BILLING_CYCLE.MONTHLY);

  const [isOpen, setIsOpen] = useState(show);
  const planRef = useRef();
  const [showCancelPopup, setShowCancelPopup] = useState(false);
  const [blockScroll, allowScroll] = useScrollBlock();
  const scrollRef = useRef(null);

  const { showErrorPopup, showSuccessPopup } = usePaymentConfirmation();
  const monetizationStats = useContext(MonetizationContext);
  const plans = useMemo(() => {
    const plans =
      monetizationStats?.app?.plans?.sort((a, b) => b.sortOrder - a.sortOder) ||
      [];
    const planWithFeatures = appendFeatures(plans);
    return planWithFeatures;
  }, [monetizationStats]);

  const filteredPlans = useMemo(
    () => plans?.filter((p) => p?.extraInfo?.featureInfo?.billingCycle === tab),
    [plans, tab],
  );

  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTo({
        left: 0,
        behavior: 'smooth',
      });
    }
  }, [tab]);

  useEffect(() => {
    if (isOpen) {
      blockScroll();
      logAmplitudeEvent('LANDED', 'MULTIPLANPOPUP', 'SUBSCRIPTION', {
        SOURCE: source,
      });
      logFbEvent('LANDED_MULTIPLANPOPUP_SUBSCRIPTION');
    } else {
      allowScroll();
    }

    return () => allowScroll();
  }, [source, allowScroll, blockScroll, isOpen]);

  useEffect(() => {
    setIsOpen(show);
  }, [show]);

  const {
    setShowLoaderScreen,
    setShowSuccessScreen,
    showLoaderScreen,
    showSuccessScreen,
  } = useShowPaywall();

  const successHandler = () => {
    onAfterSuccess();
    setShowLoaderScreen(false);

    if (!!planRef?.current?.extraInfo?.trialPlan) {
      setShowSuccessScreen(true);
    } else {
      const planPrice = planRef?.current?.price;
      showSuccessPopup({
        amount: planPrice,
        onOk: () => {
          window.location.reload();
        },
        onClose: () => {
          window.location.reload();
        },
      });
    }
  };

  const failureHandler = () => {
    setShowLoaderScreen(false);
    showErrorPopup();
    onAfterFailure();
  };

  const onAfterSuccess = useCallback(async () => {
    await onSuccess();
    onClose();
  }, [onSuccess, onClose]);

  const onAfterFailure = useCallback(async () => {
    await onFailure();
    onClose();
  }, [onFailure, onClose]);

  const { startSubscription } = useSubscription({
    source: source,
    onSubscriptionSuccess: successHandler,
    onSubscriptionFailure: failureHandler,
  });

  const onSubscription = useCallback(
    (plan) => {
      planRef.current = plan;
      setShowLoaderScreen(true);
      startSubscription(plan);
    },
    [startSubscription, setShowLoaderScreen],
  );

  if (
    !isOpen ||
    showLoaderScreen ||
    showSuccessScreen ||
    !plans.length ||
    !filteredPlans.length
  )
    return null;

  const closeHandler = () => {
    planRef.current = null;
    if (onClose) {
      onClose();
    } else {
      setIsOpen(false);
    }
  };

  return (
    <div className="fixed overflow-auto inset-0 min-h-screen-mobile bg-primary-50 z-[99999] flex flex-col">
      <CancellationInfo
        isOpen={showCancelPopup}
        onClose={() => setShowCancelPopup(false)}
      />
      <div className="absolute top-4 right-4" onClick={closeHandler}>
        <CloseFilled />
      </div>
      <img
        src="https://static-assets.animall.in/static/images/app-subscription/trial-bg.jpg"
        alt="Trial Background"
        className="absolute top-0 inset-x-0 w-full object-contain z-[-2]"
      />

      <div
        style={{
          background: `radial-gradient(
            rgba(173, 242, 237, 1) 0%,
            rgba(173, 242, 237, 1) 55%,
            rgba(173, 242, 237, 0.8) 60%,
            rgba(173, 242, 237, 0) 70%
          )`,
        }}
        className="absolute top-8 left-1/2 -translate-x-1/2 h-96 w-96 rounded-full scale-x-125 z-[-1]"
      ></div>

      <div className="w-full pt-[70px] pb-5">
        <div className="relative h-[52px] flex justify-center items-center px-4 mb-3 w-fit mx-auto">
          <span className="absolute  mx-auto top-0 inset-x-0 w-2/4 h-0.5 bg-gradient-to-r from-primary-50 via-primary-300 to-primary-50"></span>
          <span className="absolute bottom-0 w-2/4 mx-auto inset-x-0 h-0.5 bg-gradient-to-r from-primary-50 via-primary-300 to-primary-50"></span>
          <H3
            style={{
              WebkitTextFillColor: 'transparent',
              WebkitBackgroundClip: 'text',
            }}
            className="bg-gradient-primary bg-clip-text"
          >
            Animall पर पशु ख़रीदें-बेचें
          </H3>
        </div>
        <div className="flex justify-between items-center px-4">
          {benefits.map((benefit, index) => (
            <>
              {index !== 0 && <span className="h-5 w-[1px] bg-grey-100"></span>}
              <div
                className="flex items-center justify-start gap-0.5"
                key={benefit}
              >
                <SecondaryBody className="text-primary-500">
                  {benefit.highlightedText}
                </SecondaryBody>
                <SecondaryBody className="text-text-secondary">
                  {benefit.normalText}
                </SecondaryBody>
              </div>
            </>
          ))}
        </div>

        <div className="max-w-6xl mx-auto px-4 pt-6 flex justify-center items-center gap-3">
          <Tabs value={tab} onChange={setTab} />
        </div>

        <div
          className="px-4 overflow-x-auto overflow-y-visible self-stretch flex justify-between gap-4"
          ref={scrollRef}
        >
          {filteredPlans.map((p) => (
            <PlanCard key={p.id} plan={p} startSubscription={onSubscription} />
          ))}
        </div>
      </div>
    </div>
  );
};

export default MultiPlanAppTrialPopup;
