export const RestrictedIcon = ({ width = 10, height = 10, fill = 'white' }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 10 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5 0.5C2.52676 0.5 0.5 2.52676 0.5 5C0.5 7.47324 2.52676 9.5 5 9.5C7.47324 9.5 9.5 7.47324 9.5 5C9.5 2.52676 7.47324 0.5 5 0.5ZM1.57227 5C1.57227 3.11209 3.11209 1.57227 5 1.57227C5.7119 1.57227 6.39748 1.79373 6.98281 2.21033L5 4.19315L2.21037 6.98281C1.79373 6.39744 1.57227 5.7119 1.57227 5ZM5 8.42773C4.2881 8.42773 3.60252 8.20623 3.01719 7.78963L7.78965 3.01717C8.20627 3.60252 8.42773 4.28807 8.42773 5C8.42773 6.88787 6.88791 8.42773 5 8.42773Z"
        fill={fill}
      />
      <path
        d="M9.5 5C9.5 7.47324 7.47324 9.5 5 9.5V8.42773C6.88791 8.42773 8.42773 6.88787 8.42773 5C8.42773 4.28807 8.20627 3.60252 7.78963 3.01719L5 5.80682V4.19315L6.98281 2.21033C6.39748 1.79373 5.7119 1.57227 5 1.57227V0.5C7.47324 0.5 9.5 2.52676 9.5 5Z"
        fill={fill}
      />
    </svg>
  );
};
export const CallIcon = ({ width = 10, height = 10, fill = 'white' }) => {
  return (
    <svg
      width={width}
      height={width}
      viewBox="0 0 10 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.77784 6.40747C8.22683 6.40747 7.6858 6.32129 7.1731 6.15186C6.92187 6.06617 6.61304 6.14478 6.45971 6.30226L5.44775 7.06618C4.27416 6.43972 3.55126 5.71705 2.93335 4.55227L3.6748 3.56668C3.86743 3.3743 3.93652 3.09329 3.85374 2.82962C3.68359 2.31423 3.59715 1.77345 3.59715 1.2222C3.59717 0.823977 3.2732 0.5 2.87499 0.5H1.22218C0.823977 0.5 0.5 0.823977 0.5 1.22218C0.5 5.78663 4.21339 9.5 8.77784 9.5C9.17605 9.5 9.50002 9.17602 9.50002 8.77782V7.12963C9.5 6.73145 9.17602 6.40747 8.77784 6.40747Z"
        fill={fill}
      />
    </svg>
  );
};
export const CattleIcon = ({ width = 10, height = 10, fill = 'white' }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 10 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.15383 3.13532C6.99057 2.81298 6.80219 2.51576 6.55311 2.24365C6.51334 2.1997 6.45682 2.17458 6.39821 2.17458H5.62585C5.72214 2.1683 6.54473 2.06574 6.47566 0.719865C6.47147 0.644512 6.50915 0.573346 6.57404 0.531484C6.63892 0.491715 6.71846 0.489622 6.78544 0.525205C6.80637 0.537763 7.29826 0.814055 7.4678 1.4378C7.60594 1.94015 7.4992 2.51157 7.15383 3.13532Z"
        fill={fill}
      />
      <path
        d="M4.39502 2.17458H3.6729C3.61429 2.17458 3.55778 2.1997 3.51801 2.24365C3.26893 2.51785 3.0638 2.82136 2.8817 3.16044C2.52378 2.52623 2.41284 1.94852 2.55308 1.4378C2.72262 0.814055 3.2145 0.537763 3.23544 0.525205C3.30242 0.489622 3.38195 0.491715 3.44684 0.531484C3.51173 0.573346 3.5494 0.644512 3.54522 0.719865C3.47614 2.06783 4.30083 2.1683 4.39502 2.17458Z"
        fill={fill}
      />
      <path
        d="M9.97105 4.09531C9.61731 4.50138 9.26985 4.79232 8.90774 4.98698C8.66494 5.11885 8.38446 5.20048 8.07468 5.22769L7.66443 5.26118C7.65606 5.29257 7.64559 5.32397 7.63513 5.35537L7.56606 5.55212C7.40489 6.00214 7.24999 6.43542 7.14534 6.84148C6.65555 6.41449 5.89575 6.15075 5.01036 6.15075C4.12497 6.15075 3.36517 6.41449 2.87538 6.84148C2.77072 6.43542 2.61583 6.00214 2.45466 5.55212L2.38559 5.35537C2.37512 5.33025 2.36884 5.30513 2.36256 5.28002L1.9251 5.24234C1.62579 5.21722 1.34531 5.13768 1.09413 5.00372C0.72993 4.80697 0.382473 4.51393 0.0287355 4.10996C-0.0298718 4.04298 -0.0403373 3.9467 0.00361814 3.86925C0.0454805 3.7939 0.133391 3.74995 0.219209 3.7646C0.436894 3.80018 0.901566 3.60552 1.46043 3.24341C1.80161 3.02363 2.22023 2.96712 2.58443 3.09061C2.67653 3.12201 2.76863 3.16178 2.85863 3.20155C2.867 3.1869 2.87328 3.17434 2.88166 3.15969C3.06376 2.8206 3.26888 2.5171 3.51796 2.2429C3.55773 2.19895 3.61425 2.17383 3.67285 2.17383H6.3981C6.4567 2.17383 6.51322 2.19895 6.55299 2.2429C6.80207 2.51501 6.99045 2.81223 7.15371 3.13457C7.15999 3.14713 7.16627 3.15969 7.17255 3.17225C7.25418 3.13457 7.33581 3.10317 7.41535 3.07596C7.78374 2.95247 8.20446 3.00898 8.54145 3.22876C9.09822 3.58878 9.5608 3.78553 9.78267 3.74995C9.86849 3.73739 9.9543 3.77925 9.99826 3.8567C10.0401 3.93414 10.0297 4.02833 9.97105 4.09531Z"
        fill={fill}
      />
      <path
        d="M5.01032 6.56934C3.71886 6.56934 2.70789 7.21192 2.70789 8.03452C2.70789 8.85711 3.71886 9.4997 5.01032 9.4997C6.30177 9.4997 7.31275 8.85711 7.31275 8.03452C7.31275 7.21192 6.30177 6.56934 5.01032 6.56934ZM4.17307 8.24383H3.75444C3.63932 8.24383 3.54513 8.14964 3.54513 8.03452C3.54513 7.9194 3.63932 7.82521 3.75444 7.82521H4.17307C4.28819 7.82521 4.38238 7.9194 4.38238 8.03452C4.38238 8.14964 4.28819 8.24383 4.17307 8.24383ZM6.26619 8.24383H5.84756C5.73244 8.24383 5.63825 8.14964 5.63825 8.03452C5.63825 7.9194 5.73244 7.82521 5.84756 7.82521H6.26619C6.38131 7.82521 6.4755 7.9194 6.4755 8.03452C6.4755 8.14964 6.38131 8.24383 6.26619 8.24383Z"
        fill={fill}
      />
    </svg>
  );
};
export const LockIcon = ({ width = 6, height = 10, fill = 'white' }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 6 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.00004 3.50004C2.602 3.50004 2.223 3.57912 1.87504 3.72009V2.37504C1.87504 1.75391 2.37891 1.25004 3.00004 1.25004C3.62149 1.25004 4.12504 1.75391 4.12504 2.37504V2.75H4.87507V2.37504C4.87507 1.33936 4.03572 0.5 3.00004 0.5C1.96436 0.5 1.125 1.33936 1.125 2.37504V4.16029C0.439871 4.70996 0 5.55292 0 6.49996C0 8.15675 1.34325 9.5 3.00004 9.5C4.65682 9.5 6.00007 8.15675 6.00007 6.49996C6.00007 4.84318 4.65682 3.50004 3.00004 3.50004ZM3.375 6.77097V7.62496H2.62507V6.77097C2.40166 6.64138 2.25 6.40186 2.25 6.125C2.25 5.71112 2.58581 5.37496 3.00004 5.37496C3.41427 5.37496 3.75007 5.71112 3.75007 6.125C3.75007 6.40186 3.59842 6.64138 3.375 6.77097Z"
        fill={fill}
      />
    </svg>
  );
};
export const RupeeIcon = ({ width = 6, height = 8, fill = 'white' }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 6 8"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.49995 1.81248C5.4659 1.77839 5.42232 1.76138 5.36934 1.76138H4.39779C4.33335 1.44709 4.21213 1.17427 4.03412 0.943218H5.35792C5.41104 0.943218 5.4545 0.926183 5.48867 0.892173C5.52258 0.858102 5.53962 0.8145 5.53962 0.761484V0.181814C5.53962 0.128758 5.5226 0.0852351 5.48867 0.0511251C5.45446 0.0170948 5.411 0 5.35788 0H0.630646C0.577591 0 0.534068 0.0170948 0.499958 0.0511052C0.465947 0.0852152 0.448853 0.128738 0.448853 0.181794V0.937467C0.448853 0.986701 0.466843 1.02931 0.502823 1.06529C0.538804 1.10127 0.581412 1.11926 0.630646 1.11926H1.45452C2.25372 1.11926 2.76131 1.33321 2.97719 1.76128H0.630646C0.577591 1.76128 0.534008 1.7783 0.499958 1.81239C0.465947 1.84652 0.448853 1.89002 0.448853 1.94307V2.52266C0.448853 2.57568 0.465868 2.61926 0.499958 2.65327C0.534068 2.6874 0.57767 2.70438 0.630646 2.70438H3.05685C2.97351 3.01507 2.77936 3.25177 2.4744 3.41466C2.16942 3.57757 1.76704 3.65902 1.26701 3.65902H0.630646C0.581412 3.65902 0.538804 3.67703 0.502823 3.71299C0.466823 3.74899 0.448853 3.79162 0.448853 3.84081V4.56248C0.448853 4.61179 0.465868 4.65344 0.499958 4.68749C1.22719 5.4602 2.17041 6.54155 3.32949 7.93182C3.36362 7.97731 3.41095 8 3.47156 8H4.57948C4.6591 8 4.71407 7.96601 4.74428 7.89769C4.78215 7.82951 4.77473 7.76511 4.72153 7.70449C3.61561 6.34839 2.74622 5.33325 2.11363 4.65904C2.75758 4.58329 3.28032 4.37491 3.68186 4.03407C4.08333 3.69319 4.32762 3.24992 4.4148 2.70448H5.36934C5.42232 2.70448 5.46592 2.68748 5.49995 2.65337C5.53408 2.61936 5.55116 2.57578 5.55116 2.52276V1.94323C5.55116 1.89012 5.53408 1.84659 5.49995 1.81248Z"
        fill={fill}
      />
    </svg>
  );
};
export const PrimeCattleIcon = ({
  width = 12,
  height = 10,
  fill = '#2E3C4D',
}) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 12 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.22602 9.23307C5.1628 8.98566 5.37552 8.11231 5.07913 7.31689C5.01958 7.1356 4.9175 6.97792 4.78363 6.86042C4.00438 6.22829 3.79561 5.29185 3.77271 4.26263C3.7797 4.05108 3.75259 3.84145 3.69272 3.64411C3.63468 3.42139 3.45615 3.27185 3.24965 3.273C3.04486 3.27593 2.86048 3.43125 2.79591 3.65524C2.58283 4.20449 2.66674 4.74384 2.81403 5.27329C2.86783 5.46998 2.95902 5.6543 2.99531 5.8547C3.0316 6.0551 3.04496 6.2889 2.81727 6.39653C2.58959 6.50415 2.48305 6.32354 2.41401 6.15778C1.99853 5.16814 1.85468 4.17851 2.39129 3.12084C2.68661 2.55056 3.11764 2.19553 3.62933 1.92462C3.9449 1.76009 4.26857 1.61165 4.52011 1.32342C4.72466 1.09174 4.84164 0.837711 4.7201 0.52731C4.70825 0.497066 4.67843 0.478516 4.64552 0.478516C4.6288 0.478516 4.61229 0.48342 4.59774 0.492188C4.33118 0.652816 4.03708 0.695267 3.75182 0.749428C2.64203 0.973333 1.7835 1.58443 1.22375 2.7423C0.988722 3.23712 0.840654 3.54886 0.74129 4.07831C0.697692 4.36554 0.684726 4.65631 0.702677 4.94424C0.715134 5.22915 0.741263 5.51244 0.780946 5.79285C1.01987 7.25256 1.61241 8.39559 2.87149 8.94855C3.12781 9.07602 3.39649 9.1714 3.67296 9.23307C3.85427 9.27249 4.04188 9.27249 4.22602 9.23307Z"
        fill={fill}
      />
      <path
        d="M7.44208 9.25771C6.52648 8.94081 6.37979 8.05397 6.73495 7.283C6.80792 7.10668 6.92152 6.95709 7.06382 6.84995C7.88824 6.27799 8.1666 5.35982 8.26656 4.33521C8.27543 4.12374 8.31818 3.91672 8.39267 3.72442C8.46723 3.50668 8.65646 3.37094 8.8623 3.38755C9.0663 3.40583 9.23851 3.57453 9.28612 3.80272C9.45744 4.36639 9.33335 4.89794 9.14681 5.41487C9.07842 5.60698 8.97367 5.78394 8.92247 5.98106C8.87127 6.17818 8.84043 6.41032 9.05941 6.5347C9.27839 6.65909 9.39816 6.48697 9.47943 6.32684C9.9679 5.37112 10.1855 4.39505 9.72965 3.30014C9.47789 2.70934 9.07467 2.32301 8.58472 2.01452C8.28237 1.82681 7.97073 1.65453 7.74149 1.34826C7.55487 1.10191 7.45726 0.839825 7.60172 0.539404C7.6158 0.510132 7.64693 0.493869 7.67974 0.496335C7.69642 0.497588 7.71252 0.503715 7.72637 0.513549C7.98014 0.693699 8.27023 0.758067 8.55063 0.833451C9.64052 1.13988 10.4509 1.8136 10.9223 3.01016C11.1196 3.52119 11.2439 3.84315 11.3033 4.37856C11.3252 4.66825 11.3164 4.95917 11.2769 5.24495C11.2431 5.52812 11.1958 5.80866 11.1352 6.08531C10.7876 7.52301 10.1111 8.61843 8.81412 9.07549C8.54898 9.1834 8.2739 9.25838 7.99359 9.29915C7.80983 9.32488 7.62275 9.31082 7.44208 9.25771Z"
        fill={fill}
      />
      <path
        d="M6.06524 5.06738C6.27313 5.06738 6.5662 5.11883 6.59318 5.51325C6.61403 5.80846 6.26424 6.12571 5.93526 6.13183C5.62665 6.14286 5.28348 5.83663 5.28053 5.54755C5.27759 5.25847 5.5443 5.06738 6.06524 5.06738Z"
        fill={fill}
      />
      <path
        d="M7.65888 3.8412C7.65945 3.9758 7.60608 4.10646 7.5122 4.20032C7.33173 4.38076 7.00547 4.38892 6.82098 4.22576C6.81424 4.2198 6.80765 4.21362 6.80122 4.20721C6.70391 4.11022 6.65228 3.97146 6.65962 3.82662C6.6536 3.53839 6.86781 3.29045 7.13838 3.27248H7.16706C7.45582 3.25644 7.65842 3.43434 7.65888 3.8412Z"
        fill={fill}
      />
      <path
        d="M5.34992 3.84218C5.35049 3.97678 5.29712 4.10744 5.20324 4.2013C5.02277 4.38174 4.69651 4.3899 4.51202 4.22674C4.50528 4.22078 4.49869 4.21459 4.49226 4.20818C4.39495 4.1112 4.34332 3.97244 4.35066 3.8276C4.34464 3.53936 4.55885 3.29143 4.82942 3.27345H4.8581C5.14686 3.25741 5.34946 3.43532 5.34992 3.84218Z"
        fill={fill}
      />
      <path
        d="M4.22602 9.23307C5.1628 8.98566 5.37552 8.11231 5.07913 7.31689C5.01958 7.1356 4.9175 6.97792 4.78363 6.86042C4.00438 6.22829 3.79561 5.29185 3.77271 4.26263C3.7797 4.05108 3.75259 3.84145 3.69272 3.64411C3.63468 3.42139 3.45615 3.27185 3.24965 3.273C3.04486 3.27593 2.86048 3.43125 2.79591 3.65524C2.58283 4.20449 2.66674 4.74384 2.81403 5.27329C2.86783 5.46998 2.95902 5.6543 2.99531 5.8547C3.0316 6.0551 3.04496 6.2889 2.81727 6.39653C2.58959 6.50415 2.48305 6.32354 2.41401 6.15778C1.99853 5.16814 1.85468 4.17851 2.39129 3.12084C2.68661 2.55056 3.11764 2.19553 3.62933 1.92462C3.9449 1.76009 4.26857 1.61165 4.52011 1.32342C4.72466 1.09174 4.84164 0.837711 4.7201 0.52731C4.70825 0.497066 4.67843 0.478516 4.64552 0.478516C4.6288 0.478516 4.61229 0.48342 4.59774 0.492188C4.33118 0.652816 4.03708 0.695267 3.75182 0.749428C2.64203 0.973333 1.7835 1.58443 1.22375 2.7423C0.988722 3.23712 0.840654 3.54886 0.74129 4.07831C0.697692 4.36554 0.684726 4.65631 0.702677 4.94424C0.715134 5.22915 0.741263 5.51244 0.780946 5.79285C1.01987 7.25256 1.61241 8.39559 2.87149 8.94855C3.12781 9.07602 3.39649 9.1714 3.67296 9.23307C3.85427 9.27249 4.04188 9.27249 4.22602 9.23307Z"
        fill={fill}
      />
      <path
        d="M7.44208 9.25771C6.52648 8.94081 6.37979 8.05397 6.73495 7.283C6.80792 7.10668 6.92152 6.95709 7.06382 6.84995C7.88824 6.27799 8.1666 5.35982 8.26656 4.33521C8.27543 4.12374 8.31818 3.91672 8.39267 3.72442C8.46723 3.50668 8.65646 3.37094 8.8623 3.38755C9.0663 3.40583 9.23851 3.57453 9.28612 3.80272C9.45744 4.36639 9.33335 4.89794 9.14681 5.41487C9.07842 5.60698 8.97367 5.78394 8.92247 5.98106C8.87127 6.17818 8.84043 6.41032 9.05941 6.5347C9.27839 6.65909 9.39816 6.48697 9.47943 6.32684C9.9679 5.37112 10.1855 4.39505 9.72965 3.30014C9.47789 2.70934 9.07467 2.32301 8.58472 2.01452C8.28237 1.82681 7.97073 1.65453 7.74149 1.34826C7.55487 1.10191 7.45726 0.839825 7.60172 0.539404C7.6158 0.510132 7.64693 0.493869 7.67974 0.496335C7.69642 0.497588 7.71252 0.503715 7.72637 0.513549C7.98014 0.693699 8.27023 0.758067 8.55063 0.833451C9.64052 1.13988 10.4509 1.8136 10.9223 3.01016C11.1196 3.52119 11.2439 3.84315 11.3033 4.37856C11.3252 4.66825 11.3164 4.95917 11.2769 5.24495C11.2431 5.52812 11.1958 5.80866 11.1352 6.08531C10.7876 7.52301 10.1111 8.61843 8.81412 9.07549C8.54898 9.1834 8.2739 9.25838 7.99359 9.29915C7.80983 9.32488 7.62275 9.31082 7.44208 9.25771Z"
        fill={fill}
      />
      <path
        d="M6.06524 5.06836C6.27313 5.06836 6.5662 5.11981 6.59318 5.51423C6.61403 5.80943 6.26424 6.12669 5.93526 6.13281C5.62665 6.14384 5.28348 5.83761 5.28053 5.54853C5.27759 5.25945 5.5443 5.06836 6.06524 5.06836Z"
        fill={fill}
      />
      <path
        d="M7.65888 3.8412C7.65945 3.9758 7.60608 4.10646 7.5122 4.20032C7.33173 4.38076 7.00547 4.38892 6.82098 4.22576C6.81424 4.2198 6.80765 4.21362 6.80122 4.20721C6.70391 4.11022 6.65228 3.97146 6.65962 3.82662C6.6536 3.53839 6.86781 3.29045 7.13838 3.27248H7.16706C7.45582 3.25644 7.65842 3.43434 7.65888 3.8412Z"
        fill={fill}
      />
      <path
        d="M5.34992 3.84316C5.35049 3.97776 5.29712 4.10841 5.20324 4.20228C5.02277 4.38271 4.69651 4.39087 4.51202 4.22772C4.50528 4.22176 4.49869 4.21557 4.49226 4.20916C4.39495 4.11218 4.34332 3.97341 4.35066 3.82857C4.34464 3.54034 4.55885 3.2924 4.82942 3.27443H4.8581C5.14686 3.25839 5.34946 3.4363 5.34992 3.84316Z"
        fill={fill}
      />
    </svg>
  );
};
