import { SecondaryBody } from 'components/ui/typography';

import { BILLING_CYCLE } from '../helper';

const { cn } = require('lib/utils');

const tabs = [
  { key: BILLING_CYCLE.MONTHLY, label: 'महीना का' },
  { key: BILLING_CYCLE.YEARLY, label: 'साल का', badge: '50% की छूट' },
];

function Tabs({ value, onChange }) {
  return (
    <div className="inline-flex rounded-lg gap-1.5 bg-white p-1 shadow-[0px_1px_2px_0px_#00000033] ">
      {tabs.map((t) => {
        const active = value === t.key;
        return (
          <button
            key={t.key}
            onClick={() => onChange(t.key)}
            className={cn(
              'relative w-[118px] py-2.5 rounded font-semibold transition',
              active ? 'bg-gradient-primary' : 'bg-surface-0',
            )}
          >
            <SecondaryBody
              className={cn(
                active
                  ? 'text-white font-bold'
                  : 'text-text-primary font-medium',
              )}
            >
              {t.label}
            </SecondaryBody>
            {t.badge && (
              <span
                className={
                  'absolute -top-4 right-7 mx-auto text-xs text-white p-1 bg-status-rejected-text rounded font-bold'
                }
              >
                {t.badge}
              </span>
            )}
          </button>
        );
      })}
    </div>
  );
}

export default Tabs;
