import { useState } from 'react';

import CancelAutoPayInfoPopup from 'components/AutoPay/CancelAutoPayInfoPopup';
import { H3, H4, SecondaryBody, SmallBody } from 'components/ui/typography';

import { GoldCrownIcon } from 'modules/home/<USER>';

import {
  CallIcon,
  CattleIcon,
  LockIcon,
  PrimeCattleIcon,
  RestrictedIcon,
  RupeeIcon,
} from '../Icons';

const { cn } = require('lib/utils');

const Icons = {
  restricted: (props) => <RestrictedIcon {...props} />,
  call: (props) => <CallIcon {...props} />,
  cattle: (props) => <CattleIcon {...props} />,
  primeCattle: (props) => <PrimeCattleIcon {...props} />,
  lock: (props) => <LockIcon {...props} />,
  rupee: (props) => <RupeeIcon {...props} />,
};

function PlanCard({ plan, startSubscription }) {
  const [showAutoPayInfoPopup, setShowAutoPayInfoPopup] = useState(false);
  const startAtOffsetMins = plan?.extraInfo?.startAtOffsetMins;
  const trialDays = startAtOffsetMins ? startAtOffsetMins / (60 * 24) : 1;
  const isVipPlan = plan?.extraInfo?.featureInfo?.isVip;
  const isTrialPlan = !!plan?.extraInfo?.trialPlan;
  const planPrice = !!plan?.extraInfo?.trialPlan ? 1 : plan?.price;
  return (
    <div className="relative bg-white mt-6 rounded-lg min-w-[302px] h-[466px] flex flex-col p-4">
      {isVipPlan && (
        <SmallBody
          className={cn(
            'absolute rounded-b bg-secondary-80 px-3 py-0.5 left-6 -top-2.5 font-bold text-xs leading-body text-white',
            'before:content-[""] before:absolute before:-left-[3px] before:top-0 before:w-1.5 before:h-2.5 before:bg-secondary-100 before:-skew-x-[30deg] before:-z-[1]',
            'after:content-[""] after:absolute after:-right-[3px] after:top-0 after:w-1.5 after:h-2.5 after:bg-secondary-100 after:skew-x-[30deg] after:-z-[1]',
          )}
        >
          10 गुना ज़्यादा फ़ायदे वाला प्लान
        </SmallBody>
      )}
      <div className="w-full">
        <div className="flex items-start justify-between">
          <div>
            {isVipPlan ? (
              <div
                className="gap-1 flex justify-center items-center py-1.5 px-2 rounded-md"
                style={{
                  background:
                    'linear-gradient(90deg, #E2B65C 0%, #FFFFFF 100%)',
                }}
              >
                <GoldCrownIcon w="17.5" h="17.5" />
                <H4>VIP User प्लान</H4>
              </div>
            ) : (
              <div
                className="gap-1 flex justify-center items-center py-1.5 px-2 rounded-md"
                style={{
                  background:
                    'linear-gradient(90deg, #D9FFFC 0%, #FFFFFF 100%)',
                }}
              >
                <H4>
                  <span className="text-primary-500 mr-1">NORMAL</span>
                  <span className="text-text-primary">प्लान</span>
                </H4>
              </div>
            )}

            {isTrialPlan && (
              <div className="text-base leading-5 font-medium text-grey-300 mt-1.5">
                {`₹${plan.price}/${
                  plan?.extraInfo?.featureInfo?.billingCycle === 'MONTHLY'
                    ? 'महीना'
                    : 'साल'
                }`}
              </div>
            )}
          </div>
          <div className="text-right">
            <div
              className={cn(
                ' font-bold text-[52px] leading-[52px] flex items-end justify-end',
                isVipPlan ? 'text-text-primary' : 'text-primary-600',
              )}
            >
              {isTrialPlan ? (
                <>
                  ₹{planPrice}
                  <H3 className="ml-1 mb-1 text-inherit">में</H3>
                </>
              ) : (
                <H3 className="ml-1 text-inherit">₹{planPrice}</H3>
              )}
            </div>
            {isTrialPlan ? (
              <div className="text-base leading-5 font-medium text-grey-300">
                {trialDays} दिन का ट्रायल
              </div>
            ) : (
              <SmallBody className="text-right">
                हर{' '}
                {plan?.extraInfo?.featureInfo?.billingCycle === 'MONTHLY'
                  ? 'महीना'
                  : 'साल'}
              </SmallBody>
            )}
          </div>
        </div>

        <ul className="mt-6 space-y-3">
          {plan.features.map((f, i) => (
            <li key={i} className="flex items-center gap-3">
              <span
                className={cn(
                  'h-5 w-5 rounded-full flex justify-center items-center shrink-0',
                  isVipPlan
                    ? 'bg-gradient-prime-golden'
                    : 'bg-gradient-primary',
                )}
              >
                {Icons[f.icon]({ fill: isVipPlan ? '#2E3C4D' : '#FFFFFF' })}
              </span>
              <SecondaryBody>{f.text}</SecondaryBody>
            </li>
          ))}
        </ul>
      </div>

      <div className="mt-auto">
        <button
          className={cn(
            'w-full p-4 mb-4 rounded-md text-xl leading-6 font-bold text-surface-3 flex items-center justify-between px-4',
            isVipPlan ? 'bg-gradient-prime-golden' : 'bg-gradient-primary',
          )}
          onClick={() => startSubscription(plan)}
        >
          <span>प्लान लें</span>
          <span>₹{planPrice}</span>
        </button>
        <SmallBody className="text-grey-300">
          कभी भी{' '}
          <span
            className="font-bold"
            onClick={() => setShowAutoPayInfoPopup(true)}
          >
            कैंसल
          </span>{' '}
          करें
        </SmallBody>
      </div>
      <CancelAutoPayInfoPopup
        show={showAutoPayInfoPopup}
        setShow={setShowAutoPayInfoPopup}
      />
    </div>
  );
}

export default PlanCard;
