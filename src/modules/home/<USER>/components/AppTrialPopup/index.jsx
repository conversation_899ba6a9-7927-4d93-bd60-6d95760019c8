import { useCallback, useContext, useEffect, useMemo } from 'react';

import { useRouter } from 'next/router';

import useSubscription from 'hooks/monetization/useSubscription';
import usePhonepe from 'hooks/usePhonepe';

import { logAmplitudeEvent } from 'lib/log-event';

import { AnimallLoaderContext } from 'components/AnimallLoader';
import useBottomSheet from 'components/Popups/hooks/useBottomSheet';
import UpiDropDown from 'components/UpiDropDown';
import Button from 'components/ui/Button';
import { SecondaryBody } from 'components/ui/typography';

import { MonetizationContext } from 'providers/Monetization';
import { usePaymentConfirmation } from 'providers/PaymentConfirmationProvider';
import { useShowPaywall } from 'providers/PaywallProvider';

import { FEATURES, FEATURE_BENIFITS, FEATURE_INFO } from '../../constant';
import PlanCard from './PlanCard';

const priceText = {
  APP_SUBSCRIPTION_149: 'फिर ₹149/महिना',
  APP_REPEAT_SUBSCRIPTION_149: 'फिर ₹149/महिना',
  APP_SUBSCRIPTION_39: 'फिर ₹39/हफ़्ता',
};

const AppTrialPopup = ({ show, setShow, pageSource, featureName }) => {
  const { onToggle, isOpen, BottomSheet } = useBottomSheet('app-trial-popup');

  // Fallback to a safe default if an unknown featureName is passed
  const featureInfo =
    FEATURE_INFO[featureName] || FEATURE_INFO[FEATURES.AFTER_AD_FREE_PLAN];
  const { subTitle, icon } = featureInfo || {};
  const { setShowSuccessScreen } = useShowPaywall();
  const { closeAnimallLoader } = useContext(AnimallLoaderContext);
  const monetizationStats = useContext(MonetizationContext);
  const plan = useMemo(
    () => monetizationStats?.app?.plans?.[0],
    [monetizationStats],
  );

  const togglePopup = () => {
    onToggle();
    setShow((prev) => !prev);
  };

  useEffect(() => {
    if (show) {
      onToggle();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [show]);

  useEffect(() => {
    if (!isOpen && show) {
      setShow(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  useEffect(() => {
    if (!isOpen) {
      return;
    }

    logAmplitudeEvent('VIEWED', 'APPSUBSCRIPTION', 'POPUP', {
      PAGE: pageSource,
      SECTION: featureName,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  const { isPhonepeSupported } = usePhonepe();
  const trialPrice = isPhonepeSupported ? 2 : 1;
  const planPrice = !!plan?.extraInfo?.trialPlan ? trialPrice : plan?.price;

  const displayPrice = planPrice;

  const { showSuccessPopup, showErrorPopup } = usePaymentConfirmation();
  const { startSubscription, setSelectedPackage } = useSubscription({
    source: pageSource,
    onSubscriptionSuccess: () => {
      closeAnimallLoader();
      togglePopup();
      if (!!plan?.extraInfo?.trialPlan) {
        setShowSuccessScreen(true);
      } else {
        showSuccessPopup({
          amount: displayPrice,
          onOk: () => {
            window.location.reload();
          },
          onClose: () => {
            window.location.reload();
          },
        });
      }
    },
    onSubscriptionFailure: () => {
      togglePopup();
      closeAnimallLoader();
      showErrorPopup();
    },
  });

  const onSubscription = useCallback(() => {
    startSubscription(plan);
  }, [startSubscription, plan]);

  const featureBenefits = FEATURE_BENIFITS.slice(0, 2);
  featureBenefits.push(priceText[plan?.name] || `फिर ₹${plan?.price}/महिना`);
  const startAtOffsetMins = plan?.extraInfo?.startAtOffsetMins;
  const trialDays = startAtOffsetMins ? startAtOffsetMins / (60 * 24) : 1;
  return (
    <BottomSheet
      isOpen={isOpen}
      onClose={togglePopup}
      className="py-6 px-4 bg-gradient-to-b from-[#FFE8E8] via-[#EEFEFC] to-white  rounded-t-[32px] shadow-lg"
    >
      <div className="flex flex-col items-center gap-6">
        {/* title */}
        <div className="flex justify-center items-center w-[52px] h-[52px] -mb-3 bg-white rounded-full">
          {icon}
        </div>
        <SecondaryBody className="font-medium font-mukta text-base text-text-primary">
          {subTitle}
        </SecondaryBody>

        <PlanCard
          title={featureInfo.title}
          benefits={featureBenefits}
          isTrial={!!plan?.extraInfo?.trialPlan}
          price={displayPrice}
          trialDays={trialDays}
        />

        <div className="w-full flex flex-row gap-4 h-full">
          <UpiDropDown onChange={setSelectedPackage} />
          <Button
            size="xl"
            textClassName="w-full flex justify-between items-center text-surface-3"
            onClick={onSubscription}
            id="GROWTH_PLAN_PAYMENT_CTA_MAIN"
          >
            <span>प्लान लें</span>
            <span>₹{displayPrice}</span>
          </Button>
        </div>
      </div>
    </BottomSheet>
  );
};

export default AppTrialPopup;
