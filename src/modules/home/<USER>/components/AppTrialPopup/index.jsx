import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import useSubscription from 'hooks/monetization/useSubscription';
import usePhonepe from 'hooks/usePhonepe';

import { logAmplitudeEvent } from 'lib/log-event';
import { getCookie } from 'lib/storage';
import { cn, isInBuckets } from 'lib/utils';

import { AnimallLoaderContext } from 'components/AnimallLoader';
import useBottomSheet from 'components/Popups/hooks/useBottomSheet';
import UpiDropDown from 'components/UpiDropDown';
import Button from 'components/ui/Button';
import { SecondaryBody } from 'components/ui/typography';

import { MonetizationContext } from 'providers/Monetization';
import { usePaymentConfirmation } from 'providers/PaymentConfirmationProvider';
import { useShowPaywall } from 'providers/PaywallProvider';

import { FEATURES, FEATURE_INFO } from '../../constant';
import { BILLING_CYCLE } from '../MultiPlanAppTrial/helper';
import PlanCard from './PlanCard';

const priceText = {
  APP_SUBSCRIPTION_149: 'फिर ₹149/महिना',
  APP_REPEAT_SUBSCRIPTION_149: 'फिर ₹149/महिना',
  APP_SUBSCRIPTION_39: 'फिर ₹39/हफ़्ता',
};

const AppTrialPopup = ({ show, setShow, pageSource, featureName }) => {
  const { onToggle, isOpen, BottomSheet } = useBottomSheet('app-trial-popup');

  const { setShowSuccessScreen } = useShowPaywall();
  const { closeAnimallLoader } = useContext(AnimallLoaderContext);
  const monetizationStats = useContext(MonetizationContext);
  const isX4_X5User = isInBuckets([40, 59], getCookie('bucketId'));

  const scrollRef = useRef(null);

  const tab = BILLING_CYCLE.MONTHLY;
  const filteredPlans = useMemo(() => {
    const allPlans = monetizationStats?.app?.plans ?? [];
    let plans = [];
    if (isX4_X5User) {
      plans = allPlans.filter(
        (p) => p?.extraInfo?.featureInfo?.billingCycle === tab,
      );
    } else {
      plans = allPlans.slice(0, 1);
    }

    return plans.length ? plans : allPlans.slice(0, 1);
  }, [monetizationStats, tab, isX4_X5User]);

  const [selectedPlan, setSelectedPlan] = useState(filteredPlans?.[0]);

  // Fallback to a safe default if an unknown featureName is passed
  const featureInfo =
    FEATURE_INFO(featureName, selectedPlan) ||
    FEATURE_INFO(FEATURES.AFTER_AD_FREE_PLAN, selectedPlan);
  const { subTitle, icon } = featureInfo || {};

  const togglePopup = () => {
    onToggle();
    setShow((prev) => !prev);
  };

  useEffect(() => {
    if (show) {
      onToggle();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [show]);

  useEffect(() => {
    if (!isOpen && show) {
      setShow(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  useEffect(() => {
    if (!isOpen) {
      return;
    }

    logAmplitudeEvent('VIEWED', 'APPSUBSCRIPTION', 'POPUP', {
      PAGE: pageSource,
      SECTION: featureName,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  const { isPhonepeSupported } = usePhonepe();
  const trialPrice = isPhonepeSupported ? 2 : 1;
  const planPrice = !!selectedPlan?.extraInfo?.trialPlan
    ? trialPrice
    : selectedPlan?.price;

  const displayPrice = planPrice;

  const { showSuccessPopup, showErrorPopup } = usePaymentConfirmation();
  const { startSubscription, setSelectedPackage } = useSubscription({
    source: pageSource,
    onSubscriptionSuccess: () => {
      closeAnimallLoader();
      togglePopup();
      if (!!selectedPlan?.extraInfo?.trialPlan) {
        setShowSuccessScreen(true);
      } else {
        showSuccessPopup({
          amount: displayPrice,
          onOk: () => {
            window.location.reload();
          },
          onClose: () => {
            window.location.reload();
          },
        });
      }
    },
    onSubscriptionFailure: () => {
      togglePopup();
      closeAnimallLoader();
      showErrorPopup();
    },
  });
  useEffect(() => {
    if (scrollRef.current) {
      scrollRef.current.scrollTo({
        left: 0,
        behavior: 'smooth',
      });
    }
  }, [tab]);

  const onSubscription = useCallback(() => {
    startSubscription(selectedPlan);
  }, [startSubscription, selectedPlan]);

  const featureBenefits = (plan) => {
    const extraInfo = plan.extraInfo || {};
    const featureInfo = extraInfo.featureInfo || {};
    const { dailyCwaLimit, billingCycle, listingLimit, cashbackCoins, isVip } =
      featureInfo;

    const features = [
      {
        icon: 'cart',
        text: `सारे पशु ${isVip ? '2-3' : '10-15'} दिन में बिक जाएँगे`,
      },

      {
        icon: 'cattle',
        text: isX4_X5User
          ? `महीने में ${listingLimit} पशु दर्ज कर सकेंगे`
          : 'जितने भी पशु बेचें, खरीदें',
      },
      isVip
        ? {
            icon: 'animall',
            text: 'सारे पशु बनेंगे प्राइम पशु',
          }
        : null,
      {
        icon: 'rupee',
        text: `${cashbackCoins ?? plan.price} कॉइन्स मिलेंगे`,
      },
      {
        icon: 'rupee',
        text: `फिर ₹${plan.price}/महिना`,
      },
    ];
    return features.filter(Boolean);
  };

  const startAtOffsetMins = selectedPlan?.extraInfo?.startAtOffsetMins;
  const trialDays = startAtOffsetMins ? startAtOffsetMins / (60 * 24) : 1;
  return (
    <BottomSheet
      isOpen={isOpen}
      onClose={togglePopup}
      className="py-6 px-4 bg-gradient-to-b from-[#FFE8E8] via-[#EEFEFC] to-white  rounded-t-[32px] shadow-lg"
    >
      <div className="flex flex-col items-center gap-6">
        {/* title */}
        <div className="flex justify-center items-center w-[52px] h-[52px] -mb-3 bg-white rounded-full">
          {icon}
        </div>
        <SecondaryBody className="font-medium font-mukta text-base text-text-primary">
          {subTitle}
        </SecondaryBody>
        <div
          className=" w-full overflow-x-auto overflow-y-visible flex justify-between gap-4"
          ref={scrollRef}
        >
          {filteredPlans.map((p, index) => (
            <PlanCard
              key={index}
              title={FEATURE_INFO(featureName, p)?.title}
              benefits={featureBenefits(p)}
              isTrial={!!selectedPlan?.extraInfo?.trialPlan}
              price={displayPrice}
              trialDays={trialDays}
              plan={p}
              isSelected={p?.name === selectedPlan?.name}
              setSelectedPlan={setSelectedPlan}
            />
          ))}
        </div>
        <div className="w-full flex flex-row gap-4 h-full">
          <UpiDropDown onChange={setSelectedPackage} />
          <Button
            size="xl"
            textClassName={cn(
              'w-full flex justify-between items-center text-surface-3',
              selectedPlan?.extraInfo?.featureInfo?.isVip && 'text-black',
            )}
            className={cn(
              'shine',
              selectedPlan?.extraInfo?.featureInfo?.isVip && 'bg-[#E2B65C]',
            )}
            onClick={onSubscription}
            id="GROWTH_PLAN_PAYMENT_CTA_MAIN"
          >
            <span>प्लान लें</span>
            <span>₹{displayPrice}</span>
          </Button>
        </div>
      </div>
    </BottomSheet>
  );
};

export default AppTrialPopup;
