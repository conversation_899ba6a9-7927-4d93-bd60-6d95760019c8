import { cn } from 'lib/utils';

import {
  H1,
  H4,
  PrimaryBody,
  SecondaryBody,
  TertiaryBody,
} from 'components/ui/typography';

import { Tick } from 'modules/buy/components/VipBuyerPopup/icons';
import { GoldCrownIcon } from 'modules/home/<USER>';
import AddCartIcon from 'modules/prime/icons/AddCart';
import AnimallIcon from 'modules/prime/icons/AnimallIcon';

import {
  CallIcon,
  CattleIcon,
  LockIcon,
  PrimeCattleIcon,
  RestrictedIcon,
  RupeeIcon,
} from '../../components/MultiPlanAppTrial/Icons';

const Icons = {
  restricted: (props) => <RestrictedIcon {...props} />,
  call: (props) => <CallIcon {...props} />,
  cattle: (props) => <CattleIcon {...props} />,
  primeCattle: (props) => <PrimeCattleIcon {...props} />,
  lock: (props) => <LockIcon {...props} />,
  rupee: (props) => <RupeeIcon {...props} />,
  animall: (props) => <AnimallIcon {...props} />,
  cart: (props) => <AddCartIcon {...props} />,
};
const PlanCard = ({
  title,
  benefits = [],
  isTrial,
  price,
  trialDays,
  plan,
  isSelected = false,
  setSelectedPlan,
}) => {
  const isVipPlan = plan?.extraInfo?.featureInfo?.isVip;

  return (
    <div
      className={cn(
        'px-4 py-3 border-[3px]  w-full rounded-lg relative min-w-[290px]',
        isSelected && 'border-navActive',
        isVipPlan && isSelected && 'border-[#E2B65C]',
      )}
      style={{
        background:
          'linear-gradient(109.74deg, #FFFFFF 0.66%, #FFFFFF 46.36%, #FFFFFF 100%)',
      }}
      onClick={() => setSelectedPlan(plan)}
    >
      <div className="flex items-center justify-between">
        {/* Radio input */}
        <PrimaryBody
          className={cn(
            'bg-gradient-to-r from-[#D9FFFC] to-white p-2 rounded-md gap-1',
            isVipPlan && 'from-[#E2B65C] to-white flex gap-2',
          )}
        >
          {isVipPlan && <GoldCrownIcon w={22} h={22} />}
          {title}
        </PrimaryBody>
        <div className="flex items-center justify-center">
          <span
            className={cn(
              'w-5 h-5 rounded-full border-2  flex justify-center items-center border-grey-200',
              isSelected && ' border-primary-600',
              isVipPlan && isSelected && 'border-[#E2B65C]',
            )}
          >
            <span
              className={cn(
                'w-3 h-3 rounded-full ',
                isSelected && 'bg-primary-600',
                isVipPlan && isSelected && 'bg-[#E2B65C]',
              )}
            ></span>
          </span>
          <input
            className="sr-only"
            type="radio"
            name="plan"
            checked={true}
            readOnly
          />
        </div>
      </div>
      <div className="flex justify-between mt-2 min-h-[150px]">
        <div className="flex justify-between">
          <div className="mt-2">
            <div className="font-mukta font-normal text-sm text-vipPage-surface-2 w-fit">
              साथ में:
            </div>
            <ul className="mt-2 space-y-1 w-fit">
              {benefits.map((f, i) => (
                <li key={i} className="flex items-center gap-1">
                  <span
                    className={cn(
                      'h-4 w-4 rounded-full flex justify-center items-center shrink-0',
                      isVipPlan
                        ? 'bg-gradient-prime-golden'
                        : 'bg-gradient-primary',
                    )}
                  >
                    {Icons[f.icon]({ fill: isVipPlan ? '#2E3C4D' : '#FFFFFF' })}
                  </span>
                  <TertiaryBody>{f.text}</TertiaryBody>
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div className=" flex flex-col justify-end">
          <div className="flex gap-1 items-baseline justify-end">
            <H1>₹{price}</H1>
            <H4>में</H4>
          </div>
          {isTrial && (
            <p className="text-text-secondary font-bold font-mukta text-xs">
              {trialDays || 1} दिन का ट्रायल
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default PlanCard;
