import { cn } from 'lib/utils';

import {
  CallIcon,
  GallaryIcon,
  SearchIcon,
  SearchIconWithBg,
} from 'modules/home/<USER>';
import { POST_STATE_ENUM } from 'modules/user/components/seller-dashboard/constants';

const Resource = ({ post, className, children }) => {
  const getSortedResources = (resources) => {
    return resources?.sort((a, b) => {
      if (a.tag === 'PRIME') return -1;
      if (a.isVideo) return -1;
      return 1;
    });
  };

  const resource = getSortedResources(post?.resources)?.[0];
  return (
    <div className=" h-fit w-fit relative ">
      {!post?.resources?.length || !resource?.url ? (
        <div
          className={cn(
            'flex bg-surface-0 justify-center items-center w-[50px] h-[38px] rounded flex-shrink-0',
            className,
          )}
        >
          <GallaryIcon />
        </div>
      ) : (
        <div
          className={cn(
            'relative w-[50px] h-[38px] rounded flex-shrink-0',
            className,
          )}
        >
          {resource?.thumb ? (
            <img
              src={`https://animall.b-cdn.net/${resource.thumb}`}
              alt={`post-${post._id}`}
              loading="lazy"
              className="w-full h-full object-cover rounded"
            />
          ) : resource?.isVideo ? (
            <video
              src={`${resource.url}#t=0.1`}
              className="w-full h-full object-cover rounded"
            />
          ) : (
            <img
              src={resource?.url}
              alt={`post-${post._id}`}
              loading="lazy"
              className="w-full h-full object-cover rounded-md"
            />
          )}
        </div>
      )}
      {children}
    </div>
  );
};

export default Resource;
