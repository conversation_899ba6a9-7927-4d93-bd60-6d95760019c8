export const FireIcon = ({ w = 16, h = 17, fill = 'white' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.44499 5.59124C4.37374 6.35124 4.32374 7.69624 4.77249 8.26874C4.77249 8.26874 4.56124 6.79124 6.45499 4.93749C7.21748 4.19124 7.39374 3.17624 7.12749 2.41499C6.97624 1.98374 6.69999 1.62749 6.45999 1.37874C6.31999 1.23249 6.42748 0.991237 6.63124 0.999987C7.86374 1.05499 9.86123 1.39749 10.71 3.52749C11.0825 4.46249 11.11 5.42874 10.9325 6.41124C10.82 7.03874 10.42 8.43374 11.3325 8.60499C11.9837 8.72749 12.2987 8.20999 12.44 7.83749C12.4987 7.68249 12.7025 7.64374 12.8125 7.76749C13.9125 9.01874 14.0062 10.4925 13.7787 11.7612C13.3387 14.2137 10.855 15.9987 8.38749 15.9987C5.30499 15.9987 2.85124 14.235 2.21499 11.0425C1.95874 9.75374 2.08874 7.20374 4.07624 5.40374C4.22374 5.26874 4.46499 5.38874 4.44499 5.59124Z"
        fill="url(#paint0_radial_2429_9671)"
      />
      <path
        d="M9.51377 10.1775C8.37752 8.71501 8.88627 7.04626 9.16502 6.38126C9.20252 6.29376 9.10252 6.21126 9.02377 6.26501C8.53502 6.59751 7.53377 7.38001 7.06752 8.48126C6.43627 9.97001 6.48127 10.6988 6.85502 11.5888C7.08002 12.125 6.81877 12.2388 6.68752 12.2588C6.56002 12.2788 6.44252 12.1938 6.34877 12.105C6.07912 11.846 5.88694 11.5171 5.79377 11.155C5.77377 11.0775 5.67252 11.0563 5.62627 11.12C5.27627 11.6038 5.09502 12.38 5.08627 12.9288C5.05877 14.625 6.46002 16 8.15502 16C10.2913 16 11.8475 13.6375 10.62 11.6625C10.2638 11.0875 9.92877 10.7113 9.51377 10.1775Z"
        fill="url(#paint1_radial_2429_9671)"
      />
      <defs>
        <radialGradient
          id="paint0_radial_2429_9671"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(7.77696 16.0376) rotate(-179.751) scale(8.82346 14.4775)"
        >
          <stop offset="0.314" stopColor="#FF9800" />
          <stop offset="0.662" stopColor="#FF6D00" />
          <stop offset="0.972" stopColor="#F44336" />
        </radialGradient>
        <radialGradient
          id="paint1_radial_2429_9671"
          cx="0"
          cy="0"
          r="1"
          gradientUnits="userSpaceOnUse"
          gradientTransform="translate(8.27252 7.25731) rotate(90.5787) scale(9.23205 6.94781)"
        >
          <stop offset="0.214" stopColor="#FFF176" />
          <stop offset="0.328" stopColor="#FFF27D" />
          <stop offset="0.487" stopColor="#FFF48F" />
          <stop offset="0.672" stopColor="#FFF7AD" />
          <stop offset="0.793" stopColor="#FFF9C4" />
          <stop offset="0.822" stopColor="#FFF8BD" stopOpacity="0.804" />
          <stop offset="0.863" stopColor="#FFF6AB" stopOpacity="0.529" />
          <stop offset="0.91" stopColor="#FFF38D" stopOpacity="0.209" />
          <stop offset="0.941" stopColor="#FFF176" stopOpacity="0" />
        </radialGradient>
      </defs>
    </svg>
  );
};
export const LocationIcon = ({ w = 7, h = 12, fill = 'white' }) => {
  return (
    <svg
      width="12"
      height="16"
      viewBox="0 0 12 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6 0C2.692 0 0 2.71067 0 6.04333C0 10.7787 5.436 15.668 5.66733 15.8733C5.76267 15.958 5.88133 16 6 16C6.11867 16 6.23733 15.958 6.33267 15.874C6.564 15.668 12 10.7787 12 6.04333C12 2.71067 9.308 0 6 0ZM6 9.33333C4.162 9.33333 2.66667 7.838 2.66667 6C2.66667 4.162 4.162 2.66667 6 2.66667C7.838 2.66667 9.33333 4.162 9.33333 6C9.33333 7.838 7.838 9.33333 6 9.33333Z"
        fill="#14776F"
      />
    </svg>
  );
};
export const TopPashuIcon = ({ w = 16, h = 17, fill = 'white' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2429_9707)">
        <path
          d="M0.477938 8.32312L1.57089e-08 8.52575C-3.12343e-05 8.64687 0.0465625 8.76709 0.137469 8.85765L7.67019 16.364C7.75809 16.4517 7.90909 16.4997 7.99775 16.5001C8.08781 16.5005 8.24184 16.4531 8.33128 16.3639L15.8627 8.85284C15.9534 8.7624 15.9999 8.64249 16.0001 8.52159L15.5845 8.32315C15.3528 8.32315 13.2006 8.32315 10.5856 8.32315L7.78156 9.22831L5.41003 8.32315C2.95019 8.32312 0.901375 8.32312 0.477938 8.32312Z"
          fill="#FFB739"
        />
        <path
          d="M15.9999 8.52162C15.9999 8.6424 15.9534 8.76228 15.8625 8.85278L8.33116 16.3639C8.24253 16.4525 8.08987 16.5 8 16.5V9.15771L10.5856 8.32312H15.5845L15.9999 8.52162Z"
          fill="#FF9441"
        />
        <path
          d="M5.40997 8.32312L8 16.5L10.5856 8.32312H5.40997Z"
          fill="#FF9441"
        />
        <path d="M10.5856 8.32312L8 16.5V8.32312H10.5856Z" fill="#FF7149" />
        <path
          d="M15.9215 8.26132L13.4144 4.50497C13.3797 4.45304 13.335 4.40891 13.2835 4.37485L13.0563 4.48397L8.43372 4.51279L8 4.29669L7.6495 4.51766L3.03734 4.54641L2.7155 4.37532C2.66441 4.40929 2.61987 4.45325 2.58525 4.50522L0.0783437 8.26629C0.0255625 8.34547 0 8.43582 0 8.52566C0.908156 8.52541 14.8271 8.52175 16 8.52144C16.0001 8.43135 15.9744 8.34069 15.9215 8.26132Z"
          fill="#FFD17E"
        />
        <path
          d="M15.9999 8.5216C15.4187 8.5216 11.707 8.52285 8 8.52378V4.29675L8.43384 4.51272L13.0563 4.484L13.2835 4.37475C13.335 4.4091 13.3797 4.4531 13.4143 4.50491L15.9216 8.26125C15.9746 8.34088 16.0002 8.43138 15.9999 8.5216Z"
          fill="#FFB739"
        />
        <path
          d="M2.97476 4.29675C2.88114 4.29675 2.79133 4.32491 2.71558 4.37532L5.49233 8.52428H5.49264L7.99983 4.29675H2.97476Z"
          fill="#FFB739"
        />
        <path
          d="M10.5082 8.52294L13.2834 4.37482C13.2078 4.32472 13.1183 4.29675 13.0249 4.29675H7.99982L10.5062 8.52297H10.5082V8.52294Z"
          fill="#FF9441"
        />
        <path
          d="M5.20074 11.7912C4.41715 11.7912 3.77961 11.1537 3.77961 10.3701C3.77961 10.1112 3.56974 9.90137 3.31086 9.90137C3.05199 9.90137 2.84211 10.1112 2.84211 10.3701C2.84211 11.1498 2.20846 11.7912 1.42102 11.7912C1.16215 11.7912 0.952271 12.0011 0.952271 12.26C0.952271 12.5188 1.16215 12.7287 1.42102 12.7287C2.20461 12.7287 2.84211 13.3662 2.84211 14.1498C2.84211 14.4087 3.05199 14.6186 3.31086 14.6186C3.56974 14.6186 3.77961 14.4087 3.77961 14.1498C3.77961 13.3702 4.41321 12.7287 5.20074 12.7287C5.45961 12.7287 5.66949 12.5188 5.66949 12.26C5.66949 12.0011 5.45961 11.7912 5.20074 11.7912Z"
          fill="#FFD17E"
        />
        <path
          d="M12.3853 2.38984C11.6017 2.38984 10.9642 1.75234 10.9642 0.96875C10.9642 0.709875 10.7543 0.5 10.4954 0.5C10.2366 0.5 10.0267 0.709875 10.0267 0.96875C10.0267 1.75234 9.38922 2.38984 8.60559 2.38984C8.34672 2.38984 8.13684 2.59972 8.13684 2.85859C8.13684 3.11747 8.34672 3.32734 8.60559 3.32734C9.38918 3.32734 10.0267 3.96484 10.0267 4.74844C10.0267 5.00731 10.2366 5.21719 10.4954 5.21719C10.7543 5.21719 10.9642 5.00731 10.9642 4.74844C10.9642 3.96484 11.6017 3.32734 12.3853 3.32734C12.6442 3.32734 12.854 3.11747 12.854 2.85859C12.854 2.59972 12.6442 2.38984 12.3853 2.38984Z"
          fill="#FFB739"
        />
      </g>
      <defs>
        <clipPath id="clip0_2429_9707">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
export const BackArrowIcon = ({ w = 7, h = 12, fill = 'white' }) => {
  return (
    <svg
      width="17"
      height="10"
      viewBox="0 0 17 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0 5C6.86244e-08 5.20635 0.0850164 5.40358 0.235033 5.54526L4.73503 9.79526C5.03617 10.0797 5.51085 10.0661 5.79526 9.76497C6.07967 9.46383 6.06611 8.98915 5.76497 8.70474L2.63642 5.75H15.75C16.1642 5.75 16.5 5.41422 16.5 5C16.5 4.58579 16.1642 4.25 15.75 4.25H2.63642L5.76497 1.29526C6.06611 1.01085 6.07967 0.536173 5.79526 0.235034C5.51085 -0.0661045 5.03617 -0.0796666 4.73503 0.204742L0.235033 4.45474C0.0850165 4.59643 -6.86243e-08 4.79366 0 5Z"
        fill="#2E3C4D"
      />
    </svg>
  );
};
export const TopBuyerAndSellerIcon = ({ w = 7, h = 12, fill = 'white' }) => {
  return (
    <svg
      width={w}
      height={h}
      viewBox="0 0 16 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_2429_9743)">
        <path
          d="M11.6129 7.59673V7.77782C11.6129 7.99973 11.6487 8.22017 11.7189 8.43067L12.129 9.66123H12.9032V6.82251H12.3871C11.9595 6.82254 11.6129 7.16913 11.6129 7.59673Z"
          fill="#5A4146"
        />
        <path
          d="M15.1464 8.13283L14.7097 9.66126L12.6452 7.85483C12.3601 7.85483 12.129 7.62373 12.129 7.3387C12.129 6.91114 12.4757 6.56451 12.9032 6.56451H14.7097C14.9947 6.56451 15.2258 6.79561 15.2258 7.08064V7.5657C15.2258 7.75748 15.1991 7.94836 15.1464 8.13283Z"
          fill="#694B4B"
        />
        <path
          d="M14.1934 10.4356H12.645V11.5778H14.1934V10.4356Z"
          fill="#E6AF78"
        />
        <path
          d="M12.6451 10.9095C12.8845 11.0068 13.1455 11.0615 13.4193 11.0615C13.6932 11.0615 13.9542 11.0068 14.1935 10.9095V10.4355H12.6451V10.9095Z"
          fill="#D29B6E"
        />
        <path
          d="M15.4385 11.4172L14.1935 11.0616L13.4194 11.5753L12.6452 11.0615L11.4002 11.4172C11.0679 11.5122 10.8387 11.816 10.8387 12.1616V14.0484C10.8387 14.1909 10.9543 14.3065 11.0968 14.3065H15.742C15.8845 14.3065 16 14.1909 16 14.0484V12.1616C16 11.816 15.7709 11.5122 15.4385 11.4172Z"
          fill="#D5DCED"
        />
        <path
          d="M13.6774 14.3064H13.1613L13.2903 11.5776H13.5483L13.6774 14.3064Z"
          fill="#AFB9D2"
        />
        <path
          d="M13.4193 10.6935C12.5642 10.6935 11.871 10.0003 11.871 9.14515V8.8428C11.871 8.70593 11.9253 8.57465 12.0221 8.47783L12.4901 8.00987C12.5892 7.91077 12.7247 7.8538 12.8648 7.85821C13.6897 7.88424 14.4199 8.06096 14.819 8.4508C14.9168 8.54624 14.9678 8.67974 14.9678 8.8163V9.14515C14.9678 10.0003 14.2745 10.6935 13.4193 10.6935Z"
          fill="#F0C087"
        />
        <path
          d="M12.6452 8.90683C12.6452 8.60915 12.8952 8.37252 13.1923 8.39146C13.7132 8.42471 14.4863 8.51177 14.9606 8.7399C14.9451 8.63102 14.8983 8.52824 14.819 8.4508C14.4199 8.06096 13.6897 7.88424 12.8648 7.85821L12.8647 7.85824C12.7247 7.85383 12.5892 7.9108 12.4901 8.00987L12.0222 8.4778C11.9253 8.57458 11.871 8.70586 11.871 8.84277V9.14512C11.871 9.85124 12.3441 10.4458 12.9903 10.632C12.7753 10.3666 12.6452 10.0297 12.6452 9.66124L12.6452 8.90683Z"
          fill="#E6AF78"
        />
        <path
          d="M14.9678 12.9306C14.9678 12.7252 15.0493 12.5283 15.1945 12.3832L15.8588 11.7189C15.9476 11.8458 16 11.9986 16 12.1617V14.0484C16 14.1909 15.8845 14.3065 15.742 14.3065H14.9678V12.9306Z"
          fill="#C7CFE2"
        />
        <path
          d="M13.5483 12.0938H13.2903C13.219 12.0938 13.1613 12.036 13.1613 11.9647V11.5776H13.6774V11.9647C13.6774 12.036 13.6196 12.0938 13.5483 12.0938Z"
          fill="#959CB5"
        />
        <path
          d="M13.4193 11.5753L12.9819 11.9094C12.9066 11.9669 12.7976 11.9439 12.7521 11.8607L12.3597 11.1441L12.5177 10.9032C12.5527 10.8498 12.6274 10.8407 12.6742 10.8841L13.4193 11.5753Z"
          fill="#C7CFE2"
        />
        <path
          d="M13.4193 11.5753L13.8567 11.9094C13.9321 11.967 14.041 11.9439 14.0865 11.8607L14.4789 11.1442L14.321 10.9033C14.2859 10.8498 14.2113 10.8407 14.1644 10.8842L13.4193 11.5753Z"
          fill="#C7CFE2"
        />
        <path
          d="M4.61945 10.5858C4.39892 10.0015 4.22401 8.99701 4.18623 8.47404C4.11435 7.47885 3.36439 6.6676 2.35057 6.6676C1.33676 6.6676 0.586792 7.47888 0.514917 8.47404C0.477136 8.99698 0.30223 10.0015 0.0816984 10.5858C0.0331047 10.7146 0.0891984 10.8566 0.216042 10.9141C0.452355 11.0212 0.956198 11.231 1.56385 11.3128H3.13726C3.74207 11.2309 4.24945 11.0209 4.48507 10.9141C4.61195 10.8566 4.66804 10.7146 4.61945 10.5858Z"
          fill="#5A4146"
        />
        <path
          d="M4.48505 10.9141C4.61187 10.8566 4.66799 10.7146 4.61937 10.5858C4.39887 10.0015 4.22393 8.99701 4.18618 8.47404C4.11434 7.47885 3.36434 6.6676 2.35055 6.6676C2.34799 6.6676 2.34546 6.6676 2.3429 6.66763C1.56499 6.67079 1.29009 7.7317 1.95974 8.12754C1.99996 8.15132 2.02574 8.16276 2.02574 8.16276L2.61227 11.3128H3.13724C3.74205 11.2308 4.24943 11.0209 4.48505 10.9141Z"
          fill="#694B4B"
        />
        <path
          d="M4.21719 11.8309L3.38206 11.4133C3.20719 11.3259 3.09672 11.1471 3.09675 10.9516L3.09681 10.1774H1.54838V10.9517C1.54838 11.1472 1.43794 11.3259 1.26306 11.4133L0.427938 11.8309C0.165656 11.962 0 12.2301 0 12.5233V14.0483C0 14.1908 0.115531 14.3064 0.258063 14.3064H4.38709C4.52962 14.3064 4.64516 14.1908 4.64516 14.0483V12.5233C4.64516 12.2301 4.47947 11.962 4.21719 11.8309Z"
          fill="#E6AF78"
        />
        <path
          d="M2.32258 11.2097C2.60046 11.2097 2.86661 11.1524 3.11061 11.0504C3.10421 11.0179 3.09674 10.9856 3.09674 10.9516L3.0968 10.1774H1.54836V10.9517C1.54836 10.9858 1.54089 11.0183 1.53442 11.0509C1.77858 11.1524 2.04461 11.2097 2.32258 11.2097Z"
          fill="#D29B6E"
        />
        <path
          d="M4.21719 11.8309L3.62225 11.5334C3.34637 11.9594 2.86791 12.2419 2.32259 12.2419C1.77728 12.2419 1.29878 11.9594 1.02291 11.5334L0.427969 11.8309C0.165688 11.962 0 12.2301 0 12.5233V14.0484C0 14.1909 0.115531 14.3064 0.258063 14.3064H4.38709C4.52962 14.3064 4.64516 14.1909 4.64516 14.0484V12.5233C4.64516 12.2301 4.47947 11.962 4.21719 11.8309Z"
          fill="#D5DCED"
        />
        <path
          d="M2.32261 10.6935C1.53536 10.6935 0.885363 10.106 0.787081 9.34555C0.772831 9.23537 0.8258 9.12734 0.924988 9.0773C1.0438 9.01737 1.21646 8.9163 1.38177 8.7688C1.56586 8.60455 1.67386 8.43134 1.73486 8.30418C1.78739 8.19468 1.90814 8.13274 2.02583 8.16277C2.93446 8.39452 3.55305 8.86724 3.77749 9.06024C3.83774 9.11205 3.87246 9.18846 3.86627 9.26771C3.80374 10.0655 3.13652 10.6935 2.32261 10.6935Z"
          fill="#F0C087"
        />
        <path
          d="M3.77745 9.06024C3.55304 8.86724 2.93442 8.39452 2.02579 8.16277C1.9081 8.13274 1.78735 8.19468 1.73482 8.30418C1.68864 8.40049 1.61229 8.52318 1.49901 8.64843C1.49898 8.64874 1.49889 8.64899 1.49885 8.6493C1.4637 8.68915 1.42626 8.72909 1.38176 8.76884C1.21645 8.91637 1.04379 9.0174 0.92498 9.07734C0.825792 9.12737 0.772855 9.23543 0.787073 9.34562C0.875355 10.0286 1.40979 10.5722 2.08785 10.6759C1.77889 10.4657 1.54842 10.1766 1.54842 9.66134V9.29334C1.60704 9.25005 1.66607 9.2069 1.72539 9.15396C1.86882 9.02596 1.99404 8.87793 2.09445 8.71855C2.78954 8.93546 3.26079 9.29674 3.43945 9.45037C3.48835 9.49324 3.60973 9.60512 3.75173 9.73834C3.81282 9.5913 3.85323 9.43355 3.8662 9.26777C3.87242 9.18849 3.8377 9.11209 3.77745 9.06024Z"
          fill="#E6AF78"
        />
        <path
          d="M0.157312 12.0581C0.0579062 12.19 0 12.3519 0 12.5233V14.0484C0 14.1909 0.115531 14.3064 0.258063 14.3064H1.03225V13.0061C1.03225 12.8493 0.960969 12.701 0.838531 12.6031L0.157312 12.0581Z"
          fill="#C7CFE2"
        />
        <path
          d="M11.7076 11.4787L9.54838 10.6935L8.00001 11.2096L6.45163 10.6935L4.29241 11.4787C3.88448 11.627 3.61292 12.0147 3.61292 12.4488V14.0484C3.61292 14.1909 3.72845 14.3064 3.87098 14.3064H12.129C12.2716 14.3064 12.3871 14.1909 12.3871 14.0484V12.4488C12.3871 12.0147 12.1156 11.627 11.7076 11.4787Z"
          fill="#FF507D"
        />
        <path
          d="M7.72967 11.7258L7.48389 14.3064H8.51614L8.27036 11.7258H7.72967Z"
          fill="#707487"
        />
        <path
          d="M8.27099 12.0803H7.72912C7.62934 12.0803 7.54846 11.9995 7.54846 11.8997V11.2097H8.45162V11.8997C8.45165 11.9994 8.37077 12.0803 8.27099 12.0803Z"
          fill="#5B5D6E"
        />
        <path
          d="M12.1093 11.7455C12.2836 11.9325 12.3871 12.1816 12.3871 12.4488V14.0484C12.3871 14.1909 12.2716 14.3064 12.1291 14.3064H10.8387V13.4437C10.8387 13.1699 10.9475 12.9073 11.1411 12.7138L12.1093 11.7455Z"
          fill="#D23C69"
        />
        <path
          d="M10.8387 5.37154V3.20967C10.8387 2.9246 10.6076 2.69354 10.3226 2.69354H7.22579C6.0856 2.69354 5.16125 3.61785 5.16125 4.75807V5.37154C5.16125 5.64892 5.20597 5.92448 5.29369 6.18761L5.3796 6.44532C5.40591 6.52426 5.41932 6.60692 5.41932 6.69014V6.82257H10.5806V6.69014C10.5806 6.60692 10.594 6.52426 10.6203 6.44532L10.7062 6.18761C10.794 5.92445 10.8387 5.64889 10.8387 5.37154Z"
          fill="#5A4146"
        />
        <path
          d="M6.45166 3.74192C6.45166 4.32092 6.92104 4.79029 7.50004 4.79029H7.64519L7.71525 6.82254H10.5807V6.6901C10.5807 6.60689 10.5941 6.52423 10.6204 6.44529L10.7063 6.18757C10.794 5.92445 10.8387 5.64889 10.8387 5.37151V3.20967C10.8387 2.9246 10.6076 2.69354 10.3226 2.69354H7.50004C6.92104 2.69354 6.45166 3.16292 6.45166 3.74192Z"
          fill="#694B4B"
        />
        <path
          d="M9.54841 8.88721H6.45154V11.2097H9.54841V8.88721Z"
          fill="#E6AF78"
        />
        <path
          d="M6.45157 9.75971C6.90744 10.0238 7.43523 10.1774 7.99994 10.1774C8.56466 10.1774 9.09248 10.0238 9.54832 9.75971V8.88708H6.45154L6.45157 9.75971Z"
          fill="#D29B6E"
        />
        <path
          d="M8.00005 11.2097L7.17698 12.0327C7.06636 12.1434 6.88352 12.1309 6.78895 12.0063L5.93555 10.8818L6.15877 10.4114C6.22464 10.2725 6.39645 10.2217 6.52727 10.3023L8.00005 11.2097Z"
          fill="#D23C69"
        />
        <path
          d="M8 11.2097L8.82306 12.0328C8.93369 12.1434 9.11653 12.1309 9.21109 12.0063L10.0645 10.8818L9.84128 10.4114C9.77541 10.2726 9.60359 10.2217 9.47278 10.3024L8 11.2097Z"
          fill="#D23C69"
        />
        <path
          d="M7.99997 9.66128C6.57472 9.66128 5.41931 8.50587 5.41931 7.08062V6.77828C5.41931 6.6414 5.47369 6.51012 5.57047 6.41331L5.78425 6.19953C5.88103 6.10275 5.93541 5.97146 5.93541 5.83456V5.21593C5.93541 5.09643 6.01566 4.99262 6.13194 4.96515C6.74228 4.821 8.64156 4.47953 9.93409 5.43015C10.018 5.49187 10.0644 5.59328 10.0644 5.69746V5.83456C10.0644 5.97143 10.1188 6.10271 10.2156 6.19953L10.4294 6.41331C10.5262 6.51009 10.5805 6.64137 10.5805 6.77828V7.08062C10.5806 8.50587 9.42522 9.66128 7.99997 9.66128Z"
          fill="#F0C087"
        />
        <path
          d="M9.93412 5.43021C9.02506 4.76161 7.81753 4.73268 6.96772 4.82421C6.60925 4.8628 6.31306 4.92246 6.132 4.96521C6.01572 4.99268 5.93547 5.09649 5.93547 5.21599V5.83458C5.93547 5.97149 5.88109 6.10277 5.78428 6.19958L5.5705 6.41336C5.47369 6.51018 5.41931 6.64146 5.41931 6.77836V7.08068C5.41931 8.40018 6.41025 9.48646 7.68812 9.64068C7.24284 9.17696 6.96769 8.54855 6.96769 7.85486V5.96858C6.96769 5.70705 7.16215 5.48849 7.42162 5.45596C8.04094 5.3783 9.17525 5.33161 10.0644 5.83464V5.69752C10.0645 5.59333 10.018 5.49189 9.93412 5.43021Z"
          fill="#E6AF78"
        />
        <path
          d="M3.8907 11.7455C3.71645 11.9325 3.61292 12.1816 3.61292 12.4488V14.0484C3.61292 14.1909 3.72845 14.3064 3.87098 14.3064H5.16129V13.4437C5.16129 13.1699 5.05254 12.9073 4.85895 12.7138L3.8907 11.7455Z"
          fill="#D23C69"
        />
      </g>
      <defs>
        <clipPath id="clip0_2429_9743">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export const DashedLine = ({ w = 7, h = 12, fill = 'white' }) => {
  return (
    <svg className="absolute 2px bottom-0" height="1">
      {' '}
      <line
        x1="0"
        y1="1"
        x2="100%"
        y2="1"
        stroke="#DEE0E4"
        strokeWidth="1"
        strokeDasharray="5,5"
      />
    </svg>
  );
};
