import { useEffect, useMemo, useRef, useState } from 'react';

import { useRouter } from 'next/router';

import { logAmplitudeEvent, logFbEvent } from 'lib/log-event';
import { getCookie, setCookieForToday } from 'lib/storage';
import { isInBuckets } from 'lib/utils';

import RobotGrid from 'components/Robot/RobotGrid';
import TopCategories from 'components/categories/TopCategories';

import AppTrialNudge from './components/AppTrialNudge';
import AppTrialPopupV2 from './components/AppTrialPopupV2';
import BuySellSection from './components/BuySellSection';
import DailyTip from './components/DailyTips';
import ExploreAnimallSection from './components/ExploreAnimallSection';
import IncompleteListingCard from './components/IncompleteListingCard';
import InsightSection from './components/Insights';
import MultiPlanAppTrialPopup from './components/MultiPlanAppTrial';
import MyPosts from './components/MyPosts';
import OtherServices from './components/OtherServices';
import UpgradeSilverPlanNudge from './components/UpgradeSilverPlan';
import VideoTutorial from './components/VideoTutorial';
import Footer from './components/footer';
import ImageCard from './components/imageCard';

import { differenceInHours } from 'date-fns';
import useAppPaywall from 'modules/app-subscription/hooks/useAppPaywall';
import VetIntakeEntry from 'modules/chat/vet-intake/vetIntakeEntry';
import SubscriptionStatusBanner from 'modules/paywall-home/components/SubscriptionStatusBanner';
import { POST_STATE_ENUM } from 'modules/user/components/seller-dashboard/constants';
import { parseCookies, setCookie } from 'nookies';
import { useGrowthUser } from 'providers/GrowthUserProvider';

import HomepagePrimePopup from '../components/PrimeOrExpiredPopup';
import PseudoActivePopup from '../components/PseudoActivePopup';
import VipBuyerNudge from '../components/VipBuyerNudge';
import { isPostComplete } from '../utils';

const Home = ({
  user,
  _newPostsCount,
  recentlyPlaceListingOrder,
  myPosts,
  monetizationStats,
  districtsRecentTxn,
  _buyersCount,
  activePosts,
  isEligibleForPrimePopup,
  insightsData,
}) => {
  const { isPremiumUser, isEligible: isAppPaywallEligible } = useAppPaywall();
  const [showTrialPopup, setShowTrialPopup] = useState(false);
  const [pseudoActivePost, setPseudoActivePost] = useState(null);
  const [showPseudoActivePopup, setShowPseudoActivePopup] = useState(false);
  const { isGrowthUser, isLoaded } = useGrowthUser();

  const router = useRouter();

  const { vipBuyer } = monetizationStats || {};
  const hasDiscontinuedVipBuyerSubscription = useMemo(() => {
    return (
      vipBuyer &&
      !vipBuyer?.premiumUser &&
      vipBuyer?.recurringSubscriptionData &&
      vipBuyer?.recurringSubscriptionData?.status &&
      !['active', 'authenticated'].includes(
        vipBuyer?.recurringSubscriptionData?.status,
      )
    );
  }, [vipBuyer]);
  const isListingLimitReached = monetizationStats?.listing?.isLimitReached;
  const { buyersCount, newPostsCount } = parseCookies();

  // ---- Gating rules (X8/X9 + paywall) ----
  const bucketId = getCookie('bucketId');
  const inX8 = isInBuckets([80, 89], bucketId);
  const inX59 = isInBuckets([50, 99], bucketId);
  const inX49 = isInBuckets([40, 99], bucketId);
  const inX69 = isInBuckets([60, 99], bucketId);
  const isGrowthBucket50to99 =
    isLoaded && isGrowthUser && inX59 && isAppPaywallEligible && isPremiumUser;
  const isGrowthBucket40to99 =
    isLoaded && isGrowthUser && inX49 && isAppPaywallEligible && isPremiumUser;
  const isGrowthBucket60to99 =
    isLoaded && isGrowthUser && inX69 && isPremiumUser; // Top Categories

  const showCategories = isGrowthBucket60to99 || (inX8 && !isGrowthUser);

  const showRobotGrid = isGrowthBucket40to99;
  const scrollLandingEventFired = useRef({
    50: false,
    100: false,
  });
  const isEligibleForInsightsCards = isInBuckets([20, 39], bucketId);
  const isEligibleForFreeListing =
    monetizationStats?.listing?.remainingLimit > 0 &&
    monetizationStats?.listing?.status === 'UNPAID';

  const [isBlocked, blockedDraftedListing] = useMemo(() => {
    if (!myPosts || !myPosts.length || !isListingLimitReached) return false;
    const unpaidCompletePost = myPosts.find(
      // blocked post
      (post) =>
        post.state === POST_STATE_ENUM.DRAFTED &&
        post?.paymentRequired &&
        isPostComplete(post),
    );
    return [!!unpaidCompletePost, unpaidCompletePost];
  }, [myPosts, isListingLimitReached]);

  useEffect(() => {
    // cache values for 30mins
    if (!buyersCount || !newPostsCount) {
      // check if it already exists
      const daysFor30Mins = 30 / 1440;
      setCookie('buyersCount', _buyersCount, daysFor30Mins);
      setCookie('newPostsCount', _newPostsCount, daysFor30Mins);
    }

    logAmplitudeEvent('LANDED', 'LANDINGPAGE', 'HOMESCREEN', {
      TYPE: 'NEW 2025',
    });
    logFbEvent('LANDED_LANDINGPAGE_HOMESCREEN');

    const handleScroll = () => {
      const scrollHeight = document.body.scrollHeight;
      const clientHeight = document.documentElement.clientHeight;

      const scrollPercentage = Math.round(
        (window.scrollY / (scrollHeight - clientHeight)) * 100,
      );

      Object.keys(scrollLandingEventFired.current).forEach((key) => {
        if (scrollPercentage >= key && !scrollLandingEventFired.current[key]) {
          logAmplitudeEvent('SCROLLED', 'LANDINGPAGE', 'HOMESCREEN', {
            PERCENTAGE: key,
          });
          scrollLandingEventFired.current[key] = true;
        }
      });
    };

    window.addEventListener('scroll', handleScroll);

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    const hasShownPseudoActivePopup = getCookie('hasShownPseudoActivePopup');
    if (!!hasShownPseudoActivePopup) return;
    const isEligibleForPseudoActiveListing =
      monetizationStats?.listing?.isEligibleForPseudoActiveListing;

    const post = myPosts.find(
      (post) =>
        post.state === POST_STATE_ENUM.DRAFTED &&
        isPostComplete(post) &&
        !!post?.paymentRequired &&
        differenceInHours(new Date(), new Date(post?.publishedOn)) >= 48,
    );

    if (isEligibleForPseudoActiveListing && !!post) {
      setCookieForToday('hasShownPseudoActivePopup', true);
      setPseudoActivePost(post);
      setShowPseudoActivePopup(true);
    }
  }, [myPosts, monetizationStats]);

  useEffect(() => {
    if (!isLoaded) return;
    const hasTrialPopUpShowed = !!sessionStorage.getItem('hasTrialPopUpShowed');
    const show = isGrowthUser && !hasTrialPopUpShowed && !isPremiumUser;
    if (show) {
      sessionStorage.setItem('hasTrialPopUpShowed', 'true');
      setShowTrialPopup(true);
    }
  }, [monetizationStats, isGrowthUser, isLoaded, isPremiumUser]);

  const isX0ToX3User = isInBuckets([0, 29], getCookie('bucketId'));
  const isX4_X5User = isInBuckets([40, 59], getCookie('bucketId'));

  console.log('NEW_HOME_PAGE_RENDERED', { isGrowthUser });
  return (
    <>
      <div className="bg-surface-0 pt-[76px] pb-28 min-h-screen">
        {isX4_X5User ? (
          <MultiPlanAppTrialPopup
            show={showTrialPopup}
            source="HOME_PAGE"
            onClose={() => setShowTrialPopup(false)}
          />
        ) : (
          <AppTrialPopupV2
            show={showTrialPopup}
            source="HOME_PAGE"
            onClose={() => setShowTrialPopup(false)}
          />
        )}

        {isLoaded &&
          isGrowthUser &&
          isInBuckets([80, 99], getCookie('bucketId')) &&
          isPremiumUser && (
            <div className="px-4">
              <SubscriptionStatusBanner user={user} />
            </div>
          )}
        {!isPremiumUser && isLoaded && isGrowthUser && (
          <AppTrialNudge
            availableCattles={_newPostsCount}
            districtsRecentTxn={districtsRecentTxn}
            monetizationStats={monetizationStats}
          />
        )}

        {/* VIP Buyer nudge */}
        {!isGrowthUser && // VIP buyer nudge is not shown to growth users
          !isBlocked && // listing has higher priority than vip buyer nudge - listing is blocked
          hasDiscontinuedVipBuyerSubscription && (
            <VipBuyerNudge buyersCount={_buyersCount} />
          )}

        {isBlocked &&
          !isEligibleForFreeListing &&
          (!isGrowthUser ||
            (isGrowthUser &&
              monetizationStats?.listing?.isEligibleForVIPUserPlansPage &&
              isInBuckets([40, 59], getCookie('bucketId')))) && (
            <IncompleteListingCard
              districtsRecentTxn={districtsRecentTxn}
              monetizationStats={monetizationStats}
              buyersCount={_buyersCount}
              lastOrder={recentlyPlaceListingOrder}
              blockedDraftedListing={blockedDraftedListing}
            />
          )}
        <UpgradeSilverPlanNudge />
        <BuySellSection
          isEligibleForFreeListing={isEligibleForFreeListing}
          buyersCount={_buyersCount}
          newPostsCount={_newPostsCount}
          isX0ToX3User={isX0ToX3User}
        />
        {insightsData && isEligibleForInsightsCards && (
          <InsightSection insightsData={insightsData} />
        )}
        <div className="flex flex-row gap-3 mx-4 sm:max-w-md sm:mx-auto">
          {isGrowthBucket50to99 && <VetIntakeEntry />}
        </div>

        {showCategories && (
          <div className="flex py-3 mb-3">
            <TopCategories />
          </div>
        )}

        {showRobotGrid && <RobotGrid isFreeAccess={false} />}

        <ExploreAnimallSection />
        {isX0ToX3User && <DailyTip />}
        <MyPosts
          user={user}
          isListingLimitReached={isListingLimitReached}
          posts={myPosts}
          buyersCount={_buyersCount}
        />

        <OtherServices />
        <VideoTutorial />
        <ImageCard />
        <Footer />

        {isEligibleForPrimePopup && (
          <HomepagePrimePopup activePosts={activePosts} />
        )}

        <PseudoActivePopup
          post={pseudoActivePost}
          isOpen={showPseudoActivePopup}
          onSubmit={() => {
            setShowPseudoActivePopup(false);
            router.reload();
          }}
          onClose={() => setShowPseudoActivePopup(false)}
          source={'HOMEPAGE'}
        />
      </div>
    </>
  );
};

export default Home;
