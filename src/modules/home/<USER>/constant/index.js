import { cn } from 'lib/utils';

import { AdIcon } from 'modules/buy/components/StreakPopup/Icons';

import { CategoryPaywall } from '../icons/CategoryPaywall';
import {
  IncreaseMilkIcon,
  PashuUstadIcon,
  RatePredictorIcon,
} from '../icons/exploreAnimallIcons';
import {
  CattleBimaIcon,
  CattleLoanIcon,
  PashuCharchaIcon,
  PlayGameIcon,
} from '../icons/otherServiceIcons';

export const FEATURES = {
  RATE_PREDICTOR: 'RATE_PREDICTOR',
  DIET_PLANNER: 'DIET_PLANNER',
  PASHU_ADVISER: 'PASHU_ADVICER',
  PASHU_LOAN: 'PASHU_LOAN',
  PASHU_INSURANCE: 'PASHU_INSURANCE',
  PASHU_CHAT: 'PASHU_CHAT',
  PLAY_GAME: 'PLAY_GAME',
  AFTER_AD_FREE_PLAN: 'AFTER_AD_FREE_PLAN',
  TOP_CATEGORIES: 'TOP_CATEGORIES',
};

export const FEATURE_INFO = (type, plan) => {
  const isVipPlan = plan?.extraInfo?.featureInfo?.isVip;

  const features = {
    [FEATURES.RATE_PREDICTOR]: {
      icon: <RatePredictorIcon w={30} h={30} />,
      title: (
        <div>
          <span className="font-semibold text-xl leading-6 text-text-primary mr-1">
            पशु का सही
          </span>
          <span
            className={cn(
              'font-semibold text-xl leading-6 text-primary-500',
              isVipPlan && 'text-text-primary',
            )}
          >
            रेट जाने
          </span>
        </div>
      ),
      subTitle: 'एक्सपर्ट से जाने पशु का सही रेट',
    },
    [FEATURES.DIET_PLANNER]: {
      icon: <IncreaseMilkIcon w={25} h={28} />,
      title: (
        <div>
          <span
            className={cn(
              'font-semibold text-xl leading-6 text-primary-500 mr-1',
              isVipPlan && 'text-text-primary',
            )}
          >
            ज़्यादा दूध,
          </span>
          <span className="font-semibold text-xl leading-6 text-text-primary">
            ज़्यादा कमाई
          </span>
        </div>
      ),
      subTitle: 'एक्सपर्ट से सीखिए दूध बढ़ाने के आसान तरीके',
    },

    [FEATURES.PASHU_ADVISER]: {
      icon: <PashuUstadIcon w={43} h={28} />,
      title: (
        <div>
          <span className="font-semibold text-xl leading-6 text-text-primary mr-1">
            सलाह लें
          </span>
          <span
            className={cn(
              'font-semibold text-xl leading-6 text-primary-500',
              isVipPlan && 'text-text-primary',
            )}
          >
            एक्सपर्ट से
          </span>
        </div>
      ),
      subTitle: 'वीडियो भेजें और पाएं आसान समाधान',
    },
    [FEATURES.PASHU_LOAN]: {
      icon: <CattleLoanIcon w={32} h={32} />,
      title: (
        <div>
          <span className="font-semibold text-xl leading-6 text-text-primary mr-1">
            घर बैठे लें
          </span>
          <span
            className={cn(
              'font-semibold text-xl leading-6 text-primary-500',
              isVipPlan && 'text-text-primary',
            )}
          >
            पशु लोन
          </span>
        </div>
      ),
      subTitle: 'घर बैठे आसान पशु लोन पाएँ, सीधे Animall से',
    },
    [FEATURES.PASHU_INSURANCE]: {
      icon: <CattleBimaIcon w={32} h={32} />,
      title: (
        <div>
          <span className="font-semibold text-xl leading-6 text-text-primary mr-1">
            सस्ता
          </span>
          <span
            className={cn(
              'font-semibold text-xl leading-6 text-primary-500',
              isVipPlan && 'text-text-primary',
            )}
          >
            पशु बीमा
          </span>
        </div>
      ),
      subTitle: 'अपने पशु का सस्ता बीमा करवायें Animall से',
    },
    [FEATURES.PASHU_CHAT]: {
      icon: <PashuCharchaIcon w={32} h={32} />,
      title: (
        <div>
          <span className="font-semibold text-xl leading-6 text-text-primary mr-1">
            पशु
          </span>
          <span
            className={cn(
              'font-semibold text-xl leading-6 text-primary-500',
              isVipPlan && 'text-text-primary',
            )}
          >
            चर्चा
          </span>
        </div>
      ),
      subTitle: 'सवाल पूछें या जानकारी बाँटें — सब कुछ एक ही जगह',
    },
    [FEATURES.PLAY_GAME]: {
      icon: <PlayGameIcon w={32} h={32} />,
      title: (
        <div>
          <span
            className={cn(
              'font-semibold text-xl leading-6 text-primary-500 mr-1',
              isVipPlan && 'text-text-primary',
            )}
          >
            गेम खेलें,
          </span>
          <span className="font-semibold text-xl leading-6 text-text-primary">
            कॉइन कमाएँ
          </span>
        </div>
      ),
      subTitle: 'सवालों के जवाब दो, कॉइन पाओ',
    },
    [FEATURES.AFTER_AD_FREE_PLAN]: {
      icon: <AdIcon w={32} h={32} fill="black" />,
      title: (
        <div>
          <span
            className={cn(
              'font-semibold text-xl leading-6 text-primary-500 mr-1',
              isVipPlan && 'text-text-primary',
            )}
          >
            कोई ऐड नहीं,
          </span>
          <span className="font-semibold text-xl leading-6 text-text-primary">
            सिर्फ़ पशु
          </span>
        </div>
      ),
      subTitle: 'अब AD नहीं, बिना रुकावट पशु देखे',
    },
    [FEATURES.TOP_CATEGORIES]: {
      icon: <CategoryPaywall w={32} h={32} />,
      title: (
        <div>
          <span className="font-semibold text-xl leading-6 text-text-primary mr-1">
            पशु से जुड़े
          </span>
          <span
            className={cn(
              'font-semibold text-xl leading-6 text-primary-500',
              isVipPlan && 'text-text-primary',
            )}
          >
            हर सवाल का जवाब
          </span>
        </div>
      ),
      subTitle: 'हर सवाल का जवाब, डॉक्टर की मदद से',
    },
  };
  return features[type] || features[FEATURES.AFTER_AD_FREE_PLAN];
};

export const FEATURE_BENIFITS = [
  'जितने भी पशु बेचें',
  'जितने भी पशु खरीदें',
  'फिर ₹149/महिना',
];
