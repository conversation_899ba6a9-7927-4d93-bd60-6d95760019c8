import { useEffect, useMemo } from 'react';

import { useRouter } from 'next/router';

import { logAmplitudeEvent } from 'lib/log-event';
import { cn } from 'lib/utils';

import { TertiaryBody } from 'components/ui/typography';

const labels = {
  favoriteCattle: 'सबकी पसंद',
  topCattle: 'टॉप पशु',
  topBuyerAndSeller: 'खरीदार व पशु पालक',
};

export default function PillSegmentedControl() {
  const router = useRouter();
  const { section } = router.query;
  const active = section || 'favoriteCattle';

  const items = useMemo(
    () => ['favoriteCattle', 'topCattle', 'topBuyerAndSeller'],
    [],
  );

  const handleClick = (key) => {
    logAmplitudeEvent('CLICKED', 'MARKETING', 'INSIGHTS', {
      section: key,
    });

    router.replace(
      {
        pathname: router.pathname,
        query: { ...router.query, section: key },
      },
      undefined,
      { scroll: false },
    );

    // Scroll to element
    const el = document.getElementById(`section-${key}`);
    if (el) el.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    logAmplitudeEvent('LANDED', 'MARKETING', 'INSIGHTS', {
      section: active,
    });

    router.replace(
      {
        pathname: router.pathname,
        query: { ...router.query, section: active },
      },
      undefined,
      { scroll: false },
    );
    const el = document.getElementById(`section-${active}`);
    if (el) {
      setTimeout(() => {
        el.scrollIntoView({ behavior: 'smooth' });
      }, 0);
    }
  }, []);

  return (
    <div className="flex gap-3 mt-4">
      {items.map((key) => {
        const selected = key === active;
        return (
          <div
            key={key}
            onClick={() => handleClick(key)}
            className={cn(
              'rounded-lg px-4 py-2 text-sm font-medium shadow-sm',
              'ring-1 ring-black/10',
              selected
                ? 'bg-primary-25 text-emerald-700 border-[1px] border-[#14776F]'
                : 'bg-[#ffff] text-gray-600 hover:bg-gray-200',
            )}
          >
            <TertiaryBody
              className={cn(
                selected ? 'text-primary-600' : ' text-text-secondary',
              )}
            >
              {labels[key]}
            </TertiaryBody>
          </div>
        );
      })}
    </div>
  );
}
