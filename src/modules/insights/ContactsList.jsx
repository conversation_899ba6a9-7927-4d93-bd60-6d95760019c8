import { cn } from 'lib/utils';

import Image from 'components/UnoptimizedImage';
import { SecondaryBody } from 'components/ui/typography';

import { DashedLine } from 'modules/home/<USER>/icons/insights';
import { CWAPhoneIcon, CWAWAIcon } from 'modules/user/components/icons';

const ContactsList = ({
  buyerData = [],
  sellersData = [],
  handleContactBuyer,
}) => {
  // Transform and combine all contacts into a single array
  const allContacts = [
    // Buyers
    ...buyerData.map((item) => ({
      id: `buyer-${item.name}`,
      name: item.name,
      count: item.count,
      countLabel: 'काल किए',
      img: item.img,
      seller: item.seller,
      contact: item.contact,
      section: 'खरीदार',
    })),
    // Sellers
    ...sellersData.map((item) => ({
      id: `seller-${item.seller}`,
      name: item.seller,
      count: item.totalListings,
      countLabel: 'पशु बेचें',
      img: item.img,
      seller: item.seller,
      contact: item.contact,
      section: 'पशु पालक',
    })),
  ];

  if (allContacts.length === 0) return null;

  return (
    <div>
      {allContacts.map((contact, index) => {
        // Show section header when section changes
        const showSectionHeader =
          index === 0 ||
          (index > 0 && allContacts[index - 1].section !== contact.section);

        return (
          <div key={contact.id}>
            {showSectionHeader && (
              <SecondaryBody
                className={cn(' font-bold text-base', index !== 0 && 'pt-4')}
              >
                {contact.section}
              </SecondaryBody>
            )}

            <div className="flex relative items-center justify-between py-3">
              <div className="flex gap-2">
                <div className="flex items-center gap-2">
                  <Image
                    className="rounded-full border border-trueGray-100 flex-shrink-0"
                    src={
                      contact.img ||
                      'https://static-assets.animall.in/static/images/Home_page/defaultProfileAvtar.png'
                    }
                    alt="profile"
                    width={32}
                    height={32}
                    objectFit="cover"
                  />
                </div>

                <div className="flex flex-col">
                  <p className="font-semibold text-text-primary text-lg max-w-[150px] truncate">
                    {contact.name}
                  </p>
                  <p className=" font-normal text-grey-400 text-xs">
                    {contact.countLabel} :{' '}
                    <span className="font-bold text-sm text-grey-400 ">
                      {contact.count || 0}
                    </span>
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-1.5 h-12 bg-trueGray-100 rounded-lg p-1.5 ">
                <div
                  className="flex justify-center items-center bg-gradient-to-b from-[#2F80ED] to-[#0385DC] w-9 h-9 rounded-md"
                  onClick={() =>
                    handleContactBuyer('CALL', contact.seller, contact.contact)
                  }
                >
                  <CWAPhoneIcon w={18} h={18} fill="#FFFFFF" />
                </div>
                <div
                  className="flex items-center justify-center bg-gradient-to-b from-[#5BD066] to-[#27B43E] w-9 h-9 rounded-md"
                  onClick={() =>
                    handleContactBuyer('WA', contact.seller, contact.contact)
                  }
                >
                  <CWAWAIcon w={20} h={20} fill="#FFFFFF" />
                </div>
              </div>

              {index < allContacts.length - 1 &&
                index !== buyerData?.length - 1 && <DashedLine />}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ContactsList;
