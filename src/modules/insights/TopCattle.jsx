import React, { useCallback, useContext, useState } from 'react';

import dynamic from 'next/dynamic';
import { useRouter } from 'next/router';

import useAdMob from 'hooks/useAdmob';
import usePostActions from 'hooks/usePostActions';
import useRouterDisclosure from 'hooks/useRouterDisclosure';
import useToast from 'hooks/useToast';

import { AnimallLoaderContext } from 'components/AnimallLoader';
import Bar<PERSON>hart from 'components/BarChart';
import PostCard from 'components/PostCard/PostCard';
import { PostCardActions } from 'components/PostCard/constants';
import CwaErrPopup from 'components/cwaBanners';
import { H3, SecondaryBody, TertiaryBody } from 'components/ui/typography';

import { AdMobAdType, AdMobAdapters, AdUnitId } from 'modules/ads/constants';
import useSnoozePopup from 'modules/buy/components/CWASnooze/hooks/useSnoozePopup';
import { updateResourceAdsViewCounter } from 'modules/buy/helper';
import { formatIndian } from 'modules/home/<USER>/components/Insights/data';
import { TopPashuIcon } from 'modules/home/<USER>/icons/insights';

export function formatDateToDDMM(date) {
  const tz = 'Asia/Kolkata';
  const d = new Date(date);
  if (isNaN(d)) return ''; // invalid date guard

  // Compare in IST so SSR/clients are consistent
  const todayStr = new Date().toLocaleDateString('en-CA', { timeZone: tz }); // YYYY-MM-DD
  const dateStr = d.toLocaleDateString('en-CA', { timeZone: tz });

  if (dateStr === todayStr) return 'आज';

  const day = d.toLocaleString('en-GB', { day: '2-digit', timeZone: tz }); // 01..31
  const month = d.toLocaleString('en-GB', { month: '2-digit', timeZone: tz }); // 01..12
  return `${day}-${month}`;
}

export default function TopCattle({ insightsData }) {
  const router = useRouter();
  const { triggerToast, toastProps } = useToast();
  const { openAnimallLoader, closeAnimallLoader } =
    useContext(AnimallLoaderContext);
  const { showInterstitial } = useAdMob();
  const {
    isOpen: isSnoozePopupOpen,
    onOpen: openSnoozePopup,
    onClose: closeSnoozePopup,
    snoozeData,
    SnoozePopup,
  } = useSnoozePopup('snooze-popup');

  const showAdOnClosingResourceViewer = useCallback(
    (isResourceViewerOpen) => {
      if (!isResourceViewerOpen) {
        function resourceAdCallback() {
          var updatedCount = updateResourceAdsViewCounter();
          // setShowAdFreePopup(true);
        }
        showInterstitial({
          type: AdMobAdType.INTERSTITIAL,
          adUnitId: AdUnitId.RESOURCE_VIEWER_INTERSTITIAL,
          adapter: AdMobAdapters.NONE,
          extras: {},
          adCloseCallback: () => {
            resourceAdCallback();
          },
          successCallback: () => {},
          failureCallback: (error) => {
            resourceAdCallback();
          },
        });
      } else {
      }
    },
    [showInterstitial],
  );
  const [toggleReportPopup, ReportPopup, , isOpenReportPopup] =
    useRouterDisclosure({
      Modal: dynamic(() => import('components/PostCard/ReportPopup')),
      name: 'reportPopup',
    });

  const [toggleResourceViewer, ResourceViewer, , isOpenResourceViewer] =
    useRouterDisclosure({
      Modal: dynamic(() => import('components/PostCard/ResourceViewer')),
      name: 'resource-viewer',
      toggleCallback: showAdOnClosingResourceViewer,
    });
  const togglePopup = useCallback(
    (popup, props = {}) => {
      const popupMap = {
        [PostCardActions.REPORT]: toggleReportPopup,
        [PostCardActions.MEDIA]: toggleResourceViewer,
      };
      popupMap[popup]?.(props);
    },
    [toggleReportPopup, toggleResourceViewer],
  );
  const {
    performAction,
    currentPost,
    currentMediaIndex,
    resetData,
    showCwaError,
    setShowCwaError,
  } = usePostActions({
    openLoader: openAnimallLoader,
    closeLoader: closeAnimallLoader,
    // t,
    togglePopup,
    onSnooze: openSnoozePopup,
  });
  const [showAll, setShowAll] = useState(false);

  const postsToShow = showAll
    ? insightsData?.topPricedPosts?.posts
    : insightsData?.topPricedPosts?.posts.slice(0, 2);
  const chartData = insightsData?.dateWiseAvgPostPrice?.dateWise?.map(
    (item) => ({
      date: formatDateToDDMM(item.date),
      avgPrice: item.avgPrice,
      lineValue: item.avgPrice,
    }),
  );

  return (
    <div id="section-topCattle" className=" pt-8">
      <H3 className="flex items-center gap-2 mb-3">
        <TopPashuIcon w={24} h={24} /> आपके क्षेत्र के{' '}
        <span className=" text-status-moderation-text">टॉप पशु</span>
      </H3>
      {postsToShow?.map((item, idx) => (
        <PostCard
          post={item}
          triggerToast={triggerToast}
          triggerAction={performAction}
          key={`${item._id}_${idx}`}
          className="mb-4"
        />
      ))}
      <button
        onClick={() => setShowAll((prev) => !prev)}
        className="text-blue-600 text-sm font-medium hover:underline items-center justify-center flex mt-4 w-full"
      >
        {showAll ? 'कम दिखाएं' : 'और देखें'}
      </button>
      <div className="mt-3">
        <SecondaryBody className="text-black mb-3 font-bold">
          जाने पिछले 7 दिन का दाम क्या रहा{' '}
        </SecondaryBody>
        <div className=" bg-[#FAFAFA] py-3 px-2 rounded-lg">
          <div className="flex items-center justify-between">
            <TertiaryBody className="text-grey-400">
              औसत:{' '}
              <span className=" font-bold text-sm">
                {formatIndian(
                  insightsData?.dateWiseAvgPostPrice?.overallAvg || 0,
                )}
              </span>
            </TertiaryBody>
            <TertiaryBody className="text-text-secondary">7 दिन</TertiaryBody>
          </div>
          <BarChart
            data={chartData}
            xAxisField={'date'}
            yAxisField={'avgPrice'}
            BAR_WIDTH={44}
          />
          <TertiaryBody className=" text-center mt-5">
            अपने पशु को सबकी पसंद बनाए
          </TertiaryBody>
          <div
            onClick={() => router.push('/sell' + '?source=Insights')}
            className=" mt-[10px] rounded-md  border-[1px] border-primary-600 flex items-center justify-center py-4 text-primary-600 text-lg font-bold cursor-pointer"
          >
            पशु दर्ज करे
          </div>
        </div>
      </div>
      <SnoozePopup
        isOpen={isSnoozePopupOpen}
        onClose={closeSnoozePopup}
        snoozeData={snoozeData}
      />
      <ResourceViewer
        isOpen={isOpenResourceViewer}
        onClose={() => {
          toggleResourceViewer();
          resetData();
        }}
        post={currentPost}
        resourceIndex={currentMediaIndex}
        triggerAction={performAction}
        triggerToast={triggerToast}
      />
      {showCwaError && (
        <div>
          <CwaErrPopup
            error={showCwaError}
            pageName="INSIGHTS_PAGE"
            postData={currentPost}
            setShowCwaError={setShowCwaError}
          />
        </div>
      )}
      <ReportPopup
        isOpen={isOpenReportPopup}
        onClose={() => {
          toggleReportPopup();
          resetData();
        }}
        postId={currentPost?._id}
      />
    </div>
  );
}
