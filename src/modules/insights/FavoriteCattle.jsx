import React, { useMemo } from 'react';

import { i18n } from 'next-i18next';
import { useRouter } from 'next/router';

import { cn } from 'lib/utils';

import BarChart from 'components/BarChart';
import Image from 'components/UnoptimizedImage';
import GallaryIcon from 'components/icons/GallaryIcon';
import { H3, SecondaryBody, TertiaryBody } from 'components/ui/typography';

import { isNil } from 'lodash';
import {
  formatIndian,
  getCattleType,
  getCattleTypeImage,
} from 'modules/home/<USER>/components/Insights/data';
import Resource from 'modules/home/<USER>/components/Resource';
import {
  FireIcon,
  LocationIcon,
} from 'modules/home/<USER>/icons/insights';

import { formatDateToDDMM } from './TopCattle';

function formatPrice(num) {
  if (num >= 10000000) {
    return (num / 10000000).toFixed(1).replace(/\.0$/, '') + 'Cr'; // Crore
  } else if (num >= 100000) {
    return (num / 100000).toFixed(1).replace(/\.0$/, '') + 'L'; // Lakh
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1).replace(/\.0$/, '') + 'K'; // Thousand
  }
  return num?.toString(); // if less than 1000
}

export default function FavoriteCattle({ insightsData }) {
  const router = useRouter();
  const topBreed = insightsData?.topBreeds?.[0] || {};
  const topBreedBarData =
    insightsData?.topBreeds?.[0]?.dateWise?.map((item) => ({
      date: formatDateToDDMM(item.date),
      count: item.count,
      lineValue: item.count,
    })) || [];

  const breed =
    topBreed?.breed && !isNil(topBreed?.breed)
      ? i18n
          .t(`common:breedMap.${topBreed?.breed}`)
          .replace('होल्सटीन फ्रीसिएन (HF)', 'HF')
      : null;

  const cattleType = getCattleType(topBreed?.breed) || 'गाय';
  const breedType = '' + breed + ' ' + cattleType;
  const genericImage = getCattleTypeImage(topBreed?.breed);
  const averageCount = useMemo(() => {
    const dateWise =
      topBreed && topBreed?.dateWise && topBreed?.dateWise?.length > 0
        ? topBreed?.dateWise
        : [];
    const totalCount = dateWise.reduce((acc, item) => acc + item.count, 0);
    return Math.floor((totalCount || 0) / 7);
  }, []);

  return (
    <div id="section-favoriteCattle" className="pt-6">
      <H3 className="flex items-center gap-2">
        <FireIcon w={20} h={26} /> सबकी पसंद{' '}
        <span className=" text-status-moderation-text">{breedType}</span>
      </H3>
      <div className="mt-3 rounded-xl shadow-sm p-4 bg-white">
        {topBreed?.image || genericImage ? (
          <div className="relative w-full h-[197px] mb-3">
            <Image
              src={topBreed?.image || genericImage}
              alt="post-image"
              layout="fill"
              className="rounded-lg object-cover object-center"
            />
          </div>
        ) : (
          <div
            className={cn(
              'flex bg-surface-0 justify-center items-center w-full h-[197px] rounded flex-shrink-0',
              ' mb-3',
            )}
          >
            <GallaryIcon />
          </div>
        )}
        <div className=" mb-2">
          <p className=" font-extrabold text-[#2E3C4D] text-xl mb-1">
            {topBreed?.highestMilkMin || 5}L - {topBreed?.highestMilkMax || 10}L
            दूध क्षमता{' '}
            <span className="inline-block h-6 mx-2 border-l border-gray-300 align-middle"></span>
            {topBreed?.lactationMin || 0} - {topBreed?.lactationMax || 0} ब्यात
          </p>
          <p className=" font-extrabold text-[#2E3C4D] text-xl">
            {formatIndian(topBreed?.priceMin || 0)} -{' '}
            {formatIndian(topBreed?.priceMax || 0)}{' '}
          </p>
        </div>
        <div className="flex items-center justify-between whitespace-nowrap gap-2">
          <div className="flex items-center gap-1">
            <Image
              src={
                'https://static-assets.animall.in/static/images/Group_of_people.png'
              }
              alt="Paravet Service"
              layout="fixed"
              width={50}
              height={24}
            />
            <p className=" text-base">
              <span className="font-bold text-black">
                {topBreed?.total || 0} पशु
              </span>{' '}
              बिके
            </p>
          </div>

          <p className=" flex gap-1 items-center text-grey-400 font-medium">
            <LocationIcon />
            {insightsData?.user?.district}, {insightsData?.user?.location_state}
          </p>
        </div>
        <div className="mt-5">
          <SecondaryBody className="text-black mb-3 font-bold">
            जाने कितनी ज़फ़राबादी भैंस बिकाऊ है?
          </SecondaryBody>
          <div className=" bg-[#FAFAFA] py-3 px-2 rounded-lg">
            <div className="flex items-center justify-between">
              <TertiaryBody className="text-grey-400">
                औसत:{' '}
                <span className=" font-bold text-sm">{averageCount || 0}</span>
              </TertiaryBody>
              <TertiaryBody className="text-text-secondary">7 दिन</TertiaryBody>
            </div>
            <BarChart
              data={topBreedBarData}
              xAxisField="date"
              yAxisField="count"
              BAR_WIDTH={42}
            />
          </div>
        </div>
        <div className="mt-5">
          <SecondaryBody className="text-black mb-2 font-bold">
            बाक़ी बिकने वाली नसल
          </SecondaryBody>
          <div className="rounded-lg">
            <div className="overflow-x-auto rounded-lg">
              <table className="w-full text-sm text-left border-collapse">
                <thead className="bg-text-primary text-white text-base">
                  <tr>
                    <th className="pl-4 py-2 text-start">नस्ल</th>
                    <th className="pl-4 py-2 text-start">दाम दायरा</th>
                    <th className="pl-4 py-2 text-start">दूध (ली/दिन)</th>
                  </tr>
                </thead>
                <tbody>
                  {insightsData?.topBreeds.map((row, idx) => (
                    <tr
                      key={idx}
                      className={`${
                        idx % 2 !== 0 ? 'bg-white' : 'bg-gray-100'
                      } `}
                    >
                      <td className="pl-4 py-2 text-start">{row?.breed}</td>
                      <td className="pl-4 py-2 text-start">
                        ₹{formatPrice(row?.priceMin)} – ₹
                        {formatPrice(row?.priceMax)}
                      </td>
                      <td className="pl-4 py-2 text-start">
                        {row?.highestMilkMin || 0}–{row?.highestMilkMax || 5}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          <TertiaryBody className=" text-center mt-5">
            अपने पशु को सबकी पसंद बनाए
          </TertiaryBody>
          <div
            onClick={() => router.push('/sell' + '?source=Insights')}
            className=" mt-[10px] rounded-md  border-[1px] border-primary-600 flex items-center justify-center py-4 text-primary-600 text-lg font-bold cursor-pointer"
          >
            पशु दर्ज करे
          </div>
        </div>
      </div>
    </div>
  );
}
