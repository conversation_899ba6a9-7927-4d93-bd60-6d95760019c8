import React, { useCallback, useMemo, useState } from 'react';

import { useRouter } from 'next/router';

import { logAmplitudeEvent } from 'lib/log-event';

import BarChart from 'components/BarChart';
import Image from 'components/UnoptimizedImage';
import { H3, H4, SecondaryBody, TertiaryBody } from 'components/ui/typography';

import useAppPaywall from 'modules/app-subscription/hooks/useAppPaywall';
import {
  DashedLine,
  TopBuyerAndSellerIcon,
} from 'modules/home/<USER>/icons/insights';
import { CWAPhoneIcon, CWAWAIcon } from 'modules/user/components/icons';
import { useShowPaywall } from 'providers/PaywallProvider';

import ContactsList from './ContactsList';
import { formatDateToDDMM } from './TopCattle';

const TABS = {
  BUYER: 'खरीदार',
  SELLER: 'पशु पालक',
};

export default function BuyerAndSeller({ insightsData }) {
  const [activeTab, setActiveTab] = useState(TABS.BUYER);
  const isBuyerTabActive = activeTab === TABS.BUYER;
  const router = useRouter();
  const { showPaywall } = useShowPaywall();
  const {
    isEligible: isAppPaywallEligible,
    isPremiumUser: isAppPaywallPremiumUser,
  } = useAppPaywall();

  const handleTabChange = useCallback(() => {
    setActiveTab((prev) => (prev === TABS.BUYER ? TABS.SELLER : TABS.BUYER));
  }, []);

  const handleContactBuyer = (action, buyerName, buyerContact, post = {}) => {
    if (isAppPaywallEligible && !isAppPaywallPremiumUser) {
      showPaywall();
      return;
    }
    const whatsAppText = `https://animall.in?postId=${post?._id}&utm_campaign=contactPopSellerDashboard&utm_source=whatsApp \n\n ${buyerName} ji जी , आपने मेरे इस पशु के लिए संपर्क किया था - अगर आपको और जानकारी चाहिए तो हम बात कर सकते हैं|
        `;

    const anchorTag = document.createElement('a');
    anchorTag.href =
      action === 'CALL'
        ? `tel:+91${buyerContact}`
        : `https://api.whatsapp.com/send?phone=91${buyerContact}&text=${encodeURIComponent(
            whatsAppText,
          )}`;
    if (action == 'CALL') {
      anchorTag.click();
    } else {
      window.open(anchorTag.href, '_blank');
    }

    logAmplitudeEvent('CONTACT', 'BUYER', 'INSIGHTS', {
      TYPE: action,
      BUYER_NAME: buyerName,
      BUYER_PHONE: buyerContact,
    });
  };

  const BarData = useMemo(() => {
    const barData = !isBuyerTabActive
      ? insightsData?.dateWiseSellerCounts
      : insightsData?.dateWiseBuyersCount;
    return barData?.map((item) => ({
      date: formatDateToDDMM(item.date),
      count: item.count,
      lineValue: item.count,
    }));
  }, [isBuyerTabActive, insightsData]);

  const sellersData = useMemo(() => {
    return insightsData?.topSellers || [];
  }, [insightsData]);

  const buyerData = useMemo(() => {
    return insightsData?.topBuyers || [];
  }, [insightsData]);

  if (!buyerData?.length && !sellersData?.length) {
    return null;
  }

  return (
    <div id="section-topBuyerAndSeller" className="pt-8">
      <H3 className="flex items-center gap-2">
        <TopBuyerAndSellerIcon w={24} h={24} /> क्षेत्र के टॉप{' '}
        <span className=" text-status-moderation-text">खरीदार व पशु पालक</span>
      </H3>
      <div className="mt-3 rounded-xl shadow-sm p-4 bg-white">
        <ContactsList
          buyerData={buyerData}
          sellersData={sellersData}
          handleContactBuyer={handleContactBuyer}
        />
        <div className="mt-2">
          <SecondaryBody className="text-black mb-3 font-bold">
            जाने आपके क्षेत्र में कितने खरीदार व पशु पालक है
          </SecondaryBody>
          <div className=" bg-[#FAFAFA] py-3 px-2 rounded-lg">
            <div className="flex items-center justify-between">
              <H4 className="text-grey-400">
                आज{' '}
                <span className=" font-bold text-secondary-80 text-xl">
                  {BarData[BarData?.length - 1 || 0]?.count || 0}{' '}
                </span>
                {isBuyerTabActive ? 'ख़रीदार' : 'विक्रेता'}
              </H4>
              <div
                className="w-fit h-fit rounded-lg bg-surface-0 flex p-1"
                onClick={handleTabChange}
              >
                <TertiaryBody
                  className={`px-3 py-[3.5px] ${
                    isBuyerTabActive
                      ? 'bg-white rounded-md text-primary-600'
                      : ''
                  }`}
                >
                  {TABS.BUYER}
                </TertiaryBody>
                <TertiaryBody
                  className={`px-3 py-[3.5px] ${
                    !isBuyerTabActive
                      ? 'bg-white rounded-md text-primary-600'
                      : ''
                  }`}
                >
                  {TABS.SELLER}
                </TertiaryBody>
              </div>
            </div>
            <BarChart
              data={BarData}
              xAxisField="date"
              yAxisField="count"
              BAR_WIDTH="42"
            />
          </div>
        </div>
        <TertiaryBody className=" text-center mt-5">
          अपना नाम इस सूची में लेन के लिए
        </TertiaryBody>
        <div
          onClick={() => router.push('/buy' + '?source=Insights')}
          className=" mt-[10px] rounded-md  border-[1px] border-primary-600 flex items-center justify-center py-4 text-primary-600 text-lg font-bold cursor-pointer"
        >
          पशु देखें
        </div>
      </div>
    </div>
  );
}
