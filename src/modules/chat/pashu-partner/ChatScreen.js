import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { useRouter } from 'next/router';

import { logAmplitudeEvent } from 'lib/log-event';
import { getCookie } from 'lib/storage';
import { USER_TYPES, isInBuckets } from 'lib/utils';

import PaymentBottomSheet from 'components/BotPartnerCard/PaymentBottomSheet';
import SpeechMicButton from 'components/SpeechMicButton';

import { useToast } from '@chakra-ui/react';
import { BOT_PARTNERS } from 'data/bot-partners';
import { useGrowthUser } from 'providers/GrowthUserProvider';

import EnhancedMessageRenderer, {
  parseMessageOptions,
} from '../component/EnhancedMessageRenderer';
import TypingEffect from '../component/TypingEffect';
import { predefinedMessages } from '../diet-planner/data/pre-defined-messages';
import {
  formatChatDate,
  isAnyOptionClickedInThisMessage,
  replacePlaceholders,
  restartSession,
  sendBotMessage,
  sendMessage,
} from '../helper/index';

const ChatScreen = ({
  chatSession,
  setChatSession,
  isTyping,
  setIsTyping,
  paymentMessageCtaClick,
  setMessages,
  messages,
  isEligible,
  source = 'NA',
  initialQuestion,
}) => {
  const [userInput, setUserInput] = useState(initialQuestion || '');
  const [isLoading, setIsLoading] = useState(false);
  const [stopMicListening, setStopMicListening] = useState(false);
  const aiResponseCount = useRef(+chatSession?.aiResponseCount || 2);
  const inputRef = useRef(null);

  const router = useRouter();
  const toast = useToast();
  const chatEndRef = useRef(null);

  const src = router?.query?.source;

  // Check audio chat eligibility
  const [audioChatEligible, setAudioChatEligible] = useState(false);
  const { isGrowthUser, isLoaded } = useGrowthUser();

  useEffect(() => {
    const checkAudioEligibility = async () => {
      let userType = getCookie('userType');
      if (!userType) {
        userType = isGrowthUser ? USER_TYPES.NEW : USER_TYPES.OLD;
      }

      const isEligibleForAudio =
        isInBuckets([0, 59], getCookie('bucketId')) && userType === 'NEW';

      setAudioChatEligible(isEligibleForAudio);
    };

    if (isLoaded) {
      checkAudioEligibility();
    }
  }, [isGrowthUser, isLoaded]);

  // Keep cursor at end of input and ensure visibility
  useEffect(() => {
    if (inputRef.current) {
      const len = userInput.length;
      inputRef.current.setSelectionRange(len, len);
      // Scroll to show the end of the text
      inputRef.current.scrollLeft = inputRef.current.scrollWidth;
    }
  }, [userInput]);

  // Focus input when there's an initial question
  useEffect(() => {
    if (initialQuestion && inputRef.current) {
      // Give it a moment for the component to fully render
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [initialQuestion]);

  // Restart chat session when there's an initial question
  useEffect(() => {
    // Check if we've already restarted (using URL query param to persist across reloads)
    const hasRestarted = router.query.restarted === 'true';

    if (initialQuestion && !hasRestarted) {
      const restartChatSession = async () => {
        try {
          await restartSession({
            initiatorId: chatSession?.initiatorId,
            recipientId: chatSession?.recipientId,
            type: chatSession?.type,
          });

          // Add query param to prevent restart loop after reload
          const currentQuery = { ...router.query, restarted: 'true' };
          router.replace({
            pathname: router.pathname,
            query: currentQuery,
          });
        } catch (error) {
          console.error('Failed to restart chat session:', error);
        }
      };

      restartChatSession();
    }
  }, [
    initialQuestion,
    chatSession?.initiatorId,
    chatSession?.recipientId,
    chatSession?.type,
    router,
  ]);

  // Simplified state management
  const initialClickedOptions = useMemo(() => {
    const optionsMap = {};
    chatSession?.messages?.forEach((msg, index) => {
      if (msg.role === 'assistant') {
        const options = parseMessageOptions(msg);
        if (chatSession?.messages?.[index + 1]) {
          const optionIndex = options.indexOf(
            chatSession?.messages?.[index + 1].content,
          );
          const optionKey = `${index}-${optionIndex}`;
          optionsMap[optionKey] = true;
        }
      }
    });
    return optionsMap;
  }, [chatSession?.messages]);

  const [clickedOptions, setClickedOptions] = useState(
    initialClickedOptions || {},
  );
  const [currentIndex, setCurrentIndex] = useState(
    chatSession?.messages?.length - 1 || 0,
  );
  const [chatState, setChatState] = useState({
    step: chatSession?.step || 'PREDEFINED',
    status: chatSession?.status || 'ACTIVE',
    selectedOption: null,
    paymentStatus: chatSession.paymentStatus,
  });

  const [session, setSession] = useState(() => ({
    ...chatSession,
    messages: null,
  }));

  const showErrorToast = useCallback(() => {
    toast({
      title: 'संदेश भेजने में त्रुटि',
      description: 'कृपया पुनः प्रयास करें।',
      status: 'error',
    });
  }, [toast]);

  // Send current message
  const sendCurrent = useCallback(async () => {
    const text = userInput.trim();
    if (!text) return;

    // Stop microphone if it's listening
    setStopMicListening(true);
    setTimeout(() => setStopMicListening(false), 100);

    logAmplitudeEvent('CAPTURED', 'CHAT', 'PASHUPARTNER', { VALUE: text });

    await handleUserMessage(text);
    setUserInput('');
  }, [userInput, handleUserMessage]);

  const scrollToBottom = useCallback(() => {
    if (chatEndRef?.current) {
      setTimeout(() => {
        chatEndRef?.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'end',
        });
      }, 100);
    }
  }, []);

  const handleOptionSelect = async (option, optionIndex, index) => {
    try {
      if (isLoading) return;
      // Check if this option has already been clicked

      const optionKey = `${currentIndex}-${optionIndex}`;
      if (
        isAnyOptionClickedInThisMessage(index, clickedOptions) ||
        currentIndex > index ||
        chatState.step === 'COMPLETED'
      ) {
        return; // Don't allow clicking the same option multiple times
      }
      setIsLoading(true);

      // Add user messag
      const { userMessage, msgRes } = await handleUserSelectedMessage(
        optionKey,
        option,
        optionIndex,
        index,
      );
      let secondMsgRes;
      if (
        ['AI_RESPONSE'].includes(chatState.step) ||
        ['AI_RESPONSE'].includes(msgRes.chatPhase)
      ) {
        await handleAIResponse(userMessage, msgRes.chatPhase, optionKey);
      } else {
        await handlePredefinedResponse(index, userMessage, optionKey);
      }
    } catch (error) {
      console.error('Error handling option select:', error);
      showErrorToast();
    } finally {
      setIsLoading(false);
    }
  };

  const handleUserSelectedMessage = async (
    optionKey,
    option,
    optionIndex,
    index,
  ) => {
    try {
      const msgRes = await sendMessage({
        content: option,
        senderId: session?.initiatorId,
        receiverId: session?.recipientId,
        chatSessionId: session?.id,
        contentType: 'TEXT',
      });

      if (!msgRes.status) {
        showErrorToast();
        return;
      }
      setChatState((prev) => ({
        ...prev,
        step: msgRes.chatPhase,
        paymentStatus: msgRes.chatPaymentStatus || prev.paymentStatus,
      }));
      setClickedOptions((prev) => ({
        ...prev,
        [optionKey]: true,
      }));

      const userMessage = {
        role: 'user',
        content: msgRes?.data?.content,
        contentType: msgRes?.data?.contentType,
        time: msgRes?.data?.createdAt,
        hasOptions: true,
      };
      setTimeout(() => {
        setMessages((prev) => [...prev, userMessage]);
        setCurrentIndex((prev) => prev + 1);
      }, 500);
      setTimeout(() => {
        setIsTyping(true);
      }, 1000);

      return { userMessage, msgRes };
    } catch (error) {
      return false;
    }
  };

  const handleAIResponse = async (userMessage, chatPhase, optionKey) => {
    const requestMessages = messages
      .filter((msg) => !msg.isRemoved)
      .map((msg) => ({
        role: msg?.role,
        content: msg?.content,
      }));

    requestMessages.push({
      role: 'user',
      content: userMessage.content,
    });

    try {
      const response = await sendBotMessage({
        senderId: session?.recipientId,
        receiverId: session?.initiatorId,
        chatSessionId: session?.id,
        messages: requestMessages,
      });

      if (!response.status) {
        handleResponseError(optionKey);
        return;
      }
      aiResponseCount.current = aiResponseCount.current + 1;
      setTimeout(() => {
        setMessages((prev) => [
          ...prev,
          {
            role: 'assistant',
            content: response?.data?.content,
            contentType: response?.data?.contentType,
            time: response?.data?.createdAt,
            hasOptions: response.chatPhase === 'PREDEFINED',
          },
        ]);
        setCurrentIndex((prev) => prev + 1);
        setChatState((prev) => ({ ...prev, step: response.chatPhase }));
        setIsTyping(false);
      }, 3000);
    } catch (error) {
      console.error('AI response error:', error);
      handleResponseError(optionKey);
    }
  };

  // Handle predefined responses
  const handlePredefinedResponse = async (index, userMessage, optionKey) => {
    try {
      const predefinedResponse = await sendMessage({
        content: replacePlaceholders(
          predefinedMessages.dietPlannerAssistantMessages[index + 2],
          [...messages, userMessage],
        ),
        senderId: session?.recipientId,
        receiverId: session?.initiatorId,
        chatSessionId: session?.id,
        contentType: 'TEXT',
      });

      if (!predefinedResponse.status) {
        handleResponseError(optionKey);
        return;
      }

      setTimeout(() => {
        setMessages((prev) => [
          ...prev,
          {
            role: 'assistant',
            content: predefinedResponse?.data?.content,
            contentType: predefinedResponse?.data?.contentType,
            time: predefinedResponse?.data?.createdAt,
            hasOptions: true,
          },
        ]);
        setCurrentIndex((prev) => prev + 1);
        setIsTyping(false);
      }, 3000);
    } catch (error) {
      console.error('Predefined response error:', error);
      handleResponseError(optionKey);
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages?.length, isTyping, scrollToBottom]);
  useEffect(() => {
    logAmplitudeEvent('LANDED', 'CHAT', 'PASHUPARTNER', {
      SOURCE: src || 'NA',
      TYPE: chatSession?.type,
    });
  }, [chatSession?.type, src]);

  const handleUserMessage = async (text) => {
    if (!text.trim()) return;

    // 1. Send user message
    const msgRes = await sendMessage({
      content: text,
      senderId: chatSession?.initiatorId,
      receiverId: chatSession?.recipientId,
      chatSessionId: chatSession?.id,
      contentType: 'TEXT',
    });
    if (!msgRes.status) {
      showErrorToast();
      setIsTyping(false);
      return;
    }
    // 2. Add user message to chat
    setMessages((prev) => [
      ...prev,
      {
        role: 'user',
        content: msgRes?.data?.content,
        contentType: msgRes?.data?.contentType,
        time: msgRes?.data?.createdAt,
        hasOptions: false,
      },
    ]);
    setIsTyping(true);
    // 3. If AI response step → send to bot
    if (
      ['AI_RESPONSE'].includes(chatSession.step) ||
      ['AI_RESPONSE'].includes(msgRes.chatPhase)
    ) {
      const requestMessages = messages
        .filter((msg) => !msg.isRemoved)
        .map((msg) => ({
          role: msg?.role,
          content: msg?.content,
        }));

      requestMessages.push({
        role: 'user',
        content: msgRes?.data?.content,
      });

      const secondMsgRes = await sendBotMessage({
        senderId: chatSession?.recipientId,
        receiverId: chatSession?.initiatorId,
        chatSessionId: chatSession?.id,
        messages: requestMessages,
      });
      if (!secondMsgRes.status) {
        handleResponseError();
        return;
      }

      setTimeout(() => {
        setMessages((prev) => [
          ...prev,
          {
            role: 'assistant',
            content: secondMsgRes?.data?.content,
            contentType: secondMsgRes?.data?.contentType,
            time: secondMsgRes?.data?.createdAt,
            hasOptions: false,
          },
        ]);
        setIsTyping(false);
      }, 3000);
    } else {
      setIsTyping(false);
      showErrorToast();
      return;
    }
  };

  const handleResponseError = (optionKey) => {
    setMessages((prev) => prev.slice(0, -1));
    setIsTyping(false);
    showErrorToast();
  };

  const NumberOfAnswerGiven = useMemo(
    () => messages.filter((msg) => msg.role === 'user').length,
    [messages],
  );

  return (
    <>
      <div className="chatscreen mt-[56px] bg-blue-100 h-[calc(100vh-56px)] scroll-smooth overflow-auto">
        <p className="mx-auto w-fit bg-white my-4 py-1 px-4 rounded-full shadow-md text-gray-600 text-sm">
          {formatChatDate(chatSession.createdAt)}
        </p>
        {messages.map((message, index) => (
          <EnhancedMessageRenderer
            key={`message-${index}`}
            message={message}
            index={index}
            handleOptionSelect={handleOptionSelect}
            clickedOptions={clickedOptions}
            chatState={{}}
            chatSessionId={chatSession.id}
            setIsTyping={setIsTyping}
            paymentMessageCtaClick={paymentMessageCtaClick}
          />
        ))}
        {isTyping && <TypingEffect />}
        {(chatSession?.metaData?.remainingSeconds > 0 || isEligible) &&
          !(source === 'ROBOT' && messages.length < 2) && (
            <div className="w-full px-2 pt-1 pb-4 bg-blue-100 fixed bottom-0 z-10">
              <div className="max-w-5xl mx-auto flex items-center gap-1 sm:gap-2">
                {/* Input field */}
                <div className="relative flex-1 min-w-[80px] sm:min-w-[140px]">
                  <input
                    ref={inputRef}
                    name="message"
                    disabled={isTyping || chatSession?.paymentStatus !== 'PAID'}
                    onChange={(e) => setUserInput(e.target.value)}
                    onFocus={() => {
                      setStopMicListening(true);
                      // Reset the stop trigger after a brief moment
                      setTimeout(() => setStopMicListening(false), 100);
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !isTyping) {
                        sendCurrent();
                      }
                    }}
                    value={userInput}
                    placeholder={`अपना संदेश टाइप करें${
                      audioChatEligible ? ' या बोलें' : ''
                    }...`}
                    className="w-full p-3 pr-8 h-10 text-sm rounded-full border border-gray-300 
                   placeholder:text-gray-500 focus:outline-none focus:ring-2 transition bg-gray-100 focus:ring-teal-400"
                  />
                  {userInput && (
                    <button
                      onClick={() => {
                        setUserInput('');
                        inputRef.current?.focus();
                      }}
                      type="button"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                      aria-label="Clear input"
                      title="Clear"
                    >
                      ✕
                    </button>
                  )}
                </div>

                {/* Controls */}
                <div className="flex items-center gap-1 sm:gap-2 shrink-0">
                  {/* Speech Mic Button - shows for eligible users */}
                  {audioChatEligible && (
                    <SpeechMicButton
                      disabled={
                        isTyping || chatSession?.paymentStatus !== 'PAID'
                      }
                      onTranscriptChange={setUserInput}
                      stopListening={stopMicListening}
                    />
                  )}

                  {/* Send button */}
                  <button
                    onClick={sendCurrent}
                    type="button"
                    disabled={isTyping || chatSession?.paymentStatus !== 'PAID'}
                    className="flex-shrink-0 w-9 h-9 sm:w-11 sm:h-11 rounded-full flex items-center justify-center 
                   text-white transition bg-teal-500 hover:bg-teal-600 disabled:opacity-50"
                    aria-label="Send"
                    title="Send"
                  >
                    ➤
                  </button>
                </div>
              </div>
            </div>
          )}
        {chatSession?.metaData?.remainingSeconds <= 0 && !isEligible && (
          <div className="w-full text-sm fixed bottom-0 text-center self-center flex justify-center items-center bg-blue-100 h-16">
            ❌ समय समाप्त हुआ, समय बढ़ाने के लिए
            <span
              className="font-bold underline cursor-pointer px-1"
              onClick={() => paymentMessageCtaClick(true)}
            >
              20 का पेमेंट
            </span>
            करे
          </div>
        )}

        <div ref={chatEndRef} className={`${isTyping ? 'h-20' : 'h-14'}`}></div>
      </div>
    </>
  );
};

export default ChatScreen;
