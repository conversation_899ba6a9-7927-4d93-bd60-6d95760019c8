import { useState } from 'react';

import { useRouter } from 'next/router';

import PaymentBottomSheet from 'components/BotPartnerCard/PaymentBottomSheet';

import { BOT_PARTNERS } from 'data/bot-partners';
import useAppPaywall from 'modules/app-subscription/hooks/useAppPaywall';
import WalletProvider from 'providers/Wallet';

import ChatHeaderV2 from '../component/ChatHeaderV2';
import ChatScreen from './ChatScreen';

const PashuPartnerModule = (props) => {
  const [isTyping, setIsTyping] = useState(false);
  const [chatSession, setChatSession] = useState(props?.chatSession || {});
  const [isBottomSheetOpen, setISBottomSheetOpen] = useState(false);
  const [shouldSaveTime, setShouldSaveTime] = useState(true);
  const { isEligible } = useAppPaywall();

  const [messages, setMessages] = useState(() => {
    let initialMessages = [...(chatSession?.messages || [])];
    if (!isEligible && chatSession?.metaData?.remainingSeconds === 0) {
      const paymentMessage = {
        role: 'assistant',
        content: '-------0_PASHU_PARTNER-----------',
        contentType: 'TEXT',
        time: new Date().toISOString(),
        isRemoved: true,
        hasOptions: true,
      };
      initialMessages.push(paymentMessage);
    } else if (!isEligible && chatSession?.metaData?.remainingSeconds < 120) {
      const paymentMessage = {
        role: 'assistant',
        content: '-------2_PASHU_PARTNER-----------',
        contentType: 'TEXT',
        time: new Date().toISOString(),
        isRemoved: true,
        hasOptions: true,
      };
      initialMessages.push(paymentMessage);
    }
    return initialMessages;
  });

  const router = useRouter();

  const paymentMessageCtaClick = () => {
    setShouldSaveTime(false);
    setISBottomSheetOpen(true);
  };
  return (
    <WalletProvider walletData={props?.walletData}>
      <div className="border-t border-primary-600">
        <ChatHeaderV2
          isTyping={isTyping}
          use={props.use}
          chatSession={chatSession}
          setChatSession={setChatSession}
          setMessages={setMessages}
          image={props?.image}
          shouldSaveTime={shouldSaveTime}
          isEligible={isEligible}
          source={props?.source}
        />
        <ChatScreen
          chatSession={chatSession}
          setChatSession={setChatSession}
          isTyping={isTyping}
          setIsTyping={setIsTyping}
          paymentMessageCtaClick={paymentMessageCtaClick}
          setMessages={setMessages}
          messages={messages}
          isEligible={isEligible}
          source={props?.source}
          initialQuestion={props?.initialQuestion}
        />
        {!isEligible && (
          <PaymentBottomSheet
            isOpen={isBottomSheetOpen}
            onClose={() => {
              setShouldSaveTime(true);
              setISBottomSheetOpen(false);
            }}
            afterSuccess={() => {
              setISBottomSheetOpen(false);
              router.reload();
            }}
            partner={BOT_PARTNERS.find((ele) => ele.type === chatSession.type)}
          />
        )}
      </div>
    </WalletProvider>
  );
};
export default PashuPartnerModule;
