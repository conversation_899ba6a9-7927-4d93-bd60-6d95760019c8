const predefinedMessages = {
  dietPlanner: [
    {
      role: 'assistant',
      content:
        'नमस्ते! मैं पशुमित्र हूँ, \nआपके पशुओं के आहार विशेषज्ञ। आपके पास कौन सा पशु है?\n\n*%$🐄 गाय\n*%$🐃 भैंस',
    },
    { role: 'user', content: '🐄 गाय' },
    {
      role: 'assistant',
      content:
        'बहुत अच्छा! आपके पास गाय है। मुझे आपकी गाय के बारे में कुछ और जानकारी चाहिए ताकि मैं सबसे अच्छा आहार सुझाव दे सकूँ।\n\nगाय का अनुमानित वज़न कितना है?\n*%$⚖️ 300 kg से कम\n*%$⚖️ 300-400 kg\n*%$⚖️ 400-500 kg\n*%$⚖️ 500 kg से अधिक',
    },
    { role: 'user', content: '⚖️ 300 kg से कम' },
    {
      role: 'assistant',
      content:
        'ठीक है, आपकी गाय 300 kg से कम वज़न की है।\n\nअब बताइए, वर्तमान में यह कौन सी ब्यात (lactation number) पर है?\n*%$🍼 शुरुआती (1-2)\n*%$➡️ मध्यम (3-6)\n*%$⬇️ अंतिम (6-10)',
    },
    { role: 'user', content: '➡️ मध्यम (3-6)' },
    {
      role: 'assistant',
      content:
        'समझ गया, आपकी गाय मध्यम ब्यात (3-6) पर है।\n\nवर्तमान में आपकी गाय का दूध उत्पादन कितना है?\n*%$⬆️ ज्यादा (15+ लीटर प्रतिदिन)\n*%$➡️ मध्यम (10-15 लीटर प्रतिदिन)\n*%$⬇️ कम (5-10 लीटर प्रतिदिन)\n*%$📉 बहुत कम (5 लीटर से कम प्रतिदिन)',
    },
    { role: 'user', content: '⬆️ ज्यादा (15+ लीटर प्रतिदिन)' },
    {
      role: 'assistant',
      content:
        'बहुत अच्छा! आपकी गाय 15+ लीटर प्रतिदिन दूध दे रही है।\n\nक्या आपकी गाय वर्तमान में गर्भवती है?\n*%$✔️ हाँ\n*%$❌ नहीं',
    },
    { role: 'user', content: '❌ नहीं' },
    {
      role: 'assistant',
      content:
        'समझ गया, आपकी गाय वर्तमान में गर्भवती नहीं है।\n\nआप इस आहार योजना से क्या प्राप्त करना चाहते हैं?\n*%$🥛 अधिक दूध उत्पादन\n*%$💪 बेहतर स्वास्थ्य\n*%$🌱 अन्य (कृपया बताएं)',
    },
    { role: 'user', content: '💪 बेहतर स्वास्थ्य' },
    { role: 'user', content: 'hi' },
    {
      role: 'assistant',
      content:
        'नमस्ते! मैं पशुमित्र हूँ, आपके पशुओं के आहार विशेषज्ञ। आपके पास कौन सा पशु है?\n\n*%$🐄 गाय\n*%$🐃 भैंस',
    },
    { role: 'user', content: '🐄 गाय' },
    {
      role: 'assistant',
      content:
        'बहुत अच्छा! आपके पास गाय है। मुझे आपकी गाय के बारे में कुछ और जानकारी चाहिए ताकि मैं सबसे अच्छा आहार सुझाव दे सकूँ।\n\nगाय का अनुमानित वज़न कितना है?\n*%$⚖️ 300 kg से कम\n*%$⚖️ 300-400 kg\n*%$⚖️ 400-500 kg\n*%$⚖️ 500 kg से अधिक',
    },
    { role: 'user', content: '⚖️ 300 kg से कम' },
    {
      role: 'assistant',
      content:
        'ठीक है, आपकी गाय 300 kg से कम वज़न की है।\n\nअब बताइए, वर्तमान में यह कौन सी ब्यात (lactation number) पर है?\n*%$🍼 शुरुआती (1-2)\n*%$➡️ मध्यम (3-6)\n*%$⬇️ अंतिम (6-10)',
    },
    { role: 'user', content: '➡️ मध्यम (3-6)' },
    {
      role: 'assistant',
      content:
        'समझ गया, आपकी गाय मध्यम ब्यात (3-6) पर है।\n\nवर्तमान में आपकी गाय का दूध उत्पादन कितना है?\n*%$⬆️ ज्यादा (15+ लीटर प्रतिदिन)\n*%$➡️ मध्यम (10-15 लीटर प्रतिदिन)\n*%$⬇️ कम (5-10 लीटर प्रतिदिन)\n*%$📉 बहुत कम (5 लीटर से कम प्रतिदिन)',
    },
    { role: 'user', content: '⬆️ ज्यादा (15+ लीटर प्रतिदिन)' },
    {
      role: 'assistant',
      content:
        'बहुत अच्छा! आपकी गाय 15+ लीटर प्रतिदिन दूध दे रही है।\n\nक्या आपकी गाय वर्तमान में गर्भवती है?\n*%$✔️ हाँ\n*%$❌ नहीं',
    },
    { role: 'user', content: '❌ नहीं' },
    {
      role: 'assistant',
      content:
        'समझ गया, आपकी गाय वर्तमान में गर्भवती नहीं है।\n\nआप इस आहार योजना से क्या प्राप्त करना चाहते हैं?\n*%$🥛 अधिक दूध उत्पादन\n*%$💪 बेहतर स्वास्थ्य\n*%$🌱 अन्य (कृपया बताएं)',
    },
    { role: 'user', content: '💪 बेहतर स्वास्थ्य' },
  ],
  dietPlannerAssistantMessages: {
    0: 'नमस्ते! मैं पशुमित्र हूँ, \nआपके पास कौन सा पशु है?\n\n*%$🐄 गाय\n*%$🐃 भैंस',
    2: 'ठीक है!\n<<0>> का अनुमानित वज़न कितना है?\n*%$⚖️ 300 kg से कम\n*%$⚖️ 300-400 kg\n*%$⚖️ 400-500 kg\n*%$⚖️ 500 kg से अधिक',
    4: 'अब बताइए, <<0>> वर्तमान में कौन सी ब्यात (lactation number) पर है?\n*%$🍼 शुरुआती (1-2)\n*%$➡️ मध्यम (3-6)\n*%$⬇️ अंतिम (6-10)',
    6: 'आपकी <<0>> दिन का कितना दूध देती है? (लीटर में चुनें)\n*%$⬆️ ज्यादा (15+ लीटर प्रतिदिन)\n*%$➡️ मध्यम (10-15 लीटर प्रतिदिन)\n*%$⬇️ कम (5-10 लीटर प्रतिदिन)\n*%$📉 बहुत कम (5 लीटर से कम प्रतिदिन)',
    8: 'बहुत अच्छा! \nक्या आपकी <<0>> वर्तमान में गर्भवती है?\n*%$✔️ हाँ\n*%$❌ नहीं',
    10: 'समझ गया, \nआप इस आहार योजना से क्या प्राप्त करना चाहते हैं?\n*%$🥛 अधिक दूध उत्पादन\n*%$💪 बेहतर स्वास्थ्य\n*%$🤰 गर्भावस्था संबंधित',
  },
  ratePredictorAssistantMessages: {
    0: 'नमस्कार!\n कौन से पशु का रेट जानना चाहते हैं?\n\n*%$🐄 गाय\n*%$🐃 भैंस',
    2: 'अपनी <<0>> की नस्ल चुनें:\n\n*%$साहिवाल\n*%$गिर\n*%$होल्स्टीन\n*%$जर्सी\n*%$मुर्रा\n*%$थारपारकर\n*%$अन्य',
    4: 'आपकी <<0>> किस ब्यात (Lactation Number) पर है?\n\n*%$0\n*%$1\n*%$2\n*%$3\n*%$4\n*%$5\n*%$6+',
    6: 'आपकी <<0>> दिन का कितना दूध देती है? (लीटर में चुनें)\n\n*%$0\n*%$1\n*%$2\n*%$3\n*%$4\n*%$5\n*%$6\n*%$7\n*%$8\n*%$9\n*%$10\n*%$11\n*%$12\n*%$13\n*%$14\n*%$15\n*%$16\n*%$17\n*%$18\n*%$19\n*%$20+',
    8: 'आपकी <<0>> का वज़न कितना है?\n\n*%$300 kg से कम\n*%$300-400 kg\n*%$400-500 kg\n*%$500-6000 kg',
    10: 'क्या आपकी <<0>> वर्तमान में गर्भवती है?\n\n*%$✔️ हाँ\n*%$❌ नहीं',
    12: 'तो गर्भावस्था का कौन सा महीना चल रहा है?\n\n*%$1-3\n*%$3-6\n*%$6-9',
  },
  PreDefinedLastMessage: [
    '🥛 अधिक दूध उत्पादन',
    '💪 बेहतर स्वास्थ्य',
    '🤰 गर्भावस्था संबंधित',
  ],
};

const BotTypeLabels = {
  BOT_CUSTOMER_SUPPORT: 'कस्टमर सपोर्ट',
  BOT_DIET_PLANNER: 'आहार योजना बनाएँ',
  BOT_RATE_PREDICTOR: 'पशु रेट जानें',
  BOT_POST_RATE_PREDICTOR: 'पशु रेट जानें',
  BOT_PASHU_DOCTOR: 'डॉ दीपिका जी ',
  BOT_PASHU_FEED_EXPERT: 'सुमित्रा देवी जी',
  BOT_PASHU_ACCOUNTANT: 'माधुरी जी',
  BOT_PASHU_DESI_NUSKHE: 'धनी राम जी',
  BOT_PASHU_TRADER: 'नरेश जी',
  USER_CHAT: 'यूज़र चैट',
  BOT_MILK_ROBOT: 'दूध का रोबोट',
  BOT_PREGNANCY_ROBOT: 'ग्याभिन का रोबोट',
  BOT_FEED_ROBOT: 'चारे का रोबोट',
  BOT_HEALTH_ROBOT: 'स्वास्थ्य का रोबोट',
  BOT_ASTROLOGY_ROBOT: 'ज्योत्षी रोबोट',
  BOT_FRIEND_ROBOT: 'दोस्त रोबोट',
  BOT_GENERAL_ROBOT: 'सामान्य रोबोट',
};

const Last_Message_TO_Identify_Payment =
  'आप इस आहार योजना से क्या प्राप्त करना चाहते हैं?\n*%$🥛 अधिक दूध उत्पादन\n*%$💪 बेहतर स्वास्थ्य\n*%$🤰 गर्भावस्था संबंधित';

const chatType = {
  BOT_CUSTOMER_SUPPORT: 'BOT_CUSTOMER_SUPPORT',
  BOT_DIET_PLANNER: 'BOT_DIET_PLANNER',
  BOT_RATE_PREDICTOR: 'BOT_RATE_PREDICTOR',
  BOT_POST_RATE_PREDICTOR: 'BOT_POST_RATE_PREDICTOR',
  BOT_PASHU_DOCTOR: 'BOT_PASHU_DOCTOR',
  BOT_PASHU_FEED_EXPERT: 'BOT_PASHU_FEED_EXPERT',
  BOT_PASHU_ACCOUNTANT: 'BOT_PASHU_ACCOUNTANT',
  BOT_PASHU_DESI_NUSKHE: 'BOT_PASHU_DESI_NUSKHE',
  BOT_PASHU_TRADER: 'BOT_PASHU_TRADER',
  BOT_MILK_ROBOT: 'BOT_MILK_ROBOT',
  BOT_PREGNANCY_ROBOT: 'BOT_PREGNANCY_ROBOT',
  BOT_FEED_ROBOT: 'BOT_FEED_ROBOT',
  BOT_HEALTH_ROBOT: 'BOT_HEALTH_ROBOT',
  BOT_ASTROLOGY_ROBOT: 'BOT_ASTROLOGY_ROBOT',
  BOT_FRIEND_ROBOT: 'BOT_FRIEND_ROBOT',
  BOT_GENERAL_ROBOT: 'BOT_GENERAL_ROBOT',
};
const chatEndpoint = {
  BOT_CUSTOMER_SUPPORT: 'customer-support',
  BOT_DIET_PLANNER: 'diet-planner',
  BOT_RATE_PREDICTOR: 'rate-predictor',
  USER_CHAT: 'user-chat',
  BOT_POST_RATE_PREDICTOR: 'rate-predictor',
  BOT_PASHU_DOCTOR: 'pashu-doctor',
  BOT_PASHU_FEED_EXPERT: 'pashu-feed-expert',
  BOT_PASHU_ACCOUNTANT: 'pashu-accountant',
  BOT_PASHU_DESI_NUSKHE: 'pashu-desi-nuskhe',
  BOT_PASHU_TRADER: 'pashu-trader',
  BOT_MILK_ROBOT: 'milk-robot',
  BOT_PREGNANCY_ROBOT: 'pregnancy-robot',
  BOT_FEED_ROBOT: 'feed-robot',
  BOT_HEALTH_ROBOT: 'health-robot',
  BOT_ASTROLOGY_ROBOT: 'astrology-robot',
  BOT_FRIEND_ROBOT: 'friend-robot',
  BOT_GENERAL_ROBOT: 'generic-robot',
};

const BOT_IMAGES = {
  BOT_CUSTOMER_SUPPORT: 'customer-support',
  BOT_DIET_PLANNER:
    'https://static-assets.animall.in/static/images/chatbot/animall_logo.svg',
  BOT_RATE_PREDICTOR:
    'https://static-assets.animall.in/static/images/chatbot/animall_logo.svg',
  USER_CHAT: 'user-chat',
  BOT_POST_RATE_PREDICTOR:
    'https://static-assets.animall.in/static/images/chatbot/animall_logo.svg',
  BOT_PASHU_DOCTOR:
    'https://static-assets.animall.in/static/images/ai-partners/ai-doctor.jpg',
  BOT_PASHU_FEED_EXPERT:
    'https://static-assets.animall.in/static/images/ai-partners/ai-feed.jpg',
  BOT_PASHU_ACCOUNTANT:
    'https://static-assets.animall.in/static/images/ai-partners/ai-accounting.jpg',
  BOT_PASHU_DESI_NUSKHE:
    'https://static-assets.animall.in/static/images/ai-partners/ai-remedies.jpg',
  BOT_PASHU_TRADER:
    'https://static-assets.animall.in/static/images/ai-partners/ai-business.jpg',
  BOT_MILK_ROBOT:
    'https://static-assets.animall.in/static/images/robots/dudhRobot.png',
  BOT_PREGNANCY_ROBOT:
    'https://static-assets.animall.in/static/images/robots/gabhinRobot.png',
  BOT_FEED_ROBOT:
    'https://static-assets.animall.in/static/images/robots/charaRobot.png',
  BOT_HEALTH_ROBOT:
    'https://static-assets.animall.in/static/images/robots/doctorRobot.png',
  BOT_ASTROLOGY_ROBOT:
    'https://static-assets.animall.in/static/images/robots/jyotisiRobot.png',
  BOT_FRIEND_ROBOT:
    'https://static-assets.animall.in/static/images/robots/dostRobot.png',
  BOT_GENERAL_ROBOT:
    'https://static-assets.animall.in/static/images/robots/mainRobot.png',
};

export {
  predefinedMessages,
  chatType,
  Last_Message_TO_Identify_Payment,
  BotTypeLabels,
  chatEndpoint,
  BOT_IMAGES,
};
