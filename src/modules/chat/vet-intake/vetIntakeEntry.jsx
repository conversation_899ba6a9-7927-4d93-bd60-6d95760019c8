import React from 'react';

import { useRouter } from 'next/router';

import { H4 } from 'components/ui/typography';

import useAppPaywall from 'modules/app-subscription/hooks/useAppPaywall';
import ProtectedSection from 'modules/home/<USER>/components/ProtectedSection';
import { useGrowthUser } from 'providers/GrowthUserProvider';
import { useShowPaywall } from 'providers/PaywallProvider';

const VetIntakeEntry = () => {
  const { isGrowthUser, isLoaded } = useGrowthUser();
  const { isEligible: isAppPaywallEligible, isPremiumUser } = useAppPaywall();
  const { showPaywall } = useShowPaywall();
  const router = useRouter();

  const handleBannerClick = () => {
    if (isPremiumUser) {
      router.push('/chat/vet-intake?source=home_banner');
    } else {
      showPaywall({ source: 'HOME_PAGE', section: 'VET_INTAKE_ENTRY' });
    }
  };

  if (!isLoaded) return null;

  if (!isAppPaywallEligible && !!isGrowthUser) {
    return null;
  }

  return (
    <ProtectedSection
      className="inline-block w-full"
      source="HOME_PAGE"
      featureName="VET_INTAKE_ENTRY"
      shouldAuthenticate={!isPremiumUser}
      showPopupOnLock={false}
    >
      <div
        className="p-3 cursor-pointer max-w-[450px] mb-4 md:mx-auto border-4 border-white rounded-2xl shadow-md flex-1"
        style={{
          background: 'linear-gradient(180deg, #B1F2ED 0%, #70E7DE 119.29%)',
          boxShadow: '0px 0px 6px rgba(0, 0, 0, 0.15)',
        }}
        onClick={handleBannerClick}
      >
        <div className="flex flex-row items-center justify-evenly">
          {/* Image */}
          <div className="flex-shrink-0">
            <img
              src="https://static-assets.animall.in/static/images/vet-doctors.png"
              alt="Veterinary Doctors"
              className="w-20 h-16 object-contain rounded-lg"
            />
          </div>
          {/* Title */}
          <div className="flex flex-col items-center justify-center">
            <div className="flex flex-row items-center justify-center gap-2">
              <H4 className="text-primary-600 text-center">डॉक्टर से कॉल</H4>
              <svg
                width="7"
                height="11"
                viewBox="0 0 7 11"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M4.76332 5.50013L1.10052 1.83727C0.763367 1.50011 0.76337 0.953481 1.10052 0.61633C1.43768 0.27918 1.98431 0.279181 2.32146 0.616333L6.59479 4.88966C6.93195 5.22681 6.93194 5.77345 6.59479 6.1106L2.32146 10.3839C1.98431 10.721 1.43768 10.721 1.10053 10.3839C0.763372 10.0467 0.763373 9.50006 1.10053 9.16291L4.76332 5.50013Z"
                  fill="#14776F"
                />
              </svg>
            </div>

            {/* Subtitle */}
            <div className="text-primary-600 text-sm text-center font-rajdhani">
              पूछें दूध, आहार, सेहत सब
            </div>
          </div>
        </div>
      </div>
    </ProtectedSection>
  );
};

export default VetIntakeEntry;
