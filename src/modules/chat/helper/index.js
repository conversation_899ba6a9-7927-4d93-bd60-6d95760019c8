import axios from 'lib/axios';

const getInitialBotMessage = (botType, post) => {
  // These could come from your database or config
  const initialMessages = {
    BOT_DIET_PLANNER: {
      content:
        'नमस्ते! मैं पशुमित्र हूँ, \nआपके पास कौन सा पशु है?\n\n*%$🐄 गाय\n*%$🐃 भैंस',
    },
    BOT_CUSTOMER_SUPPORT: {
      content:
        'नमस्ते! मैं आपकी कैसे सहायता कर सकता हूँ?\n\n*%$📦 ऑर्डर की स्थिति\n*%$💰 भुगतान से संबंधित समस्या\n*%$❓ अन्य प्रश्न',
    },
    BOT_RATE_PREDICTOR: {
      content:
        'नमस्कार!\n कौन से पशु का रेट जानना चाहते हैं?\n\n*%$🐄 गाय\n*%$🐃 भैंस',
    },
    BOT_POST_RATE_PREDICTOR: {
      content: `
    ✅ आप इस पशु की क़ीमत जानना चाहते हैं
    - **पशु**: ${post?.animalType}
    - **नस्ल**: ${post?.breed}
    - **ब्यात संख्या**: ${post?.lactation}
    - **दूध उत्पादन (लीटर/दिन)**: ${post?.currentMilk} लीटर
    - **वज़न**: 300+
    - **गर्भवती है?**: ${post?.isPregnant || 'नहीं'}
    - **गर्भावस्था का महीना**: ${post?.pregnancyMonth || 0}
    - **स्थान**: ${post?.locationName}
    - **क़ीमत**: ${post?.price}
    
    क्या ये जानकारी सही है?\n
    \n*%$✅ हाँ, रेट बताओ
    \n*%$🔁 नहीं, जानकारी बदलनी है
      `,
    },
    BOT_PASHU_DOCTOR: {
      content:
        'नमस्ते! मैं डॉ दीपिका, आपकी पशु डॉक्टर हूं। मैं आपके पशुओं की स्वास्थ्य समस्याओं में मदद करने के लिए यहां हूं। कृपया बताएं कि आपके पशु को क्या तकलीफ है?',
    },
    BOT_PASHU_TRADER: {
      content:
        'नमस्ते! मैं नरेश, पशु व्यापार एक्सपर्ट हूं। मैं पशुओं की खरीद और बिक्री में आपकी मदद करूंगा। कृपया बताएं कि आप किस प्रकार का पशु खरीदना या बेचना चाहते हैं?',
    },
    BOT_PASHU_DESI_NUSKHE: {
      content:
        'नमस्ते! मैं धनी राम, देसी नुस्खों में एक्सपर्ट हूं। मैं घरेलू और असरदार उपायों से आपके पशु की देखभाल में मदद करूंगा। किस समस्या का समाधान चाहिए?',
    },
    BOT_PASHU_ACCOUNTANT: {
      content:
        'नमस्ते! मैं माधुरी, आपकी मुनीम हूं। मैं आपके पशुपालन खर्चों और कमाई का हिसाब रखने में मदद करूंगी। क्या आप खर्च जोड़ना चाहते हैं या रिपोर्ट देखना चाहते हैं?',
    },
    BOT_PASHU_FEED_EXPERT: {
      content:
        'नमस्ते! मैं सुमित्रा देवी, पशु आहार एक्सपर्ट हूं। मैं आपके पशु के लिए सही चारा और पोषण योजना बनाने में मदद कर सकती हूं। चलिए आपके पशु की जानकारी से शुरू करें।',
    },
    BOT_MILK_ROBOT: {
      content:
        'नमस्ते जी! मैं दूध का रोबोट हूँ।\nबताएँ आपको दूध से संबंधित क्या समस्या है -\n\n*%$दूध कम आ रहा है\n*%$दूध में फैट नहीं है\n*%$बच्चा होने के बाद दूध घट गया\n*%$एक थन से दूध नहीं आ रहा\n*%$दूध में दही जैसे टुकड़े आ रहे हैं\n*%$और कुछ समस्या है',
    },
    BOT_PREGNANCY_ROBOT: {
      content:
        'नमस्ते जी! मैं ग्याभिन का रोबोट हूँ।\nबताएँ आपको गर्भावस्था से संबंधित क्या समस्या है -\n\n*%$हीट में नहीं आ रही है\n*%$बार-बार repeat हो रहा है\n*%$गर्भ नहीं ठहर रहा\n*%$बच्चा होने के बाद दिक्कत\n*%$ग्याभिन को क्या खिलायें\n*%$और कुछ समस्या है',
    },
    BOT_FEED_ROBOT: {
      content:
        'नमस्ते जी! मैं चारे का रोबोट हूँ।\nबताएँ आपको चारे से संबंधित क्या समस्या है -\n\n*%$खाना नहीं खा रहा\n*%$वजन नहीं बढ़ रहा\n*%$भूख कम हो गई है\n*%$पानी बहुत कम पी रहा है\n*%$पशु को क्या खिलायें\n*%$और कुछ समस्या है',
    },
    BOT_HEALTH_ROBOT: {
      content:
        'नमस्ते जी! मैं स्वास्थ्य का रोबोट हूँ।\nबताएँ आपको स्वास्थ्य से संबंधित क्या समस्या है -\n\n*%$बुखार है\n*%$नाक से पानी आ रहा है\n*%$थन में सूजन / गांठ है\n*%$जूएं / चिचक / चिचड़ हो गए हैं\n*%$पतला गोबर या खाना पच नहीं रहा\n*%$और कुछ समस्या है',
    },
    BOT_ASTROLOGY_ROBOT: {
      content:
        'नमस्ते जी! मैं ज्योत्षी रोबोट हूँ।\nबताएँ आपको ज्योतिष से संबंधित क्या जानना है -\n\n*%$बार-बार बीमारी का कारण?\n*%$बार-बार नुकसान, उपाय?\n*%$ग्याभिन नहीं हो रही – ग्रह दोष?\n*%$खरीद-बेच के लिए शुभ दिन?\n*%$पशु को किस दिशा में रखें?\n*%$और कुछ समस्या है',
    },
    BOT_FRIEND_ROBOT: {
      content:
        'नमस्ते दोस्त! मैं आपका दोस्त रोबोट हूँ।\nबताएँ आपको किस बात की मदद चाहिए -\n\n*%$मुनाफा समझ नहीं आ रहा\n*%$दूध बेचने का हिसाब चाहिए\n*%$खर्चा ज्यादा, बचत नहीं हो रही\n*%$डेयरी शुरू करनी है, मदद चाहिए\n*%$लोन कैसे मिलेगा?\n*%$और कुछ समस्या है',
    },
    BOT_GENERAL_ROBOT: {
      content:
        'नमस्ते जी! मैं सामान्य रोबोट हूँ। मैं सभी पशुपालन समस्याओं में आपकी मदद कर सकता हूँ।\nबताएँ आपको किस विषय की मदद चाहिए ',
    },
  };

  return initialMessages[botType];
};

export const replacePlaceholders = (template, messages) => {
  // Filter out only user messages
  const userMessages = messages.filter((msg) => msg.role === 'user');

  // Replace placeholders like <<0>>, <<1>> with respective user message content
  return template.replace(/<<(\d+)>>/g, (_, index) => {
    const userMsg = userMessages[Number(index)];
    if (userMsg && userMsg.content)
      if (userMsg.content === '✔️ हाँ') {
        return '';
      }
    return userMsg ? userMsg.content : '';
  });
};

export const replacePlaceholdersRatePredictor = (
  template,
  messages,
  userMessage,
) => {
  // Filter out only user messages
  const userMessages = messages.filter((msg) => msg.role === 'user');
  // Replace placeholders like <<0>>, <<1>> with respective user message content
  switch (userMessage?.content) {
    case '🐃 भैंस': {
      return 'अपनी भैंस की नस्ल चुनें:\n\n*%$मुर्रा\n*%$जाफराबादी\n*%$मेहसाणा\n*%$नागपुरी\n*%$बदवानी\n*%$भदावरी\n*%$अन्य';
    }
    case '🐄 गाय': {
      return 'अपनी गाय की नस्ल चुनें:\n\n*%$साहिवाल\n*%$गिर\n*%$होल्स्टीन\n*%$जर्सी\n*%$थारपारकर\n*%$कांगेयम\n*%$अन्य';
    }
    case '❌ नहीं': {
    }
  }
  return template.replace(/<<(\d+)>>/g, (_, index) => {
    const userMsg = userMessages[Number(index)];
    if (userMsg && userMsg.content)
      if (userMsg.content === '✔️ हाँ') {
        return '';
      }
    return userMsg ? userMsg.content : '';
  });
};

export const isAnyOptionClickedInThisMessage = (index, clickedOptions) =>
  Object.keys(clickedOptions).some((key) => key.startsWith(`${index}-`));

export const makeConfirmationMsgForUserQuestions = (messages, content) => {
  // const msg="✅ आपने पशु की जानकारी भर दी है। क्या आप इन विवरणों <<>> के आधार पर आहार योजना प्राप्त करना चाहेंगे?\n\n*%$✅ हाँ, योजना दिखाओ\n*%$🔁 नहीं, जानकारी बदलनी है";
  const labels = ['पशु', 'वज़न', 'अवस्था', 'दूध उत्पादन', 'गर्भवती'];
  const userMessages = messages
    .filter((msg) => msg.role === 'user')
    .map((msg, index) => `${labels[index]}- ${msg.content}`);
  const msg = `✅ आपने यह जानकारी भरी है: \n - **${userMessages.join(
    '\n - **',
  )}\n - ** उद्देश्य- ${content} \n क्या ये जानकारी सही है? \n*%$✅ हाँ, योजना दिखाओ \n*%$🔁 नहीं, जानकारी बदलनी है `;
  return msg;
};

export const makeConfirmationMsgForUserQuestionsRatePRedictor = (
  messages,
  content,
) => {
  const labels = [
    'पशु',
    'नस्ल',
    'ब्यात संख्या',
    'दूध उत्पादन (लीटर/दिन)',
    'वज़न',
    'गर्भवती है?',
    'गर्भावस्था का महीना',
  ];

  const userMessages = messages
    .filter((msg) => msg.role === 'user')
    .map((msg, index) => `${labels[index]} - ${msg.content}`);
  if (content === '❌ नहीं') {
    userMessages.push(`गर्भवती है? - ${content}`);
  } else {
    userMessages.push(`गर्भावस्था का महीना - ${content}`);
  }
  const msg = `✅ आपने यह जानकारी भरी है:\n - **${userMessages.join(
    '\n - **',
  )} \nक्या ये जानकारी सही है?\n\n*%$✅ हाँ, रेट बताओ\n*%$🔁 नहीं, जानकारी बदलनी है`;

  return msg;
};

export function formatChatDate(isoString) {
  const date = new Date(isoString);

  // Options for time and date formatting
  const options = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  };

  return date.toLocaleString('en-IN', options); // Use 'hi-IN' if you want Hindi
}

export function formatChatTime(isoString) {
  const date = new Date(isoString);

  // Options for time and date formatting
  const options = {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  };

  return date.toLocaleString('en-IN', options); // Use 'hi-IN' if you want Hindi
}

const sendMessage = async ({
  content,
  senderId,
  receiverId,
  chatSessionId,
  contentType = 'TEXT',
}) => {
  try {
    const response = await axios.post('/api/chat/chatbot/message', {
      receiverId,
      senderId,
      contentType,
      content,
      chatSessionId,
    });
    return response.data;
  } catch (error) {
    console.error('Error sending message:', error.message);
    return { status: false };
  }
};
const sendBotMessage = async ({
  messages,
  senderId,
  receiverId,
  chatSessionId,
}) => {
  try {
    const response = await axios.post('/api/chat/chatbot/bot-reply', {
      receiverId,
      senderId,
      messages,
      chatSessionId,
    });
    return response.data;
  } catch (error) {
    console.error('Error sending message:', error);
    return { status: false };
  }
};

const restartSession = async ({ recipientId, initiatorId, type }) => {
  try {
    const response = await axios.post('/api/chat/chatbot/restart-session', {
      recipientId,
      type,
    });
    return response.data;
  } catch (error) {
    console.error('Error sending message:', error);
    return { status: false };
  }
};
const EndSession = async ({ chatSessionId }) => {
  try {
    const response = await axios.post('/api/chat/chatbot/end-session', {
      chatSessionId,
    });
    return response.data;
  } catch (error) {
    console.error('Error sending message:', error);
    return { status: false };
  }
};

const UpdateSession = async ({ chatSessionId, type }) => {
  try {
    const response = await axios.patch(
      '/api/chat/chatbot/update-session-payment',
      {
        chatSessionId,
        type,
      },
    );
    return response.data;
  } catch (error) {
    throw new Error(error);
  }
};

const getSession = async ({ chatSessionId }) => {
  try {
    const response = await axios.get(
      `/api/chat/chatbot/getSession/${chatSessionId}`,
    );
    if (response?.data?.status) {
      const chatSession = response?.data?.chatSession;
      // If this is a new session, show the first pre-defined message
      return chatSession;
    } else {
      return false;
    }
  } catch (error) {
    console.error('error in getSession', error);
    return false;
  }
};
const getPashuPartnerSession = async () => {
  try {
    const response = await axios.get(
      '/api/chat/chatbot/pashu-partners/existing-session',
    );
    if (response?.data?.status) {
      const chatSessions = response?.data?.chatSessions;
      // If this is a new session, show the first pre-defined message
      return chatSessions;
    } else {
      return false;
    }
  } catch (error) {
    console.error('error in getSession', error);
    return false;
  }
};

const formatBoldText = (text) => {
  if (!text.includes('**')) return text;

  const parts = text.split(/(\*\*.*?\*\*)/g);

  return parts.map((part, i) => {
    if (part.startsWith('**') || part.endsWith('**')) {
      const content = part.slice(2);
      return (
        <strong key={i} className="font-semibold">
          {content}
        </strong>
      );
    }
    return part;
  });
};
export {
  getInitialBotMessage,
  sendMessage,
  sendBotMessage,
  restartSession,
  EndSession,
  formatBoldText,
  getSession,
  UpdateSession,
  getPashuPartnerSession,
};
