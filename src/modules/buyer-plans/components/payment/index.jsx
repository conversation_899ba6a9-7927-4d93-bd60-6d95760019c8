import { useCallback, useContext } from 'react';

import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

import usePayment from 'hooks/monetization/usePayment';
import useSubscription from 'hooks/monetization/useSubscription';
import useCustomerSupport from 'hooks/useCustomerSupport';

import { logAmplitudeEvent } from 'lib/log-event';
import { getCommaSeparatedNumber } from 'lib/utils';

import { AnimallLoaderContext } from 'components/AnimallLoader';
import Button from 'components/ui/Button';

import { usePaymentConfirmation } from 'providers/PaymentConfirmationProvider';

const SubscriptionCTA = ({
  source,
  onAfterSuccess = () => {
    console.log('ho gya bhai success');
  },
  onAfterFailure = () => {},
  plan,
}) => {
  const router = useRouter();
  const supportPhoneNumber = useCustomerSupport();
  const { showSuccessPopup, showErrorPopup } = usePaymentConfirmation();

  const { closeAnimallLoader } = useContext(AnimallLoaderContext);

  const { t: c } = useTranslation('buyer_plans');

  const planPrice = !!plan?.extraInfo?.trialPlan ? 1 : plan?.price;
  const successCallback = useCallback(async () => {
    closeAnimallLoader();
    showSuccessPopup({
      amount: planPrice,
      onOk: () => {
        router.back();
      },
      onClose: () => {
        router.back();
      },
    });
    return;
  }, [closeAnimallLoader, planPrice, router, showSuccessPopup]);

  const failureCallback = useCallback(() => {
    closeAnimallLoader();
    showErrorPopup({
      onSupport: () => {
        logAmplitudeEvent('CLICKED', 'SUPPORT', 'PAYMENTFAILURE', {
          PLAN_TYPE: 'VIP_BUYER',
          SOURCE: source,
        });
        window.open(`https://wa.me/91${supportPhoneNumber}`, '_blank');
      },
    });
  }, [closeAnimallLoader, source, showErrorPopup, supportPhoneNumber]);

  const { startSubscription } = useSubscription({
    source: source,
    onSubscriptionSuccess: successCallback,
    onSubscriptionFailure: failureCallback,
  });

  const { startPayment } = usePayment({
    source: source,
    onPaymentSuccess: successCallback,
    onPaymentFailure: failureCallback,
  });

  const onPaymentClick = useCallback(() => {
    if (plan?.extraInfo?.isSubscription) {
      startSubscription(plan);
    } else {
      startPayment('ORDER', plan, {});
    }
  }, [plan, startPayment, startSubscription]);

  return (
    <div className="flex flex-col gap-2 items-center justify-center rounded relative transition-all duration-500 p-4 bg-surface-3 border-0">
      <div className="w-full">
        <Button
          size="xl"
          textClassName="w-full flex justify-between items-center text-surface-3"
          className="relative shine"
          onClick={onPaymentClick}
          id="GROWTH_PLAN_PAYMENT_CTA_MAIN"
        >
          <>
            <span>{c('buyPlan')}</span>
            <span className="flex items-center gap-1.5">
              ₹{getCommaSeparatedNumber(planPrice)}
            </span>
          </>
        </Button>
      </div>
    </div>
  );
};

export default SubscriptionCTA;
