import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { i18n, useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

import useSubscription from 'hooks/monetization/useSubscription';
import useMicroTransaction from 'hooks/useMicroTransaction';
import useRazorpay from 'hooks/useRazorpay';

import axios from 'lib/axios';
import { logAmplitudeEvent, logFbEvent } from 'lib/log-event';
import { getCookie } from 'lib/storage';
import { cn, delay, getCommaSeparatedNumber, isInBuckets } from 'lib/utils';

import { AnimallLoaderContext } from 'components/AnimallLoader';
import CoinDeductionAnimation from 'components/CoinDeductionAnimation';
import LowBalancePopup from 'components/LowBalancePopup';
import QrPopup from 'components/QrPopup';
import Image from 'components/UnoptimizedImage';

import { ChevronRightIcon } from '@chakra-ui/icons';
import { CloseIcon } from 'modules/assessment/icons';
import WalletInfo from 'modules/plans/components/WalletInfo';
import { QRCodeIcon } from 'modules/plans/components/icons';
import { sendEventToServer } from 'modules/plans/utils';
import { Coin } from 'modules/user/components/wallet/icons';
import { WalletContext } from 'pages/prime/landing';
import { MonetizationContext } from 'providers/Monetization';
import { usePaymentConfirmation } from 'providers/PaymentConfirmationProvider';

import PlanSelectionPopup from '../PlanSelection/components/PlanSelectionPopup';
import ExitDiscountPopup from './ExitDiscountPopup';
import PashuSelection from './PashuSelection';
import { PaymentFailurePopUp } from './PaymentStatus';
import PaymentSuccess from './PaymentSuccess';
import VideoUpload from './VideoUpload';

const CTA = ({
  post,
  setPost,
  variant = 'BOOSTER',
  scrollEnded: _scrollEnded,
  postsEligibleForPrime,
  primeBoosterPlans,
  setPrimeBoosterPlans,
  overrideSelectedPost,
  selectedPlan,
  setSelectedBoosterPlan = () => {},
  walletContext = WalletContext,
  source = 'PRIME_LANDING',
  isMicroTxnUser,
  isCwaCoinsExpUser = false,
  exitPrimeDiscount,
  showExitDiscountPopup = false,
  setShowExitDiscountPopup = () => {},
}) => {
  const { wallet } = useContext(walletContext);
  const router = useRouter();
  const [selectedPost, setSelectedPost] = useState(overrideSelectedPost);
  const [scrollEnded, setScrollEnded] = useState(_scrollEnded);
  const [openFailurePopup, setOpenFailurePopup] = useState(false);
  const [showOverlay, setShowOverlay] = useState(false);
  const [showMultipleButtons, setShowMultipleButtons] = useState(false);
  const [showVideoUpload, setShowVideoUpload] = useState(false);
  const [showPaymentSuccess, setShowPaymentSuccess] = useState(false);
  const [showQrPopup, setShowQrPopup] = useState(false);
  const [qrUrl, setQrUrl] = useState('');
  const plan =
    primeBoosterPlans?.find((plan) => plan.name === selectedPlan) || {};
  const { isEligibleForVIPUserPlansPage, isNewBenifits, isSubscription } =
    plan?.extraInfo || {};

  const isVipUserPlan = isEligibleForVIPUserPlansPage && isNewBenifits;
  const { openAnimallLoader, closeAnimallLoader } =
    useContext(AnimallLoaderContext);

  const [useWalletBalance, setUseWalletBalance] = useState(
    !(isMicroTxnUser || isCwaCoinsExpUser) &&
      wallet.showWallet &&
      wallet?.balance > 0 &&
      !isSubscription,
  );
  const [showWalletInfo, setShowWalletInfo] = useState(
    !(isMicroTxnUser || isCwaCoinsExpUser) &&
      wallet.showWallet &&
      !isSubscription,
  );
  const [isLowBalance, setIsLowBalance] = useState(false);

  const [showPashuSelection, setShowPashuSelection] = useState(false);

  const [showPlanSelection, setShowPlanSelection] = useState(false);
  const [exitPrimePlan, setExitPrimePlan] = useState(null);
  const { t } = useTranslation('common');

  const postId = post?._id;
  const planDetails = useMemo(
    () =>
      primeBoosterPlans?.find((p) => {
        if (p.discount && p.discount.name === selectedPlan) {
          return p.discount.name === selectedPlan;
        }
        return p.name === selectedPlan;
      }),
    [selectedPlan, primeBoosterPlans],
  );

  const price = planDetails?.discount?.price || planDetails?.price;
  const originalPrice = planDetails?.discount ? planDetails?.price : null;

  const stats = useContext(MonetizationContext);
  const monetizationPlan = stats?.listing?.currentPlan;
  const primeMultiPlans = ['PRIME_2500'];
  const { showSuccessPopup, showErrorPopup } = usePaymentConfirmation();

  const redirectToHomePage = useCallback(
    (sourceParam = 'AddProductPayment') => {
      logAmplitudeEvent('CLOSED', 'PRIME_SELECTION', 'MONETIZATION', {
        PLAN_TYPE: 'VIP_USER',
        SOURCE: source,
      });
      router.replace(`/home?source=${sourceParam}`);
    },
    [router, source],
  );

  const { startSubscription } = useSubscription({
    source,
    onSubscriptionSuccess: () =>
      showSuccessPopup({
        amount: selectedPlan?.price,
        onClose: redirectToHomePage,
        onOk: redirectToHomePage,
      }),
    onSubscriptionFailure: showErrorPopup,
  });

  useEffect(() => {
    // This is needed because the context is updated in client side
    // which doesn't set the state again
    setShowWalletInfo(
      !(isMicroTxnUser || isCwaCoinsExpUser) &&
        wallet.showWallet &&
        !isSubscription,
    );
    setUseWalletBalance(
      !(isMicroTxnUser || isCwaCoinsExpUser) &&
        wallet.showWallet &&
        wallet?.balance > 0 &&
        !isSubscription,
    );
  }, [wallet, isMicroTxnUser, isCwaCoinsExpUser, isSubscription]);

  const walletChangeHandler = (e) => {
    if (e?.target?.checked) {
      setUseWalletBalance(true);
    } else {
      setUseWalletBalance(false);
    }
  };

  const closePashuSelection = () => {
    setSelectedPost(null);
    setShowPashuSelection(false);
  };

  const selectPashu = () => {
    setShowPashuSelection(false);
    setSelectedBoosterPlan(() => {
      const discountedPlan = primeBoosterPlans.find((p) => !!p?.discount);
      if (!discountedPlan) return primeBoosterPlans[1]?.name;
      return discountedPlan?.discount.name;
    });
    setScrollEnded(true);
    setShowPlanSelection(true);
    setShowWalletInfo(
      !(isMicroTxnUser || isCwaCoinsExpUser) &&
        wallet.showWallet &&
        !isSubscription,
    );
  };

  const closePlanSelection = () => {
    if (exitPrimeDiscount) {
      setShowExitDiscountPopup(true);
      return;
    }
    setScrollEnded(_scrollEnded);
    setSelectedPost(null);
    setSelectedBoosterPlan(null);
    setShowPlanSelection(false);
  };

  useEffect(() => {
    if (!showPashuSelection) return;
    if (!postsEligibleForPrime?.length) return;
    setSelectedPost(postsEligibleForPrime[0]?._id);
  }, [postsEligibleForPrime, showPashuSelection]);

  const skipPayment = primeMultiPlans.some((plan) =>
    monetizationPlan?.includes(plan),
  );

  const onShare = () => {
    const shareText = `Animall पर अपना पशु को प्राइम बनाने के लिए किसी भी UPI ऐप से इस QR कोड पे पेमेंट करें\n\nQR कोड: ${qrUrl}`;
    const url = `https://api.whatsapp.com/send?text=${encodeURIComponent(
      shareText,
    )}`;
    window.open(url, '_blank', 'noopener noreferrer');
  };

  useEffect(() => {
    if (!selectedPost || selectedPost === 'NEW_POST') {
      return;
    }
    const postObj = postsEligibleForPrime?.find((p) => p._id === selectedPost);
    if (!postObj) return;
    setPost(postObj);
  }, [selectedPost, postsEligibleForPrime, setPost]);

  const closeQrPopup = (source) => {
    if (source === 'CLOSE') {
      resetPageState();
    } else if (source === 'BACK') {
      setShowMultipleButtons(true);
      setShowWalletInfo(false);
      setShowOverlay(true);
    }
    setQrUrl('');
    setShowQrPopup(false);
  };

  const { initPayment } = useRazorpay();
  const {
    deductFromWallet,
    hasEnoughBalance,
    animationDuration,
    slideInDuration,
    fadeOutDuration,
    animationState,
    deductionCoins,
  } = useMicroTransaction();

  const resetPageState = () => {
    setShowMultipleButtons(false);
    setUseWalletBalance(
      !(isMicroTxnUser || isCwaCoinsExpUser) &&
        wallet.showWallet &&
        wallet?.balance > 0 &&
        !isSubscription,
    );
    setShowWalletInfo(
      !(isMicroTxnUser || isCwaCoinsExpUser) &&
        wallet.showWallet &&
        !isSubscription,
    );
    setShowOverlay(false);
    if (!overrideSelectedPost) {
      setSelectedPost(null);
      setSelectedBoosterPlan(null);
      setShowPlanSelection(false);
    }
  };

  const paymentModeRef = useRef(null);
  const videoUrlRef = useRef(null);

  const startMicroTransaction = async () => {
    const { price } = planDetails;
    if (!hasEnoughBalance(price)) {
      setIsLowBalance(true);
      return;
    }

    openAnimallLoader();
    try {
      const { data } = await axios.post('/api/monetization/prime/post', {
        postId: post._id,
        isMicroTransaction: true,
        planName: selectedPlan,
      });
      closeAnimallLoader();
      deductFromWallet(data.walletDeductedBalance);
      await delay(animationDuration); // Wait for animation to finish
      logAmplitudeEvent('SUCCESS', 'PAYMENT', 'PRIME', {
        PLAN: price,
        SOURCE: 'LANDING',
        POST_ID: post._id,
        VARIANT: variant,
        WALLET: 'NO',
        MICRO_TRANSACTION: 'YES',
      });
      sendEventToServer('SUCCESS_PRIME_PAYMENT', undefined, {
        isMicroTransaction: true,
      });
      setShowPaymentSuccess(true);
    } catch (err) {
      console.log('[prime-payment] Micro transaction failed', err);
      closeAnimallLoader();
      logAmplitudeEvent('ERROR', 'PAYMENT', 'PRIME', {
        STEP: 'ORDER',
        PLAN: price,
        POST_ID: post._id,
        SOURCE: 'LANDING',
        VARIANT: variant,
        MICRO_TRANSACTION: 'YES',
      });
      if (err.response?.data?.error === 'Insufficient balance') {
        setIsLowBalance(true);
      }
    }
  };

  const handlePayment = (source) => {
    if (!postsEligibleForPrime?.length && !overrideSelectedPost) {
      openAnimallLoader();
      window.location.href = '/sell?source=PRIME_LANDING_BOOSTER';
      return;
    }
    if (!selectedPost) {
      setShowPashuSelection(true);
      return;
    }
    if (!selectedPlan) {
      // Pashu selected, proceed further with plan selection
      setShowPashuSelection(false);
      setSelectedBoosterPlan(primeBoosterPlans[0].name);
      setScrollEnded(true);
      setShowPlanSelection(true);
      return;
    }
    // Pashu and plan selected, proceed with payment
    logAmplitudeEvent('CLICKED', 'PLAN', 'PRIMEBOOSTER', {
      PLAN: selectedPlan,
    });
    if (isSubscription) {
      startSubscription(plan, { postId });
      return;
    }
    setShowPlanSelection(false);
    if (isMicroTxnUser) {
      paymentModeRef.current = 'MICRO_TXN';
      if (!hasEnoughBalance(price)) {
        setIsLowBalance(true);
        return;
      }
      return payForPost('MICRO_TXN');
    }
    switch (source) {
      case 'CLOSE_BTN':
      case 'CLOSE_OVERLAY':
        resetPageState();
        break;
      case 'MAIN_BTN':
        setShowOverlay(true);
        setShowMultipleButtons(true);
        setShowWalletInfo(false);
        break;
      case 'QR_BTN':
        paymentModeRef.current = 'QR';
        payForPost('QR');
        break;
      case 'UPI_BTN':
        paymentModeRef.current = 'ORDER';
        payForPost('ORDER');
        break;
    }
  };

  const resumePaymentOnVideoUpload = (url) => {
    videoUrlRef.current = url;
    setShowVideoUpload(false);
    payForPost(paymentModeRef.current, true);
  };

  const payForPost = async (paymentMode, resumeAutomation = false) => {
    const { price } = planDetails;
    const videoInPost = post?.resources?.find((r) => r?.isVideo)?.url;
    if (videoInPost) {
      videoUrlRef.current = videoInPost;
    }
    if (skipPayment) {
      setShowPaymentSuccess(true);
      return;
    }
    if (paymentMode == 'MICRO_TXN') {
      return startMicroTransaction();
    }

    const successCallback = async (paymentResultData) => {
      openAnimallLoader();

      if (paymentResultData?.status === 'paid') {
        logAmplitudeEvent('SUCCESS', 'PAYMENT', 'PRIME', {
          PLAN: price,
          SOURCE: 'LANDING',
          POST_ID: postId,
          VARIANT: variant,
          PAYMENT_MODE: paymentMode,
          WALLET: paymentResultData?.id?.startsWith('wb_') ? 'YES' : 'NO',
          MICRO_TRANSACTION: 'NO',
        });
        logFbEvent('PAYMENT_SUCCESS_CLASSIFIEDS');
        sendEventToServer('SUCCESS_PRIME_PAYMENT', 'LANDING');
        resetPageState();
        closeAnimallLoader();
        setShowPaymentSuccess(true);
        return;
      }

      // Redundant payment capture, just in case the webhook fails
      // Endpoint is idempotent
      try {
        await axios.post('/api/payment/result', paymentResultData);

        logAmplitudeEvent('SUCCESS', 'PAYMENT', 'PRIME', {
          PLAN: price,
          SOURCE: 'LANDING',
          POST_ID: postId,
          VARIANT: variant,
          WALLET: 'NO',
          MICRO_TRANSACTION: 'NO',
        });
        sendEventToServer('SUCCESS_PRIME_PAYMENT', 'LANDING');
        resetPageState();
        setShowPaymentSuccess(true);
      } catch {
        logAmplitudeEvent('ERROR', 'PAYMENT', 'PRIME', {
          STEP: 'CAPTURE',
          PLAN: price,
          SOURCE: 'LANDING',
          POST_ID: postId,
          VARIANT: variant,
        });
        console.error('[prime-payment] Payment failed during manual capture');
        setOpenFailurePopup(true);
      } finally {
        closeAnimallLoader();
      }
    };

    const failureCallback = () => {
      logAmplitudeEvent('ERROR', 'PAYMENT', 'PRIME', {
        STEP: 'PAYMENT',
        PLAN: price,
        SOURCE: 'LANDING',
        POST_ID: postId,
        VARIANT: variant,
      });
      console.error('[prime-payment] Payment failed during capture callback');
      setOpenFailurePopup(true);
    };

    try {
      openAnimallLoader();
      const originalPlanName =
        planDetails.discount?.originalPlanName || planDetails.name;
      const discountPlanName = planDetails.discount?.name;

      const { data } = await axios.post(
        '/api/monetization/subscription/init-payment',
        {
          planName: originalPlanName,
          discountPlanName,
          type: 'PRIME',
          postId,
          extraInfo: { source },
          paymentMode,
          ...(useWalletBalance && {
            useWalletBalance: useWalletBalance,
            walletBalance: wallet.balance,
          }),
        },
      );

      const order = data?.data;
      logAmplitudeEvent('STARTED', 'PAYMENT', 'PRIME', {
        PLAN: price,
        SOURCE: 'LANDING',
        POST_ID: postId,
        VARIANT: variant,
        PAYMENT_MODE: paymentMode,
      });

      if (order?.status === 'paid') {
        successCallback(order);
        return;
      }

      if (paymentMode === 'QR') {
        setQrUrl(order.image_url);
        setShowQrPopup(true);
      } else {
        initPayment({
          orderId: order.id,
          amount: order.amount,
          phone: getCookie('userPhone'),
          description: 'PRIME_PAYMENT',
          successCallback,
          failureCallback,
        });
      }
    } catch {
      logAmplitudeEvent('ERROR', 'PAYMENT', 'PRIME', {
        STEP: 'ORDER',
        PLAN: price,
        SOURCE: 'LANDING',
        POST_ID: postId,
        VARIANT: variant,
        PAYMENT_MODE: paymentMode,
      });
      console.error('[prime-payment] Payment failed during manual capture');
      setOpenFailurePopup(true);
    } finally {
      closeAnimallLoader();
    }
  };

  const maxUsableWalletBalance = useMemo(() => {
    if (!useWalletBalance) return 0;

    return Math.floor(Math.max(Math.min(wallet?.balance, price * 0.1), 0));
  }, [useWalletBalance, wallet?.balance, price]);

  const remainingAmount = useMemo(() => {
    return Math.max(price - maxUsableWalletBalance, 0);
  }, [price, maxUsableWalletBalance]);

  const handlePaymentSuccess = () => {
    setShowPaymentSuccess(false);
    openAnimallLoader();
    router.replace('/home?source=PRIME_LANDING_PAYMENT_SUCCESS');
  };

  useEffect(() => {
    if (!showExitDiscountPopup) return;
    if (!exitPrimeDiscount) return;

    setPrimeBoosterPlans((prevPlans) => {
      const clonedPlans = [...prevPlans];
      const index = clonedPlans.findIndex(
        (p) => p.id === exitPrimeDiscount.originalPlanId,
      );
      if (index === -1) return clonedPlans;
      clonedPlans[index] = {
        ...clonedPlans[index],
        discount: exitPrimeDiscount,
      };
      setExitPrimePlan(clonedPlans[index]);
      return clonedPlans;
    });
  }, [showExitDiscountPopup, exitPrimeDiscount, setPrimeBoosterPlans]);

  const closeExitDiscountPopup = () => {
    setShowExitDiscountPopup(false);
  };

  const handleExitDiscountCta = () => {
    setShowExitDiscountPopup(false);
    handlePayment('MAIN_BTN');
  };

  return (
    <>
      <ExitDiscountPopup
        plan={exitPrimePlan}
        post={post}
        isOpen={showExitDiscountPopup}
        closePopup={closeExitDiscountPopup}
        onOk={handleExitDiscountCta}
      />
      {isLowBalance && (
        <LowBalancePopup
          variant="PRIME"
          source="PRIME_LANDING"
          isOpen={isLowBalance}
          coinsRequired={price}
          onClose={() => {
            setIsLowBalance(false);
            resetPageState();
          }}
        />
      )}
      <CoinDeductionAnimation
        slideInDuration={slideInDuration}
        fadeOutDuration={fadeOutDuration}
        animationState={animationState}
        deductionCoins={deductionCoins}
      />
      {showOverlay && (
        <div
          className="min-h-screen fixed top-0 left-0 right-0 bottom-0 w-full bg-black/50 z-20"
          onClick={() => handlePayment('CLOSE_OVERLAY')}
        ></div>
      )}

      {showWalletInfo && !isSubscription && (
        <div
          className={`fixed bottom-20 w-full z-50 shadow-[0px_-4px_5px_0px_#06386F4D] ${
            scrollEnded ? 'bg-white' : 'bg-white/70 backdrop-blur-sm'
          }`}
        >
          <WalletInfo
            wallet={wallet}
            walletChangeHandler={walletChangeHandler}
            maxUsableWalletBalance={maxUsableWalletBalance}
            borderless
          />
        </div>
      )}

      {showVideoUpload && (
        <VideoUpload
          postId={postId}
          price={remainingAmount}
          onVideoUpload={resumePaymentOnVideoUpload}
          onClose={() => setShowVideoUpload(false)}
          skipPayment={skipPayment}
          source="PRIME_LANDING"
          showBackdrop
          isMicroTxnUser={isMicroTxnUser}
        />
      )}

      {showPaymentSuccess && (
        <PaymentSuccess
          post={post}
          videoUrl={videoUrlRef.current}
          price={remainingAmount}
          onClose={handlePaymentSuccess}
          skipPayment={skipPayment}
          source="PRIME_LANDING"
          isMicroTxnUser={isMicroTxnUser}
          selectedPlan={selectedPlan}
        />
      )}

      {showPashuSelection && (
        <PashuSelection
          posts={postsEligibleForPrime}
          onClose={closePashuSelection}
          selectedPost={selectedPost}
          setSelectedPost={setSelectedPost}
          onSelect={selectPashu}
        />
      )}

      {showPlanSelection && (
        <PlanSelectionPopup
          onClose={closePlanSelection}
          selectedPlan={selectedPlan}
          setSelectedPlan={setSelectedBoosterPlan}
          showWalletInfo={showWalletInfo}
          walletBalance={wallet?.balance}
          isMicroTxnUser={isMicroTxnUser}
          plans={primeBoosterPlans}
        />
      )}

      <div
        className={`fixed bottom-0 w-full z-30 transition-all ease-linear p-4 ${
          scrollEnded ? 'bg-white' : 'bg-white/70 backdrop-blur-sm'
        } ${showMultipleButtons ? 'h-36 bg-white/100 p-4' : 'h-20 pt-2'}`}
      >
        {showMultipleButtons ? (
          <div className="flex flex-col gap-2">
            <div
              className="absolute -top-10 right-4"
              onClick={() => handlePayment('CLOSE_BTN')}
            >
              <CloseIcon />
            </div>
            <button
              onClick={() => handlePayment('UPI_BTN')}
              className="w-full h-14 bg-primary-600 text-white font-bold text-xl rounded-lg flex justify-between items-center gap-2 px-4"
            >
              <p>{`₹${remainingAmount} ${t('payByUPI')}`}</p>
              <Image
                src="https://static-assets.animall.in/static/images/upi-icons.png"
                alt="upi payment"
                width={85}
                height={32}
              />
            </button>
            <button
              onClick={() => handlePayment('QR_BTN')}
              className="w-full box-border h-14 border-2 border-primary-600 text-primary-600 font-bold text-xl rounded-lg flex justify-between items-center gap-2 px-4"
            >
              <p>{`₹${remainingAmount} ${t('payThroughQR')}`}</p>
              <QRCodeIcon fill="#14776F" />
            </button>
          </div>
        ) : (
          <button
            onClick={() => handlePayment('MAIN_BTN')}
            className={cn(
              'w-full h-14 bg-primary-600 text-white font-bold text-xl rounded-lg flex justify-center items-center gap-2',
              isVipUserPlan &&
                'bg-[linear-gradient(102.08deg,_#E2B65C_24.15%,_#F8D851_61.31%,_#E2B65C_92.1%)] text-black',
            )}
          >
            {selectedPlan ? (
              <>
                {originalPrice && (
                  <span
                    className={cn(
                      'text-white font-bold text-xl opacity-60 line-through',
                      isVipUserPlan && 'text-black',
                    )}
                  >
                    {!isMicroTxnUser && '₹'}
                    {getCommaSeparatedNumber(originalPrice)}
                  </span>
                )}
                {price ? (
                  <>
                    {!isMicroTxnUser && '₹'}
                    {getCommaSeparatedNumber(remainingAmount)}
                  </>
                ) : (
                  'फ्री'
                )}
                {isMicroTxnUser && (
                  <span className="mb-1">
                    <Coin />
                  </span>
                )}
                <span className={price ? 'ml-1' : ''}>{t('buyPlan')}</span>
              </>
            ) : (
              i18n.t('prime_plan_selection:makePrimePashu')
            )}
            <ChevronRightIcon color={isVipUserPlan ? 'black' : 'white'} />
          </button>
        )}
      </div>
      <PaymentFailurePopUp
        isOpen={openFailurePopup}
        onClose={() => {
          setOpenFailurePopup(false);
          resetPageState();
        }}
        onOk={() => {
          setOpenFailurePopup(false);
          resetPageState();
        }}
        amount={remainingAmount}
      />
      {showQrPopup && qrUrl && (
        <QrPopup
          qrUrl={qrUrl}
          amount={remainingAmount}
          closeQrPopup={closeQrPopup}
          onShare={onShare}
        />
      )}
    </>
  );
};

export default CTA;
