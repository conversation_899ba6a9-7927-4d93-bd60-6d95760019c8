import { useState } from 'react';

import { useTranslation } from 'next-i18next';

import { cn, getCommaSeparatedNumber } from 'lib/utils';

import CancelAutoPayInfoPopup from 'components/AutoPay/CancelAutoPayInfoPopup';
import { H2, H3, TertiaryBody } from 'components/ui/typography';

import { Divider } from '@chakra-ui/react';
import { GoldenCalenderIcon } from 'modules/buy/Icons';
import { TickIcon } from 'modules/classifieds-plans/icons/plans';
import { getBenefitTexts } from 'modules/classifieds-plans/utils';
import AddCartIcon from 'modules/prime/icons/AddCart';
import AnimallIcon from 'modules/prime/icons/AnimallIcon';
import CattleFaceIcon from 'modules/prime/icons/CattleFaceIcon';
import DownArrow from 'modules/prime/icons/DownArrow';
import RupeeIcon from 'modules/prime/icons/RupeeIcon';
import SmallClockIcon from 'modules/prime/icons/SmallClockIcon';

const getHighlightedText = (plan, t) => {
  const {
    benefits,
    isEligibleForVIPUserPlansPage,
    isNewBenifits,
    refundableAmount,
  } = plan?.extraInfo || {};
  const isVipUserPlan = isEligibleForVIPUserPlansPage && isNewBenifits;
  if (!benefits) return null;
  if (benefits?.guranteedSaleDays) {
    return (
      <H2
        className={cn(
          'text-primary-500 bg-primary-25 px-1 italic !leading-8',
          plan?.extraInfo?.uiInfo?.theme === 'gold' && 'text-[#DC9400]',
        )}
        style={
          plan?.extraInfo?.uiInfo?.theme === 'gold'
            ? {
                background:
                  'linear-gradient(90deg, #FFDF9C 0%, #FFFFFF 97.08%)',
              }
            : {}
        }
      >
        {/* पैसा वापस  */}
        {refundableAmount
          ? t('amountBack', { amount: refundableAmount })
          : t('moneyBack')}
      </H2>
    );
  } else if (
    plan.name === 'PRIME_BOOSTER_350' ||
    plan.name === 'PRIME_BOOSTER_350_V2' ||
    plan.name === 'PRIME_BOOSTER_199' ||
    plan.name === 'APP_SUBSCRIPTION_399_VIP_USER'
  ) {
    return (
      <H2
        className={cn(
          'text-primary-500 bg-primary-25 px-1 italic !leading-8',
          isVipUserPlan &&
            'bg-gradient-to-r from-[#FFDF9C] to-white text-[#DC9400]',
        )}
      >
        {/* जल्दी बेचे  */}
        {t('sellFast')}
      </H2>
    );
  } else if (plan.name === 'PRIME_BOOSTER_99') {
    return (
      <H2 className="text-text-primary">
        {/* ऊपर करें  */}
        {t('putOnTop')}
      </H2>
    );
  } else {
    return (
      <H2 className="text-primary-500 bg-primary-25 px-1 italic !leading-8">
        {/* गारंटी से बेचे */}
        {t('sellWithGuarantee')}
      </H2>
    );
  }
};

const PlanCard = ({
  plan,
  isSelected,
  onSelect,
  setShowRefundPopup,
  showDivider,
}) => {
  const { extraInfo, uiInfo } = plan;
  const { isEligibleForVIPUserPlansPage, isNewBenifits, isSubscription } =
    extraInfo;
  const { t } = useTranslation('prime_plan_selection');
  const [showAutoPayInfoPopup, setShowAutoPayInfoPopup] = useState(false);
  const isVipUserPlan = isEligibleForVIPUserPlansPage && isNewBenifits;
  const benefitTexts = getBenefitTexts(plan, 'prime');
  const effectivePrice = plan?.discount ? plan.discount.price : plan?.price;
  const priceText = plan?.price
    ? `₹${getCommaSeparatedNumber(effectivePrice)}`
    : t('free');

  const highlightedText = getHighlightedText(plan, t);
  const benefitIcons = [
    <AddCartIcon key={1} />,
    <CattleFaceIcon key={2} />,
    <AnimallIcon key={3} />,
    <RupeeIcon key={4} />,
  ];
  const refundablePlanBenefitIcons = [
    <AddCartIcon key={1} />,
    <SmallClockIcon key={2} />,
  ];
  const is2000RefundablePlan = plan.name === 'PRIME_BOOSTER_2000';
  return (
    <>
      {showDivider && !isVipUserPlan && (
        <div className="relative w-full">
          <Divider
            className="my-4 m-auto w-full relative"
            orientation="horizontal"
            color="#DEE0E4"
          ></Divider>
          <H3 className="absolute px-2 left-1/2 top-1 bg-white -translate-x-1/2 text-surface-1">
            {t('or')}
          </H3>
        </div>
      )}
      <label
        className={cn(
          'relative rounded-lg bg-surface-3 border-[3px] border-surface-1 p-3 w-full',
          'flex flex-col gap-2.5',
          isSelected && 'border-[3px] border-primary-600',
          isSelected &&
            (isVipUserPlan || is2000RefundablePlan) &&
            ' border-[#E2B65C]',
        )}
      >
        <span className="flex items-center justify-between">
          <span className="flex flex-col items-start gap-px">
            <span className="flex items-center gap-2">
              <H3>
                {[
                  'PRIME_BOOSTER_1000',
                  'PRIME_BOOSTER_1000_V2',
                  'PRIME_BOOSTER_2000',
                ].includes(plan.name)
                  ? t('notSold')
                  : 'APP_SUBSCRIPTION_399_VIP_USER' === plan?.name
                  ? t('allCattle')
                  : t('cattle')}
              </H3>
              {highlightedText}
            </span>
          </span>
          <span
            className={cn(
              'w-5 h-5 rounded-full border-2 border-grey-200',
              'flex justify-center items-center',
              isSelected && 'border-primary-600',
              (isVipUserPlan || is2000RefundablePlan) &&
                isSelected &&
                'border-[#DC9400]',
            )}
          >
            {isSelected && (
              <span
                className={cn(
                  'w-3 h-3 rounded-full bg-primary-600',
                  (isVipUserPlan || is2000RefundablePlan) && 'bg-[#DC9400]',
                )}
              ></span>
            )}
          </span>
          <input
            className="sr-only"
            type="radio"
            name="plan"
            checked={isSelected}
            onChange={onSelect}
          />
        </span>
        <span className="flex items-end justify-between">
          <div>
            {benefitTexts.map((text, index) => (
              <span className="flex items-center gap-1" key={index}>
                {isVipUserPlan ? (
                  <span className=" bg-[linear-gradient(102.08deg,_#E2B65C_24.15%,_#F8D851_61.31%,_#E2B65C_92.1%)] w-4 h-4 rounded-full flex justify-center items-center">
                    {benefitIcons[index]}
                  </span>
                ) : is2000RefundablePlan ? (
                  <span className=" bg-[linear-gradient(102.08deg,_#E2B65C_24.15%,_#F8D851_61.31%,_#E2B65C_92.1%)] w-4 h-4 rounded-full flex justify-center items-center">
                    {refundablePlanBenefitIcons[index]}
                  </span>
                ) : (
                  <TickIcon />
                )}
                <TertiaryBody className="text-text-secondary">
                  {text}
                </TertiaryBody>
              </span>
            ))}
          </div>

          <H3 className="text-primary-700 flex flex-col items-end">
            {priceText}
            {isSubscription && (
              <p className="text-text-secondary font-bold text-xs">हर महीना</p>
            )}
          </H3>
        </span>
        {[
          'PRIME_BOOSTER_1000',
          'PRIME_BOOSTER_1000_V2',
          'PRIME_BOOSTER_2000',
        ].includes(plan.name) && (
          <div
            className="flex w-fit items-center gap-1 mt-1.5"
            onClick={() => setShowRefundPopup(true)}
          >
            <TertiaryBody className="font-bold text-text-primary text-sm">
              {/* और जानकारी */}
              {t('moreInfo')}
            </TertiaryBody>
            <DownArrow />
          </div>
        )}
        {isSubscription && (
          <div className="flex justify-start items-center gap-1 px-2 bg-gradient-prime-surface py-0.5 rounded-md">
            <GoldenCalenderIcon />
            <TertiaryBody>
              हर महीने ऑटोपेमेंट। कभी भी{' '}
              <span
                className="font-bold text-primary-600"
                onClick={() => setShowAutoPayInfoPopup(true)}
              >
                कैंसल
              </span>{' '}
              करें
            </TertiaryBody>{' '}
          </div>
        )}
      </label>

      <CancelAutoPayInfoPopup
        show={showAutoPayInfoPopup}
        setShow={setShowAutoPayInfoPopup}
      />
    </>
  );
};

export default PlanCard;
export { getHighlightedText };
