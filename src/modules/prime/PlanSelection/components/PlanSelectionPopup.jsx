// Same as the entire page, in a popup form
// to be shown on the landing page
import { useEffect } from 'react';

import useScrollBlock from 'hooks/useScrollBlock';

import Image from 'components/UnoptimizedImage';

import PlanSelector from './PlanSelector';

const PlanSelectionPopup = ({
  onClose,
  selectedPlan,
  setSelectedPlan,
  showWalletInfo,
  walletBalance,
  isMicroTxnUser,
  plans,
}) => {
  const [blockScroll, allowScroll] = useScrollBlock();
  useEffect(() => {
    blockScroll();
    return () => allowScroll();
  }, [allowScroll, blockScroll]);

  const isWalletShown = showWalletInfo && walletBalance > 0;
  const height = isWalletShown ? 'calc(100vh - 150px)' : 'calc(100vh - 80px)';
  const bottom = isWalletShown ? '150px' : '80px';

  return (
    <>
      <div
        style={{ height }}
        onClick={onClose}
        className="fixed top-0 left-0 w-screen bg-black/70 z-[1000] animate-fade-in"
      ></div>
      <div
        style={{ bottom }}
        className="flex flex-col gap-4 fixed left-0 w-screen z-[1001] animate-slide-up"
      >
        <div onClick={onClose} className="ml-auto mr-4">
          <Image
            src="https://static-assets.animall.in/static/images/close-white.svg"
            alt="close"
            width={32}
            height={32}
            loading="lazy"
          />
        </div>
        <div className="w-full rounded-t-xl pt-3 bg-white max-h-[70vh] overflow-y-auto">
          <h1 className="px-4 font-bold text-xl tracking-tight">प्लान चुनें</h1>
          <PlanSelector
            plans={plans}
            selectedPlan={selectedPlan}
            setSelectedPlan={setSelectedPlan}
            isMicroTxnUser={isMicroTxnUser}
          />
        </div>
      </div>
    </>
  );
};

export default PlanSelectionPopup;
