import React, { useState } from 'react';

import { logAmplitudeEvent } from 'lib/log-event';
import { getCommaSeparatedNumber } from 'lib/utils';

import CustomerSupportCard from 'components/CustomerSupportCard';

import Support from 'modules/classifieds-plans/components/Support';
import { DiamondIcon, GuaranteeIcon } from 'modules/prime/icons/PlanSelection';
import { Coin } from 'modules/user/components/wallet/icons';
import { useGrowthUser } from 'providers/GrowthUserProvider';

import PlanCard from './PlanCard';
import RefundPopup from './RefundPopup';

export const PlanButton = ({
  plan,
  isMicroTxnUser,
  handleChange,
  selectedPlan,
  standalone = false,
  checkedBg = '#D9FFFC',
}) => {
  const price = plan.discount?.price || plan.price;
  const originalPrice = plan.discount ? plan.price : null;
  const planName = plan.discount?.name || plan.name;

  const priceText = `${isMicroTxnUser ? '' : '₹'}${getCommaSeparatedNumber(
    price,
  )}`;
  const originalPriceText = originalPrice
    ? `${isMicroTxnUser ? '' : '₹'}${getCommaSeparatedNumber(originalPrice)}`
    : '';
  const isDiscountedPlan = !!originalPrice || price === 0;

  const styles = {
    freePlan: {
      border: '2px solid transparent',
      background: `linear-gradient(${checkedBg} 0%, ${checkedBg} 100%) padding-box, linear-gradient(96.48deg, #F8D851 3.88%, #E7B012 21.19%, #E2B65C 57.33%, #E6AA3B 97.72%) border-box`,
    },
    paidPlan: {
      border: '2px solid #069696',
      background: checkedBg,
    },
  };

  const isChecked = standalone || selectedPlan === planName;
  return (
    <div
      className={`relative w-full ${isDiscountedPlan ? 'mt-3 h-16' : 'h-14'}`}
    >
      {isDiscountedPlan && (
        <div
          style={{
            border: '1px solid transparent',
            background:
              'linear-gradient(101.5deg, #E2B65C 25.2%, #F8D851 60.85%, #E2B65C 90.39%) padding-box, linear-gradient(96.48deg, #F8D851 3.88%, #E7B012 21.19%, #E2B65C 57.33%, #E6AA3B 97.72%) border-box',
          }}
          className="absolute z-[1] left-1/2 -translate-x-1/2 -translate-y-1/2 w-[84px] h-6 flex justify-center items-center text-xs font-bold text-text-primary leading-5 rounded-full"
        >
          सिर्फ़ आपके लिए
        </div>
      )}
      <input
        type="radio"
        name="primePlan"
        id={planName}
        value={planName}
        onChange={handleChange}
        checked={isChecked}
        className={`appearance-none z-10 absolute right-3 top-1/2 -translate-y-1/2 w-6 h-6 rounded-full border-2 border-[#069696] peer checked:border-[#069696] checked:before:bg-[#069696] checked:before:content-[''] checked:before:block checked:before:rounded-full checked:before:w-4 checked:before:h-4 checked:before:mx-auto checked:before:my-0.5 ${
          standalone ? 'hidden' : ''
        }`}
        readOnly={standalone}
      />
      <label
        style={
          isChecked
            ? isDiscountedPlan
              ? styles.freePlan
              : styles.paidPlan
            : {}
        }
        htmlFor={planName}
        className={`relative flex w-full h-full pl-[84px] pr-14 rounded-lg border-2 border-trueGray-100 cursor-pointer ${
          isDiscountedPlan ? 'items-end pb-1' : 'items-center'
        }`}
      >
        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-xl font-bold font-rajdhani flex items-center gap-1">
          <span className="flex flex-col justify-center items-center">
            {originalPrice ? (
              <span className="text-base font-semibold text-text-primary line-through opacity-40 font-rajdhani">
                {originalPriceText}
              </span>
            ) : null}

            {price ? (
              <span className="font-rajdhani">{priceText}</span>
            ) : (
              <span
                className={`text-2xl font-bold leading-7 ${
                  originalPrice ? '-mt-1' : ''
                }`}
                style={{
                  background:
                    'linear-gradient(101.5deg, #E2B65C 25.2%, #F8D851 60.85%, #E2B65C 90.39%)',
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  WebkitTextStroke: '0.5px #E2B65C',
                }}
              >
                फ्री
              </span>
            )}
          </span>
          {isMicroTxnUser && (
            <span className="mb-1">
              <Coin w={16} />
            </span>
          )}
        </span>
        <p className="flex flex-col">
          <span className="text-lg font-bold tracking-tight leading-[23px] font-rajdhani">
            {plan.extraInfo?.animallGuarantee
              ? 'पशु बेचने की Animall की गारंटी'
              : `${plan.extraInfo?.boostHours} घंटे के लिए सबसे ऊपर`}
          </span>
          <span
            className={`flex items-center gap-1.5 text-sm tracking-tight font-rajdhani font-bold text-[#576665] px-1 py-0.5 w-max rounded ${
              plan.extraInfo?.animallGuarantee ? 'bg-[#FFF0B2]' : '-mt-1'
            }`}
          >
            {plan.extraInfo?.animallGuarantee ? (
              <>
                <GuaranteeIcon />
                नहीं बिका तो पैसे वापस
              </>
            ) : (
              <>
                <DiamondIcon />
                लगभग {plan.extraInfo?.estimatedCwa}+ कॉल
              </>
            )}
          </span>
        </p>
      </label>
    </div>
  );
};

const PlanSelector = ({
  plans = [],
  selectedPlan,
  setSelectedPlan,
  isWalletShown,
  screen,
  isMicroTxnUser,
}) => {
  const paddingClass = screen === 'PRIME_PLAN_SELECTION' ? 'pb-24' : '';
  const [showRefundPopup, setShowRefundPopup] = useState(false);
  const handleChange = (plan) => {
    const planName = plan?.discount?.name || plan?.name;
    logAmplitudeEvent('SELECTED', 'PLAN', 'PRIMEBOOSTER', {
      PLAN: planName,
    });
    setSelectedPlan(plan.name);
  };

  const { isGrowthUser } = useGrowthUser();

  return (
    <div
      className={`flex flex-col items-center justify-center w-full gap-4 p-4 ${
        isWalletShown ? 'pb-40' : paddingClass
      }`}
    >
      {plans.map((plan, index) => (
        <PlanCard
          isSelected={selectedPlan === plan.name}
          onSelect={() => handleChange(plan)}
          plan={plan}
          setShowRefundPopup={setShowRefundPopup}
          showDivider={index === plans.length - 1}
          key={plan?.name ?? index}
        />
      ))}
      <Support className="mt-12 mb-6 w-full px-0" />

      <RefundPopup
        isOpen={showRefundPopup}
        onClose={() => setShowRefundPopup(false)}
      />
    </div>
  );
};

export default PlanSelector;
