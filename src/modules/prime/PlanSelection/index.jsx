import { useContext, useEffect, useState } from 'react';

import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

import { logAmplitudeEvent } from 'lib/log-event';
import { isInBuckets } from 'lib/utils';

import { H4 } from 'components/ui/typography';

import PlanSelector from './components/PlanSelector';
import RepeatPrimePopup from './components/RepeatPrime';

import { useScrollPosition } from '@n8tb1t/use-scroll-position';
import { parseCookies } from 'nookies';
import { MonetizationContext } from 'providers/Monetization';
import { WalletContext } from 'providers/Wallet';

import CTA from '../components/CTA';
import Header from '../components/Header';
import SampleFeed from '../components/SampleFeed';

const PlanSelection = ({
  post,
  isMicroTxnUser,
  isCwaCoinsExpUser,
  exitPrimeDiscount,
}) => {
  const { wallet } = useContext(WalletContext);
  const { prime, app } = useContext(MonetizationContext);
  const { bestPerformingPrimePost, isPrimeRepeatUser, plans, vipUserPlan } =
    prime || {};
  const { bucketId } = parseCookies();
  const isInVipUserBucket = isInBuckets([40, 59], bucketId);
  const effectivePlans =
    app?.premiumUser && isInVipUserBucket ? vipUserPlan : plans;
  const [scrollStarted, setScrollStarted] = useState(false);
  const [scrollEnded, setScrollEnded] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState();
  const [showRepeatPrimePopup, setShowRepeatPrimePopup] = useState(false);
  const [showExitDiscountPopup, setShowExitDiscountPopup] = useState(false);
  const [boosterPlans, setBoosterPlans] = useState(effectivePlans || []);
  const { t } = useTranslation('prime_plan_selection');
  useEffect(() => {
    const sortedPlans =
      effectivePlans?.sort((a, b) => a.sortOrder - b.sortOrder) || [];
    const primeBooster500Plan = sortedPlans?.find((plan) =>
      ['PRIME_BOOSTER_500', 'PRIME_BOOSTER_500_V2'].includes(plan.name),
    );
    setSelectedPlan(primeBooster500Plan?.name ?? sortedPlans[0]?.name);
    setBoosterPlans(sortedPlans);
  }, [effectivePlans]);

  const router = useRouter();

  const handleClose = () => {
    if (isPrimeRepeatUser) {
      setShowRepeatPrimePopup(true);
    } else if (exitPrimeDiscount) {
      setShowExitDiscountPopup(true);
    } else {
      router.back();
    }
  };

  const { source, postId } = router.query;

  useEffect(() => {
    logAmplitudeEvent('LANDED', 'PLANSELECTION', 'PRIME', {
      SOURCE: source || 'NA',
      POST_ID: post?._id || 'NA',
      MICRO_TRANSACTION: isMicroTxnUser ? 'YES' : 'NO',
    });
  }, [source, post, isMicroTxnUser]);

  useScrollPosition(({ currPos }) => {
    const scrollTop = Math.abs(currPos.y);
    const scrollHeight = document.body.scrollHeight;
    const clientHeight = window.innerHeight;

    const isAtEnd = scrollHeight - scrollTop - clientHeight <= 1;
    const hasScrolled = scrollTop >= 10;

    if (hasScrolled) setScrollStarted(true);
    else setScrollStarted(false);

    if (isAtEnd) setScrollEnded(true);
    else setScrollEnded(false);
  });

  return (
    <>
      {showRepeatPrimePopup && (
        <RepeatPrimePopup
          post={
            bestPerformingPrimePost
              ? { ...bestPerformingPrimePost, isPrime: true }
              : null
          }
          onClose={() => router.back()}
          onOk={() => setShowRepeatPrimePopup(false)}
          currentPostId={postId}
        />
      )}
      <Header hideLogo scrollStarted={scrollStarted} onClose={handleClose} />
      <SampleFeed
        className="pt-12"
        post={post}
        version={2}
        width="46vw"
        screen="PRIME_PLAN_SELECTION"
      />
      <H4 className="font-bold font-rajdhani text-text-primary text-center pt-6 pb-2">
        {/* पशु सबसे ऊपर दिखाएं, ज़्यादा कॉल पाएँ */}
        {t('showCattleOnTopGetMoreCalls')}
      </H4>
      <PlanSelector
        plans={boosterPlans}
        selectedPlan={selectedPlan}
        setSelectedPlan={setSelectedPlan}
        screen="PRIME_PLAN_SELECTION"
        isMicroTxnUser={isMicroTxnUser}
        isWalletShown={wallet?.showWallet && wallet?.balance > 0}
      />
      <CTA
        scrollEnded={scrollEnded}
        post={post}
        variant="BOOSTER"
        overrideSelectedPost={postId}
        selectedPlan={selectedPlan}
        walletContext={WalletContext}
        primeBoosterPlans={boosterPlans}
        setPrimeBoosterPlans={setBoosterPlans}
        source="PRIME_PLAN_SELECTION"
        isMicroTxnUser={isMicroTxnUser}
        isCwaCoinsExpUser={isCwaCoinsExpUser}
        exitPrimeDiscount={exitPrimeDiscount}
        showExitDiscountPopup={showExitDiscountPopup}
        setShowExitDiscountPopup={setShowExitDiscountPopup}
      />
    </>
  );
};

export default PlanSelection;
