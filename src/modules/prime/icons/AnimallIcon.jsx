const AnimallIcon = ({ w = 48, h = 18, fill = '#2E3C4D', className = '' }) => (
  <svg
    width="10"
    height="9"
    viewBox="0 0 10 9"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.44517 7.96991C4.26601 7.75312 4.45241 6.98785 4.19269 6.29088C4.14052 6.13203 4.05107 5.99386 3.93377 5.8909C3.25096 5.33701 3.06803 4.51646 3.04796 3.61462C3.05409 3.42925 3.03033 3.24556 2.97787 3.07264C2.92702 2.87749 2.77058 2.74646 2.58964 2.74746C2.41019 2.75003 2.24863 2.88613 2.19205 3.0824C2.00535 3.56367 2.07887 4.03627 2.20793 4.5002C2.25507 4.67255 2.33498 4.83405 2.36677 5.00965C2.39857 5.18525 2.41027 5.39012 2.21077 5.48442C2.01126 5.57873 1.91791 5.42047 1.85742 5.27522C1.49335 4.40806 1.36731 3.54091 1.8375 2.61414C2.09627 2.11444 2.47397 1.80334 2.92233 1.56596C3.19884 1.4218 3.48246 1.29172 3.70286 1.03916C3.8821 0.836162 3.9846 0.613569 3.8781 0.341583C3.86772 0.315083 3.84159 0.298828 3.81275 0.298828C3.7981 0.298828 3.78363 0.303125 3.77088 0.310808C3.53732 0.451557 3.27961 0.488754 3.02966 0.536212C2.05722 0.732406 1.30494 1.26788 0.814465 2.28245C0.608522 2.71603 0.478779 2.98918 0.391712 3.45311C0.35351 3.70479 0.342148 3.95958 0.357878 4.21187C0.368794 4.46152 0.391689 4.70975 0.42646 4.95546C0.635819 6.23451 1.15502 7.23608 2.25827 7.7206C2.48287 7.8323 2.7183 7.91587 2.96056 7.96991C3.11943 8.00445 3.28382 8.00445 3.44517 7.96991Z"
      fill={fill}
    />
    <path
      d="M6.26327 7.99088C5.46098 7.7132 5.33244 6.93612 5.64365 6.26056C5.70759 6.10606 5.80714 5.97499 5.93182 5.88111C6.65421 5.37993 6.89812 4.5754 6.98571 3.6776C6.99348 3.4923 7.03094 3.3109 7.09621 3.1424C7.16154 2.95161 7.32736 2.83266 7.50772 2.84722C7.68647 2.86323 7.83737 3.01105 7.87909 3.21101C8.02921 3.70492 7.92047 4.17068 7.75702 4.62364C7.69709 4.79197 7.60531 4.94703 7.56045 5.11976C7.51558 5.29248 7.48856 5.49589 7.68044 5.60488C7.87232 5.71387 7.97726 5.56305 8.04847 5.42274C8.47649 4.5853 8.66715 3.73003 8.26772 2.77063C8.04712 2.25294 7.6938 1.91443 7.26449 1.64411C6.99956 1.47963 6.72649 1.32868 6.52563 1.06031C6.3621 0.84445 6.27657 0.614802 6.40315 0.351561C6.41548 0.325913 6.44276 0.311662 6.47152 0.313823C6.48613 0.314921 6.50023 0.32029 6.51237 0.328906C6.73474 0.486761 6.98892 0.543163 7.23462 0.609217C8.18963 0.877726 8.89967 1.46806 9.31274 2.51653C9.48562 2.96432 9.59453 3.24643 9.64659 3.71558C9.66582 3.96941 9.65806 4.22433 9.62347 4.47474C9.59388 4.72287 9.55245 4.96869 9.49936 5.2111C9.19475 6.47087 8.60196 7.43072 7.46551 7.83121C7.23317 7.92577 6.99214 7.99147 6.74652 8.0272C6.5855 8.04974 6.42158 8.03742 6.26327 7.99088Z"
      fill={fill}
    />
    <path
      d="M5.05723 4.31934C5.23939 4.31934 5.49619 4.36442 5.51983 4.71002C5.5381 4.96869 5.2316 5.24668 4.94334 5.25205C4.67292 5.26171 4.37222 4.99338 4.36964 4.74008C4.36706 4.48677 4.60076 4.31934 5.05723 4.31934Z"
      fill={fill}
    />
    <path
      d="M6.4532 3.2453C6.4537 3.36325 6.40694 3.47773 6.32468 3.55998C6.16655 3.71808 5.88066 3.72523 5.71901 3.58227C5.7131 3.57705 5.70733 3.57163 5.70169 3.56601C5.61643 3.48103 5.57119 3.35944 5.57762 3.23252C5.57234 2.97996 5.76004 2.76271 5.99712 2.74696H6.02226C6.27528 2.73291 6.45281 2.8888 6.4532 3.2453Z"
      fill={fill}
    />
    <path
      d="M4.43026 3.2453C4.43075 3.36325 4.384 3.47773 4.30173 3.55998C4.1436 3.71808 3.85771 3.72523 3.69606 3.58227C3.69015 3.57705 3.68438 3.57163 3.67874 3.56601C3.59348 3.48103 3.54824 3.35944 3.55467 3.23252C3.54939 2.97996 3.73709 2.76271 3.97418 2.74696H3.99931C4.25233 2.73291 4.42986 2.8888 4.43026 3.2453Z"
      fill={fill}
    />
    <path
      d="M3.44517 7.96991C4.26601 7.75312 4.45241 6.98785 4.19269 6.29088C4.14052 6.13203 4.05107 5.99386 3.93377 5.8909C3.25096 5.33701 3.06803 4.51646 3.04796 3.61462C3.05409 3.42925 3.03033 3.24556 2.97787 3.07264C2.92702 2.87749 2.77058 2.74646 2.58964 2.74746C2.41019 2.75003 2.24863 2.88613 2.19205 3.0824C2.00535 3.56367 2.07887 4.03627 2.20793 4.5002C2.25507 4.67255 2.33498 4.83405 2.36677 5.00965C2.39857 5.18525 2.41027 5.39012 2.21077 5.48442C2.01126 5.57873 1.91791 5.42047 1.85742 5.27522C1.49335 4.40806 1.36731 3.54091 1.8375 2.61414C2.09627 2.11444 2.47397 1.80334 2.92233 1.56596C3.19884 1.4218 3.48246 1.29172 3.70286 1.03916C3.8821 0.836162 3.9846 0.613569 3.8781 0.341583C3.86772 0.315083 3.84159 0.298828 3.81275 0.298828C3.7981 0.298828 3.78363 0.303125 3.77088 0.310808C3.53732 0.451557 3.27961 0.488754 3.02966 0.536212C2.05722 0.732406 1.30494 1.26788 0.814465 2.28245C0.608522 2.71603 0.478779 2.98918 0.391712 3.45311C0.35351 3.70479 0.342148 3.95958 0.357878 4.21187C0.368794 4.46152 0.391689 4.70975 0.42646 4.95546C0.635819 6.23451 1.15502 7.23608 2.25827 7.7206C2.48287 7.8323 2.7183 7.91587 2.96056 7.96991C3.11943 8.00445 3.28382 8.00445 3.44517 7.96991Z"
      fill={fill}
    />
    <path
      d="M6.26327 7.99088C5.46098 7.7132 5.33244 6.93612 5.64365 6.26056C5.70759 6.10606 5.80714 5.97499 5.93182 5.88111C6.65421 5.37993 6.89812 4.5754 6.98571 3.6776C6.99348 3.4923 7.03094 3.3109 7.09621 3.1424C7.16154 2.95161 7.32736 2.83266 7.50772 2.84722C7.68647 2.86323 7.83737 3.01105 7.87909 3.21101C8.02921 3.70492 7.92047 4.17068 7.75702 4.62364C7.69709 4.79197 7.60531 4.94703 7.56045 5.11976C7.51558 5.29248 7.48856 5.49589 7.68044 5.60488C7.87232 5.71387 7.97726 5.56305 8.04847 5.42274C8.47649 4.5853 8.66715 3.73003 8.26772 2.77063C8.04712 2.25294 7.6938 1.91443 7.26449 1.64411C6.99956 1.47963 6.72649 1.32868 6.52563 1.06031C6.3621 0.84445 6.27657 0.614802 6.40315 0.351561C6.41548 0.325913 6.44276 0.311662 6.47152 0.313823C6.48613 0.314921 6.50023 0.32029 6.51237 0.328906C6.73474 0.486761 6.98892 0.543163 7.23462 0.609217C8.18963 0.877726 8.89967 1.46806 9.31274 2.51653C9.48562 2.96432 9.59453 3.24643 9.64659 3.71558C9.66582 3.96941 9.65806 4.22433 9.62347 4.47474C9.59388 4.72287 9.55245 4.96869 9.49936 5.2111C9.19475 6.47087 8.60196 7.43072 7.46551 7.83121C7.23317 7.92577 6.99214 7.99147 6.74652 8.0272C6.5855 8.04974 6.42158 8.03742 6.26327 7.99088Z"
      fill={fill}
    />
    <path
      d="M5.05723 4.32031C5.23939 4.32031 5.49619 4.36539 5.51983 4.711C5.5381 4.96967 5.2316 5.24766 4.94334 5.25303C4.67292 5.26269 4.37222 4.99436 4.36964 4.74105C4.36706 4.48775 4.60076 4.32031 5.05723 4.32031Z"
      fill={fill}
    />
    <path
      d="M6.4532 3.2453C6.4537 3.36325 6.40694 3.47773 6.32468 3.55998C6.16655 3.71808 5.88066 3.72523 5.71901 3.58227C5.7131 3.57705 5.70733 3.57163 5.70169 3.56601C5.61643 3.48103 5.57119 3.35944 5.57762 3.23252C5.57234 2.97996 5.76004 2.76271 5.99712 2.74696H6.02226C6.27528 2.73291 6.45281 2.8888 6.4532 3.2453Z"
      fill={fill}
    />
    <path
      d="M4.43026 3.24628C4.43075 3.36422 4.384 3.47871 4.30173 3.56096C4.1436 3.71906 3.85771 3.72621 3.69606 3.58325C3.69015 3.57803 3.68438 3.5726 3.67874 3.56699C3.59348 3.48201 3.54824 3.36042 3.55467 3.2335C3.54939 2.98094 3.73709 2.76369 3.97418 2.74794H3.99931C4.25233 2.73388 4.42986 2.88978 4.43026 3.24628Z"
      fill={fill}
    />
  </svg>
);

export default AnimallIcon;
