import { useContext, useEffect, useMemo, useState } from 'react';

import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

import usePlans from 'hooks/monetization/usePlans';
import useCustomerSupport from 'hooks/useCustomerSupport';
import useStreakPopup from 'hooks/useStreakPopup';

import { logAmplitudeEvent, logFbEvent } from 'lib/log-event';
import { deleteCookie, getCookie, setCookieForToday } from 'lib/storage';
import { cn } from 'lib/utils';

import { SecondaryBody, TertiaryBody } from 'components/ui/typography';

import { FreeListingTimer } from './components/FreeListingTimer';
import Header from './components/Header';
import PaymentCTA from './components/PaymentCTA';
import Plans from './components/Plans';
import Support from './components/Support';
import TopSection from './components/TopSection';
import UserInfoFooter from './components/UserInfoFooter';

import ActivePlan, {
  getAdditionalPlansInfo,
} from 'modules/additional-plans/components/ActivePlan';
import StreakPopup from 'modules/buy/components/StreakPopup/';
import { ArrowRightLongIcon } from 'modules/home/<USER>';
import { sendEventToServer } from 'modules/plans/utils';
import { useGrowthUser } from 'providers/GrowthUserProvider';
import { MonetizationContext } from 'providers/Monetization';

import { getAudioFileUrl } from './utils';

const ClassifiedPlans = ({
  post,
  lastFreeSubscription,
  freeListingPlans = false,
  blockedDraftedListing,
  streakData = null,
  userData = null,
}) => {
  const router = useRouter();
  const { t } = useTranslation('classified_plans');
  const { isGrowthUser } = useGrowthUser();
  const { preSelectedPlan, source, extraDiscount } = router.query;

  const {
    primaryPlans,
    otherPlans,
    uiVariant,
    defaultSelectedId,
    extraDiscountConfig,
    setExtraDiscountConfig,
  } = usePlans({ blockedDraftedListing, freeListingPlans });
  const [showPaymentSuccess, setShowPaymentSuccess] = useState(false);
  const [showNewFreePlansPage, setShowNewFreePlansPage] = useState(false);
  const [continuedWithoutPlan, setCountinuedWithoutPlan] = useState(false);
  const [freeSubscriptionEndDate, setFreeSubscriptionEndDate] = useState(
    lastFreeSubscription?.endDate || null,
  );

  const {
    closeStreakPopup,
    openStreakPopup,
    setStreakData,
    showStreakPopup,
    streakData: _streakData,
  } = useStreakPopup(streakData, 'PLANS_PAGE');

  const monetizationStats = useContext(MonetizationContext);

  const { listing } = monetizationStats;

  const isEligibleForVIPUserPlansPage = useMemo(
    () => monetizationStats?.listing?.isEligibleForVIPUserPlansPage,
    [monetizationStats],
  );

  useEffect(() => {
    setFreeSubscriptionEndDate(lastFreeSubscription?.endDate);
  }, [listing, lastFreeSubscription]);

  const plansInfo = useMemo(
    () => getAdditionalPlansInfo(monetizationStats),
    [monetizationStats],
  );

  const [selectedPlanId, setSelectedPlanId] = useState();
  useEffect(() => {
    if (preSelectedPlan) {
      setSelectedPlanId(Number(preSelectedPlan));
      return;
    }
    setSelectedPlanId(defaultSelectedId);
  }, [
    preSelectedPlan,
    defaultSelectedId,
    isEligibleForVIPUserPlansPage,
    primaryPlans,
  ]);

  const currentPlan = useMemo(() => {
    const _plans = [...primaryPlans, ...otherPlans];
    return _plans?.find((plan) => plan?.id === selectedPlanId);
  }, [primaryPlans, otherPlans, selectedPlanId]);

  useEffect(() => {
    if (!currentPlan) return;

    const audioUrl = getAudioFileUrl(currentPlan);
    if (!audioUrl) return;

    const audio = new Audio(audioUrl);
    audio.play().catch((err) => {
      console.warn('Audio play failed:', err);
    });
    return () => {
      audio.pause();
      audio.currentTime = 0;
    };
  }, [currentPlan]);

  const onBeforePaymentStart = ({ plan }) => {
    const strategy = sessionStorage.getItem('listingBlockStrategy');
    logAmplitudeEvent('CLICKEDPAYMENT', 'LISTINGLIMITUTD', 'POPUP', {
      PLAN: plan?.name,
      SOURCE: source || 'NA',
      POST_ID: post?._id || 'NA',
      STRATEGY: strategy || 'NA',
    });
    sendEventToServer('CLICKEDPAYMENT_LISTINGLIMITUTD_POPUP');
  };

  useEffect(() => {
    const postId = post?._id;
    logAmplitudeEvent('LANDED', 'PLANSPAGE', 'MONETIZATION', {
      PLAN_TYPE: freeListingPlans ? 'FREE_LISTING' : 'LISTING',
      SOURCE: source || 'NA',
      VARIANT: 'NA',
      POST_ID: postId || 'NA',
    });
    logFbEvent('LANDED_PLANSPAGE_CLASSIFIEDS', postId || 'NA');
  }, [source, post]);

  const onClose = () => {
    // show success popup on back
    if (freeListingPlans && !showPaymentSuccess) {
      setShowPaymentSuccess(true);
      setCountinuedWithoutPlan(true);
    } else {
      router.back();
    }
  };
  const supportNumber = useCustomerSupport();

  const onFailureSupport = () => {
    logAmplitudeEvent('CLICKED', 'SUPPORT', 'PAYMENTFAILURE', {
      PLAN_TYPE: 'LISTING',
      SOURCE: source || 'NA',
    });
    window.open(`https://wa.me/91${supportNumber}`, '_blank');
  };

  useEffect(() => {
    const _showNewFreePlansPage = freeListingPlans && !isGrowthUser;
    setShowNewFreePlansPage(_showNewFreePlansPage);
  }, [freeListingPlans, isGrowthUser]);

  useEffect(() => {
    if (extraDiscount) return;
    const _plansPageVisits = Number(getCookie('plansPageVisits') || 0);
    setCookieForToday('plansPageVisits', _plansPageVisits + 1);
    deleteCookie('discountTimerStartDate');
  }, []);

  const updatedPrimaryPlans = showNewFreePlansPage
    ? primaryPlans?.filter(
        (plan) => plan?.price !== 0 || plan?.discount?.price !== 0,
      )
    : primaryPlans;

  return (
    <div
      className={cn(
        'min-h-screen',
        isEligibleForVIPUserPlansPage && 'bg-surface-0',
      )}
    >
      <Header onBack={onClose} showNewFreePlansPage={showNewFreePlansPage} />
      {!isEligibleForVIPUserPlansPage && (
        <TopSection
          freeListingPlans={freeListingPlans}
          isEligibleForExtraDiscount={!!extraDiscountConfig}
          post={post}
          showNewFreePlansPage={showNewFreePlansPage}
        />
      )}
      {isEligibleForVIPUserPlansPage && (
        <div className="mt-6 mx-4">
          <ActivePlan plansInfo={plansInfo} showListingLimitTag={true} />
          <div className="font-rajdhani font-bold text-2xl mb-2 leading-8 text-text-primary text-center mt-6">
            इस <span className="text-primary-600">महीने</span> के{' '}
            <span className="text-primary-600">
              {plansInfo?.listingLimit} पशु दर्ज
            </span>{' '}
            कर लिए है
          </div>
          <SecondaryBody className="text-center">
            और दर्ज करने के लिए प्लान अपग्रेड करें
          </SecondaryBody>
        </div>
      )}

      <Plans
        primaryPlans={updatedPrimaryPlans}
        otherPlans={otherPlans}
        uiVariant={uiVariant}
        selectedPlanId={selectedPlanId}
        setSelectedPlanId={setSelectedPlanId}
        freeListingPlans={freeListingPlans}
        extraDiscountConfig={extraDiscountConfig}
        setExtraDiscountConfig={setExtraDiscountConfig}
      />
      {!isEligibleForVIPUserPlansPage ? (
        showNewFreePlansPage ? (
          <div
            className="flex gap-3 items-center justify-center mx-auto mt-8 mb-4"
            onClick={onClose}
          >
            <SecondaryBody className="text-text-secondary">
              {/* अभी नहीं, मैं बिक्री में समय लगने के लिए तैयार हूँ */}
              {t('notNowImReadyToWait')}
            </SecondaryBody>
            <span>
              <ArrowRightLongIcon w={17} h={10} fill="#656F7C" />
            </span>
          </div>
        ) : (
          <>
            <Support />
            <TertiaryBody className="text-text-primary flex items-center justify-center gap-1 my-6">
              {t('next')}{' '}
              <span className="font-bold text-primary-600">FREE</span>{' '}
              {/* पशु  */}
              {t('cattle')} {/* दर्ज */}
              {t('listing')}
              <FreeListingTimer
                endTime={freeSubscriptionEndDate}
                classNames="bg-surface-0 text-text-primary "
              />
              {/* बाद होगा */}
              {t('after')}
            </TertiaryBody>
          </>
        )
      ) : null}

      {!isEligibleForVIPUserPlansPage && (
        <UserInfoFooter
          streakData={streakData}
          userData={userData}
          openStreakPopup={openStreakPopup}
        />
      )}
      {!isEligibleForVIPUserPlansPage && (
        <StreakPopup
          streakData={_streakData}
          showPopup={showStreakPopup}
          closePopup={closeStreakPopup}
          updateStreakData={setStreakData}
        />
      )}
      {isEligibleForVIPUserPlansPage && <Support className="mt-12 mb-6" />}
      <div className="mt-2 sticky bottom-0 inset-x-0 z-10 bg-surface-3 shadow-[4px_0px_10px_0px_#0000001A]">
        {currentPlan && (
          <PaymentCTA
            isEligibleForNewFreePlansPage={showNewFreePlansPage}
            disabled={!selectedPlanId}
            plan={currentPlan}
            source={source}
            post={post}
            isQrEnabled={!currentPlan?.extraInfo?.isSubscription}
            isWalletEnabled={!currentPlan?.extraInfo?.isSubscription}
            onBeforePaymentStart={onBeforePaymentStart}
            onFailureSupport={onFailureSupport}
            _showPaymentStatus={showPaymentSuccess}
            continuedWithoutPlan={continuedWithoutPlan}
          />
        )}
      </div>
    </div>
  );
};

export default ClassifiedPlans;
