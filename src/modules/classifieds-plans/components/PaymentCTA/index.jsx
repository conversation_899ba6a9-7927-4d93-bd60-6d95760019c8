import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

import usePayment from 'hooks/monetization/usePayment';
import useSubscription from 'hooks/monetization/useSubscription';

import { logAmplitudeEvent, logFbEvent } from 'lib/log-event';
import { cn, getCommaSeparatedNumber } from 'lib/utils';

import { AnimallLoaderContext } from 'components/AnimallLoader';
import ChakraProvider from 'components/ChakraProvider';
import BottomSheet from 'components/Popups/BottomSheet';
import QrPopupV2 from 'components/QrPopupV2';
import Image from 'components/UnoptimizedImage';
import Button from 'components/ui/Button';
import { H3, SecondaryBody } from 'components/ui/typography';

import { addDays } from 'date-fns';
import WalletInfo from 'modules/plans/components/WalletInfo';
import { sendEventToServer } from 'modules/plans/utils';
import VideoUpload from 'modules/prime/components/VideoUpload';
import PrimePopup from 'modules/sell/modals/PrimePopup';
import SuccessPopup from 'modules/sell/modals/SuccessPopup';
import { usePaymentConfirmation } from 'providers/PaymentConfirmationProvider';
import { WalletContext } from 'providers/Wallet';

import { QRCodeIcon } from '../../icons/payment';
import PaymentSuccessPopup from '../PaymentSuccessPopup';

// import CouponApply from '../CouponApply';

const CTAWithWalletBalance = ({
  disabled,
  plan,
  isWalletEnabled,
  setUseWalletBalance,
  effectivePrice,
  onClick,
  maxUsableWalletBalance,
  children,
  isEligibleForNewFreePlansPage,
  initialDefaultText,
}) => {
  const { wallet } = useContext(WalletContext);
  const { extraInfo = {} } = plan;
  const { maxUsage: pashuCount = 1 } = extraInfo;
  const { t } = useTranslation('common');
  const isVipUserPlan = plan?.name === 'APP_SUBSCRIPTION_399_VIP_USER';

  const isDiscounted = plan.discount
    ? plan.discount.extraInfo.percent > 0
    : false;
  const isUnlimitedPlan = pashuCount > 1000;
  const benefits = plan?.extraInfo?.benefits;
  const { prime, guarantee, guranteedSaleDays } = benefits || {};
  const ctaText = prime
    ? 'जल्दी बेचें'
    : guarantee
    ? guranteedSaleDays
      ? 'नहीं बिका तो पैसा वापस'
      : 'गारंटी से बेचे'
    : 'पशु बेचें';

  return (
    <div className={cn('w-full', disabled && 'opacity-50 pointer-events-none')}>
      {isWalletEnabled && (
        <WalletInfo
          walletChangeHandler={(e) => setUseWalletBalance(e.target.checked)}
          wallet={wallet}
          borderless
          className="p-0 pb-2"
          maxUsableWalletBalance={maxUsableWalletBalance}
        />
      )}
      <Button
        size="xl"
        textClassName={cn(
          'w-full flex justify-between items-center text-surface-3',
          isVipUserPlan && ' text-black',
        )}
        className={cn(
          'relative shine',
          isVipUserPlan &&
            'bg-[linear-gradient(102.08deg,_#E2B65C_24.15%,_#F8D851_61.31%,_#E2B65C_92.1%)]',
        )}
        onClick={() => onClick('MAIN_BTN')}
        id="GROWTH_PLAN_PAYMENT_CTA_MAIN"
      >
        {children || (
          <>
            {initialDefaultText ||
              (isEligibleForNewFreePlansPage ? (
                <span>{ctaText}</span>
              ) : (
                <span>
                  {isUnlimitedPlan ? 'unlimited' : pashuCount}{' '}
                  {t('noPostsToast.cta')}
                </span>
              ))}
            <span className="flex items-center gap-1.5">
              {isDiscounted && (
                <span className="text-primary-25/50 text-lg font-semibold leading-body line-through">
                  ₹{getCommaSeparatedNumber(plan.price)}
                </span>
              )}
              ₹{getCommaSeparatedNumber(effectivePrice)}
            </span>
          </>
        )}
      </Button>
    </div>
  );
};

export const MultiplePaymentButtons = ({
  isOpen,
  onClose,
  effectivePrice,
  onClick,
  disabled,
}) => {
  const { t } = useTranslation('common');
  return (
    <BottomSheet
      className="flex flex-col gap-6 py-8 px-4"
      isOpen={isOpen}
      onClose={onClose}
      disableBackdropClose
    >
      <Button
        disabled={disabled}
        icon={() => (
          <Image
            src="https://static-assets.animall.in/static/images/upi-icons.png"
            alt="upi payment"
            width={85}
            height={32}
          />
        )}
        iconPosition="right"
        size="xl"
        onClick={() => onClick('UPI_BTN')}
      >
        <span className="flex items-center gap-3">
          <H3 className="text-surface-3">₹{effectivePrice}</H3>
          <SecondaryBody className="text-surface-3 text-base leading-body">
            {/* UPI से पेमेंट करें */}
            {t('paymentCta.paymentByUPI')}
          </SecondaryBody>
        </span>
      </Button>
      <Button
        disabled={disabled}
        variant="outline"
        icon={() => <QRCodeIcon fill="#14776F" />}
        iconPosition="right"
        size="xl"
        onClick={() => onClick('QR_BTN')}
      >
        <span className="flex items-center gap-3">
          <H3 className="text-primary-600">₹{effectivePrice}</H3>
          <SecondaryBody className="text-primary-600 text-base leading-body">
            {/* QR कोड पर पेमेंट करें */}
            {t('paymentCta.payOnQrCode')}
          </SecondaryBody>
        </span>
      </Button>
    </BottomSheet>
  );
};

//! QR code integration is still work in progress. Not being used in current implementation.
const PaymentCTA = ({
  disabled,
  plan,
  post,
  isWalletEnabled = true,
  isQrEnabled = false,
  onBeforePaymentStart = () => {},
  successText = '',
  onAfterPaymentSuccess,
  onFailureSupport,
  pageSource = 'PLANS_PAGE',
  overridePayment = false, // If true, payment will not be initiated. Used to control on click flow from parent component (non logged-in case).
  children,
  setEffectivePrice: _setEffectivePrice = () => {},
  onSuccess = () => {},
  showDefaultSuccessPopup = true,
  isBillPayment = false,
  vetPartnerCode = null,
  isEligibleForNewFreePlansPage = false,
  _showPaymentStatus = false,
  continuedWithoutPlan = false,
  initialDefaultText,
}) => {
  const { wallet } = useContext(WalletContext);

  const router = useRouter();
  const { source } = router.query;

  const [qrUrl, setQrUrl] = useState('');
  const [useWalletBalance, setUseWalletBalance] = useState(true);
  const [showPaymentSuccess, setShowPaymentSuccess] = useState(false);

  const [showTrialPopup, setShowTrialPopup] = useState(false);
  const [showVideoUpload, setShowVideoUpload] = useState(false);
  const [showPrimePopup, setShowPrimePopup] = useState(false);

  const [couponDiscount, setCouponDiscount] = useState(0);
  const [appliedCouponCode, setAppliedCouponCode] = useState(null);
  const [showQrPopup, setShowQrPopup] = useState(false);
  const [showMultipleButtons, setShowMultipleButtons] = useState(false);
  const paidPlanRef = useRef(null);

  const amplitudeLocation = useMemo(
    () =>
      post && ['LISTING', 'PRIME'].includes(plan?.type)
        ? 'ADDPRODUCT'
        : pageSource.replace(/_/g, ''),
    [post, pageSource, plan?.type],
  );

  const [primeVideoUrl, setPrimeVideoUrl] = useState('');
  useEffect(() => {
    if (!post) return;
    const videoResource = post.resources?.find((r) => r?.isVideo);
    if (videoResource) {
      setPrimeVideoUrl(videoResource.url);
    }
  }, [post]);

  useEffect(() => {
    if (_showPaymentStatus) {
      setShowPaymentSuccess(_showPaymentStatus);
    }
  }, [_showPaymentStatus]);

  // const { isOpen: showMultipleButtons, onToggle: toggleMultipleButtons } =
  //   useBottomSheet('multiple-buttons');
  // const { isOpen: showQrPopup, onToggle: toggleQrPopup } =
  //   useBottomSheet('qr-popup');

  // const setShowMultipleButtons = useCallback(
  //   (value) => {
  //     if (value) {
  //       if (showMultipleButtons) return;
  //     } else {
  //       if (!showMultipleButtons) return;
  //     }
  //     toggleMultipleButtons();
  //   },
  //   [showMultipleButtons, toggleMultipleButtons],
  // );

  // const setShowQrPopup = useCallback(
  //   (value) => {
  //     if (value) {
  //       if (showQrPopup) return;
  //     } else {
  //       if (!showQrPopup) return;
  //     }
  //     toggleQrPopup();
  //   },
  //   [showQrPopup, toggleQrPopup],
  // );

  useEffect(() => {
    // Clear qrUrl on popup close
    if (!showQrPopup) {
      setQrUrl('');
    }
  }, [showQrPopup]);

  const resetPageState = useCallback(() => {
    setShowQrPopup(false);
    setShowMultipleButtons(false);
  }, [setShowMultipleButtons, setShowQrPopup]);

  const maxUsableWalletBalance = useMemo(() => {
    if (!isWalletEnabled) return 0;

    const amountAfterDiscount = plan.discount
      ? plan.discount.price
      : plan.price;

    return Math.floor(
      Math.max(Math.min(wallet?.balance, amountAfterDiscount * 0.1), 0),
    );
  }, [isWalletEnabled, wallet?.balance, plan]);

  const [effectivePrice, setEffectivePrice] = useState(() => {
    const amountAfterDiscount = plan.discount
      ? plan.discount.price
      : plan.price;

    if (!useWalletBalance) return amountAfterDiscount;

    return (
      amountAfterDiscount -
      Math.min(maxUsableWalletBalance, amountAfterDiscount)
    );
  });
  useEffect(() => {
    _setEffectivePrice(effectivePrice);
  }, [effectivePrice, _setEffectivePrice]);

  useEffect(() => {
    let amountAfterDiscount = plan.discount ? plan.discount.price : plan.price;
    amountAfterDiscount = Math.max(1, amountAfterDiscount - couponDiscount);

    if (!useWalletBalance) {
      setEffectivePrice(amountAfterDiscount);
      return;
    }

    setEffectivePrice(
      amountAfterDiscount -
        Math.min(maxUsableWalletBalance, amountAfterDiscount),
    );
  }, [useWalletBalance, maxUsableWalletBalance, plan, couponDiscount]);

  const redirectToHomePage = useCallback(
    (sourceParam = 'AddProductPayment') => {
      logAmplitudeEvent('CLOSED', 'PLANSPAGE', 'MONETIZATION', {
        PLAN_TYPE: 'LISTING',
        SOURCE: pageSource || 'NA',
      });
      router.replace(`/home?source=${sourceParam}`);
    },
    [pageSource, router],
  );

  const { openAnimallLoader, closeAnimallLoader } =
    useContext(AnimallLoaderContext);

  const { showSuccessPopup, showErrorPopup } = usePaymentConfirmation();
  const { startSubscription } = useSubscription({
    source: pageSource,
    onSubscriptionSuccess: () =>
      showSuccessPopup({
        text: successText,
        amount: effectivePrice,
        onClose:
          typeof onAfterPaymentSuccess === 'function'
            ? onAfterPaymentSuccess
            : redirectToHomePage,
        onOk:
          typeof onAfterPaymentSuccess === 'function'
            ? onAfterPaymentSuccess
            : redirectToHomePage,
      }),
    onSubscriptionFailure: showErrorPopup,
  });

  const successCallback = useCallback(
    async (paidPlan) => {
      const strategy = sessionStorage.getItem('listingBlockStrategy');
      logAmplitudeEvent('SUCCESS', 'PAYMENTUTD', amplitudeLocation, {
        PLAN: paidPlan?.name,
        SOURCE: source || 'NA',
        POST_ID: post?._id || 'NA',
        STRATEGY: strategy || 'NA',
      });
      logFbEvent('PAYMENT_SUCCESS_CLASSIFIEDS');
      sendEventToServer(`SUCCESS_PAYMENTUTD_${amplitudeLocation}`);
      onSuccess();
      paidPlanRef.current = paidPlan;
      if (
        post &&
        ['LISTING', 'PRIME'].includes(plan?.type) &&
        !plan?.name?.startsWith('GROWTH_')
      ) {
        closeAnimallLoader();
        resetPageState();
        if (plan?.name?.endsWith('WITH_TRIAL')) {
          setShowTrialPopup(true);
        } else {
          setShowPaymentSuccess(true);
        }
        return;
      }
      closeAnimallLoader();

      if (showDefaultSuccessPopup) {
        if (plan?.name?.endsWith('WITH_TRIAL')) {
          setShowTrialPopup(true);
        } else {
          showSuccessPopup({
            text: successText,
            amount: effectivePrice,
            onClose:
              typeof onAfterPaymentSuccess === 'function'
                ? onAfterPaymentSuccess
                : redirectToHomePage,
            onOk:
              typeof onAfterPaymentSuccess === 'function'
                ? onAfterPaymentSuccess
                : redirectToHomePage,
          });
        }
      } else {
        if (typeof onAfterPaymentSuccess === 'function') {
          onAfterPaymentSuccess();
        } else redirectToHomePage();
      }
    },
    [
      amplitudeLocation,
      source,
      post,
      onSuccess,
      plan?.type,
      plan?.name,
      closeAnimallLoader,
      showDefaultSuccessPopup,
      resetPageState,
      showSuccessPopup,
      successText,
      effectivePrice,
      onAfterPaymentSuccess,
      redirectToHomePage,
    ],
  );

  const failureCallback = useCallback(
    (args) => {
      const strategy = sessionStorage.getItem('listingBlockStrategy');
      logAmplitudeEvent('ERROR', 'PAYMENTUTD', amplitudeLocation, {
        STEP: 'CAPTURE',
        PLAN: plan?.name,
        SOURCE: source || 'NA',
        POST_ID: post?._id || 'NA',
        STRATEGY: strategy || 'NA',
      });
      closeAnimallLoader();
      resetPageState();
      showErrorPopup({
        onSupport: onFailureSupport,
      });
    },
    [
      plan,
      post,
      source,
      resetPageState,
      closeAnimallLoader,
      showErrorPopup,
      onFailureSupport,
      amplitudeLocation,
    ],
  );

  const setQrUrlAndShowPopup = useCallback(
    (url) => {
      closeAnimallLoader();
      console.log('QR URL:', url);
      setQrUrl(url);
      setShowQrPopup(true);
    },
    [setQrUrl, setShowQrPopup, closeAnimallLoader],
  );

  const { startPayment: _startPayment } = usePayment({
    source: pageSource,
    onPaymentSuccess: successCallback,
    onPaymentFailure: failureCallback,
    setQrUrl: setQrUrlAndShowPopup,
    isBillPayment: isBillPayment,
  });

  const startPayment = async (...args) => {
    onBeforePaymentStart({ plan });
    if (overridePayment) return;
    await _startPayment(...args);
  };

  const walletApiParams = useMemo(() => {
    if (!isWalletEnabled) return {};
    if (!useWalletBalance) return {};
    if (maxUsableWalletBalance <= 0) return {};

    return {
      useWalletBalance,
      walletBalance: maxUsableWalletBalance,
    };
  }, [useWalletBalance, maxUsableWalletBalance, isWalletEnabled]);

  const paymentArgs = useRef({});

  const isPrimePlan = useMemo(
    () =>
      !continuedWithoutPlan &&
      (plan.type === 'PRIME' || plan.extraInfo?.benefits?.guarantee),
    [plan, continuedWithoutPlan],
  );

  const checkAndStartPayment = (...args) => {
    const isSinglePlan = plan.extraInfo?.isSinglePlan;
    if (isSinglePlan && !post && plan.type === 'LISTING') {
      openAnimallLoader();
      window.location.href = `/sell?source=LISTING_LIMIT_POPUP&preSelectedPlan=${plan?.id}`;
      return;
    }

    startPayment(...args);
    return;

    startPayment(...args);
    return;
  };

  const handlePaymentOnVideoUpload = (url) => {
    setPrimeVideoUrl(url);
    setShowVideoUpload(false);
    startPayment(...paymentArgs.current);
  };

  const onClick = (source = 'MAIN_BTN') => {
    if (disabled) return;
    switch (source) {
      case 'MAIN_BTN':
        const currPlanName = plan?.discount?.name || null;
        if (currPlanName === 'FREE_SINGLE_POST') {
          successCallback({ name: currPlanName });
        } else if (isQrEnabled) {
          setShowMultipleButtons(true);
        } else if (plan?.extraInfo?.isSubscription) {
          startSubscription(plan);
        } else {
          checkAndStartPayment('ORDER', plan, {
            postId: post?._id,
            ...(vetPartnerCode && { vetPartnerCode }),
            ...(appliedCouponCode && { couponCode: appliedCouponCode }),
            ...walletApiParams,
          });
        }

        break;
      case 'CLOSE_BTN':
        resetPageState();
        break;
      case 'UPI_BTN':
        setShowMultipleButtons(false);
        checkAndStartPayment('ORDER', plan, {
          postId: post?._id,
          ...walletApiParams,
          ...(vetPartnerCode && { vetPartnerCode }),
          ...(appliedCouponCode && { couponCode: appliedCouponCode }),
        });
        break;
      case 'QR_BTN':
        checkAndStartPayment('QR', plan, {
          postId: post?._id,
          ...walletApiParams,
          ...(vetPartnerCode && { vetPartnerCode }),
          ...(appliedCouponCode && { couponCode: appliedCouponCode }),
        });
        break;
      default:
        break;
    }
  };

  const onCloseSuccessPopup = () => {
    setShowPaymentSuccess(false);
    setShowPrimePopup(true);
  };

  const successOkHandler = () => {
    setShowPrimePopup(false);
    redirectToHomePage();
  };

  const closePaymentSuccessPopup = useCallback(() => {
    if (post) {
      resetPageState();
      setShowPaymentSuccess(true);
    } else {
      setShowTrialPopup(false);
      redirectToHomePage();
    }
  }, [post, redirectToHomePage, resetPageState]);

  const onQrShare = () => {
    const shareText = `Animall पर अपना पशु को दर्ज करने के लिए किसी भी UPI ऐप से इस QR कोड पे पेमेंट करें\n\nQR कोड: ${qrUrl}`;
    const url = `https://api.whatsapp.com/send?text=${encodeURIComponent(
      shareText,
    )}`;
    window.open(url, '_blank', 'noopener noreferrer');
  };

  const retryWithUpi = () => {
    setShowQrPopup(false);
    checkAndStartPayment('ORDER', plan, {
      postId: post?._id,
      ...walletApiParams,
      ...(appliedCouponCode && { couponCode: appliedCouponCode }),
    });
  };

  return (
    <ChakraProvider>
      <div
        className={cn(
          'flex flex-col gap-2 items-center justify-center rounded relative',
          'transition-all duration-500',
          !children ? 'p-4 bg-surface-3' : '',
          showMultipleButtons
            ? 'h-[151px] border-t-2 border-trueGray-200'
            : 'border-0',
        )}
      >
        <PaymentSuccessPopup
          isOpen={showTrialPopup}
          onClose={closePaymentSuccessPopup}
          planMaxUsage={plan?.extraInfo?.maxUsage || 3}
          startDate={addDays(new Date(), 3)}
        />

        <SuccessPopup
          source={pageSource}
          isOpen={showPaymentSuccess}
          onClose={onCloseSuccessPopup}
        />
        <PrimePopup
          isOpen={showPrimePopup}
          onClose={successOkHandler}
          post={post}
          source={pageSource}
          isPrime={isPrimePlan}
          primeVideoUrl={primeVideoUrl}
          isEligibleForNewFreePlansPage={isEligibleForNewFreePlansPage}
          selectedPlan={paidPlanRef?.current}
        />
        {showVideoUpload && (
          <VideoUpload
            postId={post?._id}
            price={effectivePrice}
            showBackdrop
            onClose={() => setShowVideoUpload(false)}
            onVideoUpload={(url) => handlePaymentOnVideoUpload(url)}
            source={pageSource}
          />
        )}
        <MultiplePaymentButtons
          isOpen={showMultipleButtons}
          onClose={() => setShowMultipleButtons(false)}
          effectivePrice={effectivePrice}
          onClick={onClick}
          disabled={disabled}
        />
        {/* <CouponApply
          planPrice={plan.discount ? plan.discount.price : plan.price}
          defaultCodeFromURL={router.query?.coupon}
          onDiscountChange={(amount) => setCouponDiscount(amount)}
          onCouponApplied={(data) => setAppliedCouponCode(data.code)}
        /> */}

        <CTAWithWalletBalance
          disabled={disabled}
          plan={plan}
          isWalletEnabled={isWalletEnabled}
          setUseWalletBalance={setUseWalletBalance}
          effectivePrice={effectivePrice}
          onClick={onClick}
          maxUsableWalletBalance={maxUsableWalletBalance}
          isEligibleForNewFreePlansPage={isEligibleForNewFreePlansPage}
          initialDefaultText={initialDefaultText}
        >
          {children}
        </CTAWithWalletBalance>
        <QrPopupV2
          isOpen={showQrPopup}
          onClose={() => setShowQrPopup(false)}
          qrUrl={qrUrl}
          onShare={onQrShare}
          amount={effectivePrice}
          retryWithUpi={retryWithUpi}
        />
      </div>
    </ChakraProvider>
  );
};

export default PaymentCTA;
