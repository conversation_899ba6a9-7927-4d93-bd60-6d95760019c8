import { useContext, useMemo, useState } from 'react';

import { useTranslation } from 'next-i18next';

import { cn, getCommaSeparatedNumber, isInBuckets } from 'lib/utils';

import CancelAutoPayInfoPopup from 'components/AutoPay/CancelAutoPayInfoPopup';
import {
  H3,
  H4,
  PrimaryBody,
  SecondaryBody,
  TertiaryBody,
} from 'components/ui/typography';

import { GoldenCalenderIcon } from 'modules/buy/Icons';
import { PlanVariant, UiVariant } from 'modules/classifieds-plans/enums';
import { BlockedIcon } from 'modules/classifieds-plans/icons/BlockedIcon';
import { LockIcon } from 'modules/classifieds-plans/icons/LockIcon';
import { PhoneIcon } from 'modules/classifieds-plans/icons/PhoneIcon';
import EncircledTickIcon from 'modules/classifieds-plans/icons/TickIcon';
import {
  Discount10SmallIcon,
  Discount20SmallIcon,
  Discount30SmallIcon,
} from 'modules/classifieds-plans/icons/discounts';
import {
  GoldCrownIcon,
  SilverCrownIcon,
  TickIcon,
} from 'modules/classifieds-plans/icons/plans';
import RupeeIcon from 'modules/prime/icons/RupeeIcon';
import { parseCookies } from 'nookies';
import { MonetizationContext } from 'providers/Monetization';

const DiscountImage = {
  1: <Discount10SmallIcon bg="#B1F2ED" />,
  2: <Discount20SmallIcon bg="#B1F2ED" />,
  3: <Discount30SmallIcon bg="#B1F2ED" />,
};
const benefitIcons = [
  <BlockedIcon key={1} />,
  <PhoneIcon key={2} />,
  <LockIcon key={3} />,
  <RupeeIcon key={4} />,
  <PhoneIcon key={5} />,
];
export const PlanBadge = ({ planVariant, classNames = '' }) => {
  const { t } = useTranslation('classified_plans');
  if (![...Object.values(PlanVariant)].includes(planVariant)) {
    return null;
  }

  const badgeConfig =
    planVariant === PlanVariant.SILVER
      ? { text: t('classified_plans:silverPlan'), Icon: SilverCrownIcon }
      : {
          text:
            planVariant === PlanVariant.VIP_USER
              ? 'VIP User'
              : t('classified_plans:goldPlan'),
          Icon: GoldCrownIcon,
        };

  return (
    <span
      className={cn(
        'flex items-center gap-0.5',
        'px-2 py-1 rounded-full border-2 border-transparent',
        planVariant === PlanVariant.SILVER && 'bg-gradient-prime-silver-border',
        [PlanVariant.VIP, PlanVariant.VIP_USER].includes(planVariant) &&
          'bg-gradient-prime-golden-border',
        classNames,
      )}
    >
      <badgeConfig.Icon />
      <TertiaryBody className="font-bold text-text-primary">
        {badgeConfig.text}
      </TertiaryBody>
    </span>
  );
};

const PlanCard = ({ plan, isSelected, onSelect, uiVariant, index }) => {
  const [showAutoPayInfoPopup, setShowAutoPayInfoPopup] = useState(false);
  const { listing } = useContext(MonetizationContext);
  const { extraInfo, uiInfo, extraDiscountConfig } = plan;
  const { benefits, planVariant, isSubscription, isNewBenifits } = extraInfo;
  const { t } = useTranslation('classified_plans');
  const { t: c } = useTranslation('common');

  const { bucketId } = parseCookies();

  const isEligibleForVIPUserPlansPage = listing?.isEligibleForVIPUserPlansPage;

  const isInX8X9Buckets = useMemo(() => {
    return isInBuckets([80, 99], bucketId);
  }, [bucketId]);

  const { title, benefitTexts, tagLine, discountPercent, priceText } = uiInfo;

  const showFirstPaymentDiscountText =
    uiVariant === UiVariant.FIRST_PAYMENT && index === 0;

  const extraHeight = extraDiscountConfig
    ? benefitTexts.length > 1
      ? (benefitTexts.length - 1) * 10 + 12
      : 22
    : 0;

  return (
    <label
      className={cn(
        'relative rounded-lg bg-surface-3 border border-surface-1 p-3',
        'flex flex-col gap-2.5',
        isSelected && 'border-[3px] border-primary-600 p-2.5',
        isSelected && isNewBenifits && 'border-[3px] border-[#E2B65C] p-2.5',
        discountPercent > 0 && 'min-h-[130px]',
        tagLine && `${isSelected ? 'pt-[14px]' : 'pt-4'} mt-2.5`,
        planVariant === PlanVariant.SILVER && 'bg-gradient-silver',
        [PlanVariant.VIP, PlanVariant.VIP_USER].includes(planVariant) &&
          'bg-gradient-gold',
      )}
    >
      {tagLine && (
        <span
          className={cn(
            'absolute rounded-b bg-secondary-80',
            isSelected ? 'left-[18px] -top-3' : 'left-5 -top-[10px]',
            'px-3 py-0.5',
            'font-bold text-xs leading-body text-secondary-10',
            'before:content-[""] before:absolute before:-left-[3px] before:top-0 before:w-1.5 before:h-2.5 before:bg-secondary-100 before:-skew-x-[30deg] before:-z-[1]',
            'after:content-[""] after:absolute after:-right-[3px] after:top-0 after:w-1.5 after:h-2.5 after:bg-secondary-100 after:skew-x-[30deg] after:-z-[1]',
          )}
        >
          {tagLine}
        </span>
      )}
      <span className="flex items-center justify-between">
        <span className="flex flex-col items-start gap-px">
          <PlanBadge planVariant={planVariant} />
          <span className="flex items-center gap-1">
            <H3>{title}</H3>
            <PrimaryBody className="text-text-secondary">
              {/* बेचें */}
              {t('planCard.sell')}
            </PrimaryBody>
            {plan?.extraInfo?.isUnlimitedPlan && plan.days === 15 && (
              <H4 className="text-primary-500">
                ({plan.days} {c('days')})
              </H4>
            )}
            {benefits?.guarantee && (
              <H4 className="text-primary-500">
                {/* गारंटी के साथ */}
                {t('planCard.withGuarantee')}
              </H4>
            )}
            {benefits?.prime && (
              <H4 className="text-primary-500">
                {/* जल्दी */}
                {t('planCard.faster')}
              </H4>
            )}
          </span>
        </span>
        <span
          className={cn(
            'w-5 h-5 rounded-full border-2 border-grey-200',
            'flex justify-center items-center',
            isSelected && 'border-primary-600',
            isSelected && isNewBenifits && 'border-[#E2B65C]',
          )}
        >
          {isSelected && (
            <span
              className={cn(
                'w-3 h-3 rounded-full bg-primary-600',
                isSelected && isNewBenifits && 'bg-[#E2B65C]',
              )}
            ></span>
          )}
        </span>
        <input
          className="sr-only"
          type="radio"
          name="plan"
          checked={isSelected}
          onChange={onSelect}
        />
      </span>

      {/* single plan -money back guarantee text */}
      {isInX8X9Buckets &&
        plan &&
        plan?.extraInfo.isSinglePlan &&
        plan.price === 199 && (
          <span className="flex items-center gap-1 bg-gradient-to-r from-[#E7DDEE] to-white px-2 py-1 rounded-md w-fit">
            <EncircledTickIcon />
            <TertiaryBody className="text-secondary-100">
              24 घंटे में कॉल नहीं आई तो पैसा वापस
            </TertiaryBody>
          </span>
        )}

      {!(plan && plan?.extraInfo.isSinglePlan && plan.price === 199) && (
        <span className="w-full h-px bg-surface-1" />
      )}
      <span
        className="relative flex items-start justify-between"
        style={{ marginBottom: extraHeight + 'px' }}
      >
        <span className="flex flex-col">
          {benefitTexts.map((text, index) => (
            <span className="flex items-center gap-1" key={index}>
              {isEligibleForVIPUserPlansPage && isNewBenifits ? (
                <span className=" bg-[linear-gradient(102.08deg,_#E2B65C_24.15%,_#F8D851_61.31%,_#E2B65C_92.1%)] w-4 h-4 rounded-full flex justify-center items-center">
                  {benefitIcons[index]}
                </span>
              ) : (
                <TickIcon />
              )}
              <TertiaryBody className="text-text-secondary">
                {text}
              </TertiaryBody>
            </span>
          ))}
        </span>
        <H3
          className={cn(
            'absolute text-right right-0 text-primary-700',
            extraDiscountConfig
              ? '-bottom-7'
              : isEligibleForVIPUserPlansPage && isSubscription
              ? 'bottom-0'
              : 'top-0',
          )}
        >
          {priceText}
          {isEligibleForVIPUserPlansPage && isSubscription && (
            <p className="text-text-secondary font-bold text-xs">हर महीना</p>
          )}
          {!isEligibleForVIPUserPlansPage &&
          discountPercent &&
          discountPercent > 0 ? (
            <span className="flex items-center gap-1">
              <SecondaryBody className="line-through text-text-secondary text-base leading-body">
                ₹{getCommaSeparatedNumber(plan.price)}
              </SecondaryBody>
              <p className="font-bold text-xs leading-body text-status-moderation-text px-1 py-px bg-status-moderation-surface rounded">
                {showFirstPaymentDiscountText
                  ? t('planCard.firstPaymentDiscountPrefix')
                  : ''}
                {discountPercent} {t('planCard.discountSuffix')}
              </p>
              {extraDiscountConfig && (
                <p className="flex">
                  <span className="text-base leading-5 text-text-secondary ml-1">
                    +
                  </span>
                  <span className="ml-1">
                    {DiscountImage[extraDiscountConfig?.discountInterval]}
                  </span>
                </p>
              )}
            </span>
          ) : (
            extraDiscountConfig && (
              <p className="flex items-center gap-1">
                <SecondaryBody className="line-through text-text-secondary text-base leading-body">
                  ₹{getCommaSeparatedNumber(plan?.originalPrice ?? plan.price)}
                </SecondaryBody>
                <span>
                  {DiscountImage[extraDiscountConfig?.discountInterval]}
                </span>
              </p>
            )
          )}
        </H3>
      </span>
      {isSubscription && (
        <div className="flex justify-start items-center gap-1 px-2 bg-gradient-prime-surface py-0.5 rounded-md">
          <GoldenCalenderIcon />
          <TertiaryBody>
            हर महीने ऑटोपेमेंट। कभी भी{' '}
            <span
              className="font-bold text-primary-600"
              onClick={() => setShowAutoPayInfoPopup(true)}
            >
              कैंसल
            </span>{' '}
            करें
          </TertiaryBody>{' '}
        </div>
      )}
      <CancelAutoPayInfoPopup
        show={showAutoPayInfoPopup}
        setShow={setShowAutoPayInfoPopup}
      />
    </label>
  );
};

export default PlanCard;
