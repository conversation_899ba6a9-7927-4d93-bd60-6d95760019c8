import { i18n } from 'next-i18next';

import { getCookie } from 'lib/storage';
import { getCommaSeparatedNumber } from 'lib/utils';

import {
  Benefits,
  PlanVariant,
  SORTED_BENEFTIS,
  UiVariant,
  vipUserListingPlanPageBenifits,
  vipUserPrimePlanPageBenifits,
} from '../enums';

export const getBenefitTexts = (plan, source) => {
  const { extraInfo, days } = plan;
  const {
    maxUsage,
    benefits,
    everyMonthListing,
    isEligibleForVIPUserPlansPage,
    isNewBenifits,
  } = extraInfo;

  const isMultiPlan = maxUsage > 1;

  const benefitTexts = [];
  if (isEligibleForVIPUserPlansPage && isNewBenifits) {
    if (source === 'prime') {
      return vipUserPrimePlanPageBenifits(plan);
    }
    return vipUserListingPlanPageBenifits(plan);
  }

  if (everyMonthListing) {
    if (maxUsage > 1000) {
      benefitTexts.push(
        i18n.t('classified_plans:planCard3.everyMonthUnlimitedListing'),
      );
    } else {
      benefitTexts.push(
        i18n.t('classified_plans:planCard3.everyMonthListing', {
          count: maxUsage,
        }),
      );
    }
  } else if (isMultiPlan) {
    benefitTexts.push(
      i18n.t('classified_plans:planCard3.multiPlanSale', {
        days,
        count: maxUsage > 1000 ? '10+' : maxUsage,
      }),
    );
  }

  Object.keys(benefits || {})
    .sort((a, b) => {
      return SORTED_BENEFTIS.indexOf(a) - SORTED_BENEFTIS.indexOf(b);
    })
    .forEach((benefit) => {
      const value = benefits[benefit];
      if (!value) return;
      let text = '';
      switch (benefit) {
        case Benefits.CWA:
          text = isMultiPlan
            ? i18n.t('classified_plans:planCard3.callPerAnimal', { value })
            : i18n.t('classified_plans:planCard3.callsFromBuyers', { value });
          break;
        case Benefits.GUARANTEE:
          text = i18n.t('classified_plans:planCard3.moneyBackIfNotSold');
          break;
        case Benefits.RM:
          text = i18n.t('classified_plans:planCard3.helperFromAnimall');
          break;
        case Benefits.CDF:
          text = i18n.t('classified_plans:planCard3.freeWebsite');
          break;
        case Benefits.PRIME:
          text = i18n.t('classified_plans:planCard3.primeForHours', { value });
          break;
        case Benefits.SALE_DAYS:
          text = i18n.t('classified_plans:planCard3.soldInDays', { value });
          break;
        case Benefits.GURANTEED_SALE_DAYS:
          text = i18n.t('classified_plans:planCard3.moneyBackAfterDays', {
            value,
          });
          break;
        case Benefits.VIEWS:
          text = i18n.t('classified_plans:planCard3.moreThanBuyersSee', {
            value: getCommaSeparatedNumber(value),
          });
          break;
        case Benefits.BOOST_HOURS:
          text = i18n.t('classified_plans:planCard3.boostedForHours', {
            value,
          });
          break;
        case Benefits.MORE_BUYERS:
          text = i18n.t('classified_plans:planCard3.moreBuyers');
          break;
        case Benefits.INSTANT_LIVE:
          text = i18n.t('classified_plans:planCard3.instantLive');
          break;
        default:
          break;
      }
      benefitTexts.push(text);
    });

  return benefitTexts;
};

export const getTagline = ({ plan, uiVariant }) => {
  if (
    plan?.extraInfo?.isEligibleForVIPUserPlansPage &&
    plan?.extraInfo?.isNewBenifits
  ) {
    return 'आपके लिए सबसे लाभदायक';
  }
  switch (uiVariant) {
    case UiVariant.FIRST_PAYMENT:
      const isSinglePlan =
        plan.extraInfo.maxUsage === 1 && !plan.extraInfo.benefits?.guarantee;
      if (!isSinglePlan) return null;
      return i18n.t('classified_plans:planCard3.mostProfitable');
    case UiVariant.SECOND_PAYMENT:
      const isSinglePlanWithGuarantee =
        plan.extraInfo.maxUsage === 1 && plan.extraInfo.benefits?.guarantee;
      if (!isSinglePlanWithGuarantee) return null;
      return i18n.t('classified_plans:planCard3.moneyBackOnSecond');
    case UiVariant.ALL_PLANS:
      const isSilverPlan = plan.extraInfo.planVariant === PlanVariant.SILVER;
      if (!isSilverPlan) return null;
      return i18n.t('classified_plans:planCard3.sellMoreThan10');
    case UiVariant.UNLIMITED:
      const isGoldPlan = plan.extraInfo.planVariant === PlanVariant.VIP;
      if (!isGoldPlan) return null;
      return i18n.t('classified_plans:planCard3.sellFastEveryAnimal');
    default:
      return null;
  }
};

export const getPlanTitle = (plan) => {
  const { extraInfo } = plan;
  const { maxUsage: count, planVariant } = extraInfo;
  const prime = [PlanVariant.VIP, PlanVariant.VIP_USER].includes(planVariant)
    ? i18n.t('classified_plans:prime')
    : '';
  const isUnlimited = count > 1000;
  const countText = isUnlimited ? 'unlimited' : count;

  return i18n.t('classified_plans:planCard3.planTitle', {
    count: countText,
    prime,
  });
};

export const isDiscountTimerExpired = (blockedAt) => {
  const timerStartDate = getCookie('discountTimerStartDate');

  if (!timerStartDate) {
    return false;
  }

  const currentDate = new Date();
  const startDate = new Date(timerStartDate);

  if (new Date(blockedAt) > startDate) {
    return false;
  }

  const elapsedTime = (currentDate - startDate) / 1000;

  if (elapsedTime <= 30 * 60) {
    return false;
  }

  return true;
};

export const getExtraDiscountForBlockedUser = (blockedAt) => {
  const isDiscountExpired = isDiscountTimerExpired(blockedAt);
  if (isDiscountExpired || !blockedAt) {
    return null;
  }

  const plansPageVisits = Number(getCookie('plansPageVisits') || 0);
  if (!plansPageVisits) return null;

  if (plansPageVisits === 2) {
    return {
      discountPercentage: 10,
      discountInterval: 1,
      blockedAt,
    };
  } else if (plansPageVisits === 5) {
    return { discountPercentage: 20, discountInterval: 2, blockedAt };
  } else if (plansPageVisits === 10) {
    return { discountPercentage: 30, discountInterval: 3, blockedAt };
  }

  return null;
};

export const getDraftedPostBlockedDate = (post) => {
  let blockedDate = JSON.parse(
    localStorage.getItem('postIdBlockedDateMapping') || '{}',
  )[post._id];
  blockedDate = blockedDate || post?.uploadedOn || post?.publishedOn;
  return blockedDate;
};

export const getAudioFileUrl = (plan) => {
  const { extraInfo, days } = plan;
  const { maxUsage, benefits } = extraInfo;
  const { boostHours, guarantee } = benefits || {};
  switch (maxUsage) {
    case 1:
      if (guarantee) {
        return 'https://static-assets.animall.in/static/audio/plans/1_pashu_gurentee_plan.m4a';
      } else if (boostHours) {
        return 'https://static-assets.animall.in/static/audio/plans/prime_pashu_plan.m4a';
      } else {
        return 'https://static-assets.animall.in/static/audio/plans/1_pashu_plan.m4a';
      }

    case 3:
      return 'https://static-assets.animall.in/static/audio/plans/3_pashu_plan.m4a';
    case 10:
      return 'https://static-assets.animall.in/static/audio/plans/10_pashu_plan.m4a';
    default:
      if (days === 15) {
        return 'https://static-assets.animall.in/static/audio/plans/15days_plan.m4a';
      } else if (days === 30) {
        return 'https://static-assets.animall.in/static/audio/plans/30days_plan.m4a';
      }
      return null;
  }
};
