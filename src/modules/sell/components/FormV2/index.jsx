import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import { useTranslation } from 'next-i18next';

import useScrollBlock from 'hooks/useScrollBlock';

import axios from 'lib/axios';
import { logAmplitudeEvent } from 'lib/log-event';

import CustomerSupportCard from 'components/CustomerSupportCard';
import { InfoIcon } from 'components/PostCard/icons';
import { SecondaryBody, TertiaryBody } from 'components/ui/typography';

import LocationPopup from 'modules/buy/components/SkippedLocationFlow/LocationPopup';
import useFormData from 'modules/sell/hooks/useFormData';
import ReviewPopup from 'modules/sell/modals/ReviewPopup';
import { API_ENDPOINTS } from 'modules/sell/utils/constants';
import { getRequiredFields, validateForm } from 'modules/sell/utils/validation';
import { parseCookies } from 'nookies';
import { useGrowthUser } from 'providers/GrowthUserProvider';
import { MonetizationContext } from 'providers/Monetization';
import { useShowPaywall } from 'providers/PaywallProvider';

import useAppPaywall from '../../../app-subscription/hooks/useAppPaywall';
import MediaUploadWeb from '../MediaUploadWeb';
import AnimalType from './AnimalType';
import LocationInput from './LocationInput';
import MediaUpload from './MediaUpload';
import MilkDetails from './MilkDetails';
import OptionalDetails from './OptionalDetails';
import PashuType from './PashuType';
import Price from './Price';
import RatePredictorBanner from './RatePredictorBanner';
import SellInDays from './SellInDays';
import SubmitBar from './SubmitBar';

const DividerLine = () => <hr className="bg-surface-1 h-0.25 -mt-2 -mb-4" />;

const FormV2 = ({
  user,
  initialPost,
  onSubmitSuccess,
  isEdit = false,
  prefillAnimalType = null,
  prefillLactation = null,
  buyerCount,
  postsMetaData,
}) => {
  const { bucketId } = parseCookies();
  const { t } = useTranslation('sell');
  const errorClasses =
    'mt-3 font-bold font-rajdhani text-status-rejected-text border-l-2 border-status-rejected-text pl-2 pt-0';
  const [isUploading, setIsUploading] = useState(false);
  const postUuid = useRef(initialPost?.uuid);
  const isSubmitted = useRef(false);

  const { listing } = useContext(MonetizationContext);
  const isEligibleForVIPUserPlansPage = listing?.isEligibleForVIPUserPlansPage;
  const isEligibleForFreeListing =
    listing?.remainingLimit > 0 && listing?.status === 'UNPAID';

  const { showPaywall, hidePaywall } = useShowPaywall();
  const {
    isEligible: isAppPaywallEligible,
    isPremiumUser: isAppPaywallPremiumUser,
    listingRemainingLimit,
  } = useAppPaywall();

  const sectionRefs = {
    animalType: useRef(null),
    milkDetails: useRef(null),
    price: useRef(null),
    resources: useRef(null),
    location: useRef(null),
  };

  // Review popup state for edit page
  const [showReviewPopup, setShowReviewPopup] = useState(false);

  const [isWeb, setIsWeb] = useState(false);
  useEffect(() => {
    const { isAndroid } = parseCookies();
    setIsWeb(!isAndroid);
  }, []);

  const { formData, errors, updateFields, setErrors } = useFormData(
    initialPost,
    postUuid,
    `NEXT_SELL${isWeb ? '_WEB' : ''}`,
    !isEdit,
  );

  const [blockScroll, allowScroll] = useScrollBlock();
  useEffect(() => {
    // This is needed because `ReviewPopup` is directly mounted and doesn't follow bottom sheet data flow
    if (showReviewPopup) {
      blockScroll();
    } else {
      allowScroll();
    }
  }, [showReviewPopup, blockScroll, allowScroll]);

  // Skipped location flow
  const [showLocationPopup, setShowLocationPopup] = useState(false);

  const handleSubmit = async () => {
    const formErrors = validateForm(formData, t, 2);
    if (Object.keys(formErrors).length > 0) {
      console.log('[SELL] Form has errors, stopping submission');
      setErrors(formErrors);

      const firstErrorField = Object.keys(formErrors)[0];

      // Map error fields to their corresponding section refs
      const errorToSectionMap = {
        animalType: 'animalType',
        breed: 'animalType',
        lactation: 'milkDetails',
        currentMilk: 'milkDetails',
        highestMilk: 'milkDetails',
        price: 'price',
        resources: 'resources',
        location: 'location',
      };

      const sectionToScrollTo = errorToSectionMap[firstErrorField];
      if (sectionToScrollTo && sectionRefs[sectionToScrollTo]?.current) {
        sectionRefs[sectionToScrollTo].current.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
      }

      return;
    }

    if (
      !formData?.locationName ||
      formData?.location?.coordinates?.every((c) => !c)
    ) {
      // Location not present in the post
      setShowLocationPopup(true);
      return;
    }

    if (
      (isAppPaywallEligible && !isAppPaywallPremiumUser) ||
      (isAppPaywallPremiumUser &&
        listingRemainingLimit === 0 &&
        !isEligibleForVIPUserPlansPage)
    ) {
      showPaywall({
        source: 'SELL',
        onSuccess: async () => {
          if (isEdit) {
            setShowReviewPopup(true);
          } else {
            await submitPost();
          }
        },
      });
      return;
    }

    if (isEdit) {
      setShowReviewPopup(true);
    } else {
      await submitPost();
    }
  };

  const closeReviewPopup = () => {
    setShowReviewPopup(false);
  };

  const submitPost = useCallback(
    async (newPayload = {}) => {
      setIsUploading(true);

      try {
        if (isEdit) {
          closeReviewPopup();
        }
        if (initialPost) {
          const postIdBlockedDateMapping = JSON.parse(
            localStorage.getItem('postIdBlockedDateMapping') || '{}',
          );
          if (!postIdBlockedDateMapping[initialPost._id]) {
            postIdBlockedDateMapping[initialPost._id] =
              initialPost?.uploadedOn || initialPost?.publishedOn;
          }
          localStorage.setItem(
            'postIdBlockedDateMapping',
            JSON.stringify(postIdBlockedDateMapping),
          );
        }
        const payload = {
          ...formData,
          ...newPayload,
          saveOnExit: false,
        };

        const { data } = await axios.put(
          API_ENDPOINTS.UPDATE_POST(postUuid.current),
          payload,
        );

        isSubmitted.current = true;
        onSubmitSuccess(
          data.post,
          data?.isEligibleForFreePrime?.isEligible,
          data?.isEligibleForFreePrime?.activeSubscriptionId,
        );
      } catch (error) {
        if (isEdit) {
          logAmplitudeEvent('ERROR', 'EDITPRODUCTM', 'EDITPRODUCT', {
            STATUS: error.response?.data?.error || 'Unknown error',
          });
        } else {
          logAmplitudeEvent('ERROR', 'ADDPRODUCTM', 'ADDPRODUCT', {
            STATUS: error.response?.data?.error || 'Unknown error',
          });
        }
        console.error('Submission error:', error);
      } finally {
        setIsUploading(false);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [formData, isEdit, onSubmitSuccess],
  );

  const locationCallback = useCallback(
    async (parsedData) => {
      try {
        const { lat, lng, locationStr, adminLevel4, adminLevel5 } = parsedData;

        setShowLocationPopup(false);
        const userData = JSON.parse(sessionStorage.getItem('userData') || '{}');
        userData.locationLat = lat;
        userData.locationLng = lng;
        userData.locationState = adminLevel4;
        userData.district = adminLevel5;
        userData.locationName = locationStr;
        sessionStorage.setItem('userData', JSON.stringify(userData));
        localStorage.setItem('userData', JSON.stringify(userData));

        const newPayload = {
          locationName: locationStr,
          location: {
            type: 'Point',
            coordinates: [lng, lat],
          },
        };

        updateFields(newPayload);

        if (isEdit) {
          setShowReviewPopup(true);
        } else {
          await submitPost(newPayload);
        }
      } catch (error) {
        console.error(error);
      }
    },
    [isEdit, submitPost, updateFields],
  );

  const missingFields = useMemo(() => {
    const fieldToTransKey = {
      animalType: 'animalType', // कौन सा पशु
      breed: 'breed', // नस्ल
      lactation: 'lactation', // ब्यात
      currentMilk: 'currentMilk', // अभी का दूध
      highestMilk: 'highestMilk', // अधिकतम दूध
      price: 'price', // रेट
      resources: 'photoVideo', // फोटो/विडीयो
    };

    const fields = [];
    Object.keys(errors).forEach((field) => {
      if (errors[field]) {
        const translationKey = fieldToTransKey[field];
        fields.push(translationKey ? t(translationKey) : field);
      }
    });
    return fields;
  }, [errors, t]);

  const areRequiredFieldsFilled = useMemo(() => {
    if (formData.resources?.length === 0) return false;

    // remove highestMilk from required fields
    const requiredFields = getRequiredFields(formData.gender, 2);
    const hasMissing = requiredFields.some((f) =>
      [null, undefined, ''].includes(formData[f]),
    );

    return !hasMissing;
  }, [formData]);

  const { isGrowthUser } = useGrowthUser();

  return (
    <>
      {isEligibleForFreeListing && (
        <div className="mt-3 mx-4 p-2 bg-surface-3 rounded-md shadow-[0px_0px_2px_0px_#00000026] flex items-center gap-2">
          <InfoIcon />
          <TertiaryBody className="font-bold text-text-primary text-sm">
            <span className="text-primary-500">FREE</span> में पशु दर्ज करें
          </TertiaryBody>
        </div>
      )}
      <form
        onSubmit={(e) => e.preventDefault()}
        className="flex flex-col gap-8 mt-5 pb-32"
      >
        <div ref={sectionRefs.animalType}>
          <AnimalType
            animalType={formData.animalType}
            breed={formData.breed}
            gender={formData.gender}
            updateFields={updateFields}
            errors={errors}
            errorClasses={errorClasses}
            isEdit={isEdit}
            prefillAnimalType={prefillAnimalType}
            postsMetaData={postsMetaData}
          />
        </div>

        {formData.gender !== 'MALE' && (
          <div ref={sectionRefs.milkDetails}>
            <MilkDetails
              currentMilk={formData.currentMilk}
              highestMilk={formData.highestMilk}
              lactation={formData.lactation}
              gender={formData.gender}
              updateFields={updateFields}
              errors={errors}
              errorClasses={errorClasses}
              isEdit={isEdit}
              prefillLactation={prefillLactation}
            />
          </div>
        )}

        <div ref={sectionRefs.price}>
          <Price
            price={formData.price}
            isNegotiable={formData.isNegotiable}
            updateFields={updateFields}
            error={errors.price}
            errorClasses={errorClasses}
          />
          {!isGrowthUser && (
            <div className="px-4">
              <RatePredictorBanner postId={postUuid.current} />
            </div>
          )}
        </div>

        {!isGrowthUser && (
          <div className="px-4">
            <SellInDays
              value={formData?.timeToSell ? Number(formData?.timeToSell) : null}
              updateFields={updateFields}
            />
          </div>
        )}

        {!isGrowthUser && (
          <div className="px-4">
            <PashuType
              updateFields={updateFields}
              value={formData?.pashuType}
            />
          </div>
        )}

        <DividerLine />

        <div ref={sectionRefs.resources}>
          {!isWeb ? (
            <MediaUpload
              updateFields={updateFields}
              resources={formData.resources}
              error={errors.resources}
              errorClasses={errorClasses}
              eligibleForMilkingVideo={!isGrowthUser}
            />
          ) : (
            <MediaUploadWeb
              updateFields={updateFields}
              resources={formData.resources}
              error={errors.resources}
              errorClasses={errorClasses}
            />
          )}
        </div>

        <DividerLine />

        <OptionalDetails
          values={{
            hasDelivered: formData.hasDelivered,
            deliveredBefore: formData.deliveredBefore,
            isPregnant: formData.isPregnant,
            pregnancyMonth: formData.pregnancyMonth,
            calf: formData.calf,
            description: formData.description,
            currentMilk: formData.currentMilk,
            highestMilk: formData.highestMilk,
            isNegotiable: formData.isNegotiable,
          }}
          formData={formData}
          updateFields={updateFields}
          gender={formData.gender}
          errors={errors}
          isEdit={isEdit}
        />

        <DividerLine />

        <div ref={sectionRefs.location}>
          <LocationInput
            defaultLocation={{
              locationName: formData.locationName,
              lat: formData.location?.coordinates[1],
              lng: formData.location?.coordinates[0],
            }}
            updateFields={updateFields}
            error={errors.location}
            user={user}
            openLocationPopup={() => setShowLocationPopup(true)}
          />
        </div>
        <SubmitBar
          buyerCount={buyerCount}
          isUploading={isUploading}
          hasMissingFields={missingFields.length > 0}
          missingFieldsList={missingFields}
          onClick={handleSubmit}
        />
        {!isGrowthUser && isEligibleForFreeListing && (
          <>
            <div>
              <SecondaryBody className="text-center font-semibold text-grey-200 text-base mt-3">
                हर 30 दिन में 1 पशु फ्री में दर्ज़ कर पायगे
              </SecondaryBody>
            </div>
            <div className="mt-6">
              <CustomerSupportCard />
            </div>
          </>
        )}
      </form>

      {showReviewPopup && (
        // added this check for scaling down the post card - other approach will be to pass dynamic key ex: key={`review-popup-${showReviewPopup}`} not using dynamic key for performance reasons
        <ReviewPopup
          // key={`review-popup-${showReviewPopup}`}
          isOpen={showReviewPopup}
          onClose={closeReviewPopup}
          onOk={() => submitPost()} // `submitPost` takes `newPayload` as argument. If directly passed to `onOk`, it will pass the event object which will break the app.
          post={formData}
        />
      )}

      <LocationPopup
        isOpen={showLocationPopup}
        onClose={() => {
          logAmplitudeEvent('CLOSED', 'LOCATION', 'SELL');
          setShowLocationPopup(false);
        }}
        callBackFunc={locationCallback}
        description="पशु को आपके आस पास के ख़रीददारों को दिखाने के लिए लोकेशन दें"
        image="https://static-assets.animall.in/static/images/sell-location-hero.jpg"
        page={isEdit ? 'EDIT' : 'SELL'}
        saveInDb
      />
    </>
  );
};

export default FormV2;
