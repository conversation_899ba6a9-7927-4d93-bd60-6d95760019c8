import { useContext, useEffect, useMemo, useState } from 'react';

import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

import useScrollBlock from 'hooks/useScrollBlock';

import { logAmplitudeEvent, logFbEvent } from 'lib/log-event';
import { getCookie } from 'lib/storage';
import { isAndroid } from 'lib/utils';
import { getUserProfileUrl, isInBuckets } from 'lib/utils';

import { AnimallLoaderContext } from 'components/AnimallLoader';
import { RightChevron } from 'components/PostCard/icons';
import { H3, SecondaryBody } from 'components/ui/typography';

import FormV2 from './components/FormV2';

import { NEW_FREE_LISTING_PLAN_BUCKETS } from 'modules/classifieds-plans/enums';
import { UNLIMITED_POSTS_TRIAL_PLAN } from 'modules/home/<USER>';
import RepeatPrimePopup from 'modules/prime/PlanSelection/components/RepeatPrime';
import DiscountedPrimePopup from 'modules/prime/components/DiscountedPrimePopup';
import PrimePopup from 'modules/sell/modals/PrimePopup';
import SuccessPopup from 'modules/sell/modals/SuccessPopup';
import breedTypes from 'modules/sell/utils/breedTypes';
import { parseCookies } from 'nookies';
import { useGrowthUser } from 'providers/GrowthUserProvider';
import { MonetizationContext } from 'providers/Monetization';

import useAppPaywall from '../app-subscription/hooks/useAppPaywall';

const Sell = ({
  source,
  user,
  initialPost,
  isEdit = false,
  prefillAnimalType,
  prefillLactation,
  buyerCount,
  postsMetaData,
}) => {
  const { t } = useTranslation(['common', 'sell']);
  const router = useRouter();

  const { isPremiumUser: isAppPaywallPremiumUser } = useAppPaywall();

  const { preSelectedPlan } = router.query;
  const isGrowthNuRedirect = source === 'growth_nu_redirect';

  const [showPrimePopup, setShowPrimePopup] = useState(false);
  const [showRepeatPrimePopup, setShowRepeatPrimePopup] = useState(false);
  const [showDiscountedPrimePopup, setShowDiscountedPrimePopup] =
    useState(false);
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);

  const [isEligibleForFreePrime, setIsEligibleForFreePrime] = useState(false);
  const [activeSubscriptionId, setActiveSubscriptionId] = useState(null);

  const { listing, prime, cdf } = useContext(MonetizationContext);
  const isEligibleForVIPUserPlansPage = listing?.isEligibleForVIPUserPlansPage;
  const { discountedPrimePlan, bestPerformingPrimePost, isPrimeRepeatUser } =
    prime;
  const [_, allowScroll] = useScrollBlock();

  const [submittedPost, setSubmittedPost] = useState(null);

  const { openAnimallLoader } = useContext(AnimallLoaderContext);

  useEffect(() => {
    const { accessToken } = parseCookies();
    if (!accessToken) {
      const queryString = new URLSearchParams(
        window.location.search,
      ).toString();
      router.push(
        `/login?redirect=/next/sell${
          queryString ? `?${queryString}&source=sell` : ''
        }`,
      );
      return;
    }
  }, []);

  useEffect(() => {
    if (!isEdit) {
      logAmplitudeEvent('LANDED', 'ADDPRODUCTM', 'ADDPRODUCT', {
        SOURCE: source || 'NA',
      });
    } else {
      logAmplitudeEvent('LANDED', 'EDITPRODUCTM', 'EDITPRODUCT', {
        SOURCE: source || 'NA',
      });
      logFbEvent('LANDED_ADDPRODUCTM_ADDPRODUCT');
    }
  }, [source, isEdit]);

  const handleDashboardClick = (e) => {
    e.preventDefault();
    const sdUrl = `${getUserProfileUrl()}/my-posts?source=SELL_TABS`;
    router.push(sdUrl);
  };

  const redirectAfterUpload = () => {
    openAnimallLoader();
    window.location.replace('/buy?source=AddProduct');
  };

  const handleClosePrimePopup = () => {
    if (discountedPrimePlan) {
      setShowDiscountedPrimePopup(true);
      return;
    }
    if (isPrimeRepeatUser) {
      setShowRepeatPrimePopup(true);
      return;
    }
    setShowPrimePopup(false);
    redirectAfterUpload();
  };

  const alreadyHasPrime = useMemo(() => {
    const listingPlan = listing?.currentPlan || '';
    const isCdf = cdf?.premiumUser;
    const isPrimeMonthly = prime?.premiumUser;
    return (
      isPrimeMonthly ||
      isCdf ||
      ['MONTHLY_PRIME_1000', 'PRIME_2500'].includes(listingPlan)
    );
  }, [cdf, listing, prime]);

  const primeVideoUrl = useMemo(() => {
    const isVideoPresent = submittedPost?.resources?.find((r) => r?.isVideo);
    return isVideoPresent ? isVideoPresent.url : null;
  }, [submittedPost]);

  const onCloseSuccessPopup = () => {
    setShowSuccessPopup(false);

    if (alreadyHasPrime) {
      redirectAfterUpload();
    } else {
      openAnimallLoader();
      allowScroll();
      router.replace(
        `/prime/plan-selection?postId=${submittedPost._id}&source=SELL`,
      );
    }
  };

  const { isGrowthUser } = useGrowthUser();

  const onSubmitSuccess = (
    post,
    isEligibleForFreePrime = false,
    activeSubscriptionId = null,
  ) => {
    setSubmittedPost(post);

    if (
      typeof window !== 'undefined' &&
      window.fbq &&
      !isAndroid() &&
      !isEdit
    ) {
      window.fbq('trackCustom', 'UPLOADED_ADDPRODUCTM_ADDPRODUCT', {
        source: source || 'NA',
        isEdit: isEdit,
        postId: post?.id,
        lactation: post?.lactation,
        breed: post?.breed,
      });
    }

    if (post.state === 'DRAFTED') {
      if (!isEdit) {
        logAmplitudeEvent('DRAFTED', 'ADDPRODUCTM', 'ADDPRODUCT', {
          POST_ID: post._id,
          ANIMAL_TYPE: post.animalType,
          TYPE: post.paymentRequired ? 'PAYMENT REQUIRED' : 'MANUAL',
        });
      }

      if (!post.paymentRequired) {
        // Post submitted, but state is still DRAFTED
        // Might be due to some error in the backend, just return
        return;
      }

      const sourceParam = `&source=${isEdit ? 'EDIT' : 'SELL'}`;

      let url;
      if (isGrowthUser && !isEligibleForVIPUserPlansPage) {
        url = `/growth-plans?postId=${post._id}${sourceParam}`;
      } else {
        let preSelectedPlanUrl = '';
        if (preSelectedPlan) {
          preSelectedPlanUrl = `&preSelectedPlan=${preSelectedPlan}`;
        }
        url = `/plans?initialPlanType=listing&postId=${post._id}${sourceParam}${preSelectedPlanUrl}`;
      }
      router.replace(url);
      return;
    }

    const freePost =
      listing.remainingLimit > 0 &&
      listing.status === 'UNPAID' &&
      !isEdit &&
      !isGrowthUser;

    const listingPlan = listing?.currentPlan || '';

    if (
      post.state === 'QUEUED' ||
      (['PENDING', 'AUTO_PENDING'].includes(post.state) &&
        isGrowthUser &&
        !isAppPaywallPremiumUser)
    ) {
      // Post queued/pending, redirect to growth plans since this only happens to growth users (controlled via backend)

      logAmplitudeEvent('UPLOADED', 'ADDPRODUCTM', 'ADDPRODUCT', {
        POST_ID: post._id,
        ANIMAL_TYPE: post.animalType,
        GENDER: post.gender,
        BREED: post.breed || 'NA',
        PRICE: post.price,
        LACTATION: post.lactation || 'NA',
        PLAN: listingPlan || 'NA',
      });
      logFbEvent(
        'UPLOADED_ADDPRODUCTM_ADDPRODUCT',
        post._id,
        undefined,
        undefined,
        post.animalType,
      );

      let url = `/growth-plans?postId=${post._id}&source=SELL`;

      if (post.state === 'QUEUED' && freePost) {
        url = `/plans?postId=${post._id}&source=SELL&uiVariant=freeListing`;
      }

      router.replace(url);
      return;
    }

    // don't show success popup incase of free post
    if (!freePost) {
      setIsEligibleForFreePrime(isEligibleForFreePrime);
      setActiveSubscriptionId(activeSubscriptionId);

      // success popup
      setShowSuccessPopup(true);
    }

    // for 3rd listing in trial plan trigger fb event
    if (
      UNLIMITED_POSTS_TRIAL_PLAN.includes(listingPlan) &&
      listing?.planMaxUsage - listing?.remainingLimit === 2
    ) {
      logFbEvent(
        'USER_IS_CDF',
        post._id,
        undefined,
        undefined,
        post.animalType,
      );
    }

    if (isEdit) {
      logAmplitudeEvent('UPLOADED', 'EDITPRODUCTM', 'EDITPRODUCT', {
        PHONE: getCookie('userPhone') || 'NA',
        SELLER: getCookie('userName') || 'NA',
        VARIENT: 'NA',
        SOURCE_TYPE: window.location.href.includes('update-post-prompt')
          ? 'UPDATE-PROMPT'
          : 'NA',
        SOURCE: 'NORMAL',
        POST_ID: post._id,
      });
    } else {
      logAmplitudeEvent('UPLOADED', 'ADDPRODUCTM', 'ADDPRODUCT', {
        POST_ID: post._id,
        ANIMAL_TYPE: post.animalType,
        GENDER: post.gender,
        BREED: post.breed || 'NA',
        PRICE: post.price,
        LACTATION: post.lactation || 'NA',
        PLAN: listingPlan || 'NA',
      });
      logFbEvent(
        'UPLOADED_ADDPRODUCTM_ADDPRODUCT',
        post._id,
        undefined,
        undefined,
        post.animalType,
      );
    }

    if (freePost) {
      const url = `/plans?postId=${post._id}&source=SELL&uiVariant=freeListing`;
      if (isInBuckets(NEW_FREE_LISTING_PLAN_BUCKETS, getCookie('bucketId'))) {
        url += '&preSelectedPlan=3';
      }
      router.replace(url);
      return;
    }
  };

  return (
    <>
      <div className="max-w-3xl mx-auto bg-surface-0 min-h-screen pb-20">
        {/* Navbar */}
        <div className="bg-surface-0 z-10 pt-[84px]">
          {isGrowthNuRedirect ? (
            <div className="flex items-center max-w-7xl mx-auto px-4">
              <div className="flex items-center">
                <img
                  src="https://static-assets.animall.in/static/images/Home_page/buy_pashu.png"
                  alt="Cattle"
                  className="mr-3 h-16 w-auto transform scale-x-[-1]"
                />
                <div>
                  <H3>पशु बिकाऊ है?</H3>
                  <SecondaryBody className="text-text-secondary">
                    पशु की जानकारी दें, पशु बेचें
                  </SecondaryBody>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-between max-w-7xl mx-auto px-4">
              <div className="font-rajdhani font-bold text-2xl text-text-primary">
                {t('sellCattle')}
              </div>
              <div
                onClick={handleDashboardClick}
                className="font-bold text-lg text-primary-600 flex items-center gap-2"
              >
                {t('yourListedPashu', { ns: 'sell' })}
                <RightChevron w={12} h={16} fill="#14776F" />
              </div>
            </div>
          )}
        </div>

        {/* Main Content */}
        <main className="pt-0">
          <FormV2
            user={user}
            breedTypes={breedTypes}
            initialPost={initialPost}
            onSubmitSuccess={onSubmitSuccess}
            isEdit={isEdit}
            prefillAnimalType={prefillAnimalType}
            prefillLactation={prefillLactation}
            buyerCount={buyerCount}
            postsMetaData={postsMetaData}
          />
        </main>

        <SuccessPopup
          source="SELL"
          isOpen={showSuccessPopup}
          onClose={onCloseSuccessPopup}
        />

        <>
          <PrimePopup
            isOpen={showPrimePopup}
            onClose={handleClosePrimePopup}
            post={submittedPost}
            source="SELL"
            isPrime={alreadyHasPrime}
            primeVideoUrl={primeVideoUrl}
            isEligibleForFreePrime={isEligibleForFreePrime}
            activeSubscriptionId={activeSubscriptionId}
          />
          {showRepeatPrimePopup && (
            <RepeatPrimePopup
              post={
                bestPerformingPrimePost
                  ? { ...bestPerformingPrimePost, isPrime: true }
                  : null
              }
              onClose={redirectAfterUpload}
              onOk={() => {
                openAnimallLoader();
                router.replace(
                  `/prime/plan-selection?postId=${submittedPost?._id}&source=SELL`,
                );
              }}
              currentPostId={submittedPost?._id}
            />
          )}
          {showDiscountedPrimePopup && (
            <DiscountedPrimePopup
              postId={submittedPost?._id}
              onClose={redirectAfterUpload}
              source="SELL"
            />
          )}
        </>
      </div>
    </>
  );
};

export default Sell;
