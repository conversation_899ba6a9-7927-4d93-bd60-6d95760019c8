import { useMemo } from 'react';

import { useRouter } from 'next/router';

import { getCookie } from 'lib/storage';
import { isInBuckets } from 'lib/utils';

import { DotLottieReact } from '@lottiefiles/dotlottie-react';
import useAppPaywall from 'modules/app-subscription/hooks/useAppPaywall';
import { analyzePostPrice } from 'modules/vip-buyer/utils/price-analysis';
import { useGrowthUser } from 'providers/GrowthUserProvider';

const VipBuyerPriceChecker = ({ isVipBuyer, post }) => {
  const router = useRouter();
  const { isPremiumUser } = useAppPaywall();

  const { isGrowthUser } = useGrowthUser();
  const isSubscriptionSupported = true; // Allow subscriptions on both web and app
  const isEligibleForVIPBuyerV2 = useMemo(() => {
    return (
      (!isGrowthUser && isSubscriptionSupported) ||
      (isGrowthUser &&
        isPremiumUser &&
        isInBuckets([40, 59], getCookie('bucketId')))
    );
  }, [isGrowthUser, isPremiumUser, isSubscriptionSupported]);

  const openVipBuyerPopup = () => {
    if (!getCookie('accessToken')) {
      return router.push('/login');
    }
    if (
      window?.showVipBuyerPopupHandler &&
      typeof window?.showVipBuyerPopupHandler === 'function'
    ) {
      window.showVipBuyerPopupHandler('RateChecker');
    } else {
      router.push('/buyer-plans?source=postCard');
    }
  };

  const vipBuyerPriceChecker = useMemo(() => {
    if (!isVipBuyer) return null;

    return analyzePostPrice(post);
  }, [isVipBuyer, post]);

  if (
    !isEligibleForVIPBuyerV2 ||
    !['COW', 'BUFFALO'].includes(post?.animalType) ||
    post?.gender === 'MALE'
  ) {
    return null;
  }

  return isVipBuyer &&
    vipBuyerPriceChecker &&
    vipBuyerPriceChecker?.totalPriceStr ? (
    <div className="bg-[#D9FFFC] text-text-primary my-2 text-sm px-3 py-1.5 rounded-lg">
      इस पशु की कीमत ज़्यादा से ज़्यादा{' '}
      <span className="font-bold">{vipBuyerPriceChecker?.totalPriceStr}</span>{' '}
      होनी चाहिए
    </div>
  ) : (
    <div className="bg-[#D9FFFC] text-text-primary my-2 text-sm px-3 py-1.5 rounded-lg flex items-center justify-between">
      <div className="text-text-primary">जाने इस पशु का सही रेट</div>
      <div
        className="flex items-center gap-1 justify-center"
        onClick={openVipBuyerPopup}
      >
        <div className="font-medium text-primary-600">रेट जाने</div>
        <DotLottieReact
          src="https://lottie.host/2aa8be2f-2f09-4a48-b945-f66498876391/gRGx7ACjER.lottie"
          autoplay
          loop
          style={{
            width: '30px',
            height: ' 18px',
          }}
        />
      </div>
    </div>
  );
};

export default VipBuyerPriceChecker;
