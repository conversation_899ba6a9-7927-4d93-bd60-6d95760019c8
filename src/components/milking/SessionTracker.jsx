import { useContext, useState } from 'react';

import { useTranslation } from 'next-i18next';
import Image from 'next/image';
import { useRouter } from 'next/router';

import useAudioLoop from 'hooks/useAudioLoop';
import useMilkingSessions from 'hooks/useMilkingSessions';
import useTimer from 'hooks/useTimer';
import useVibrate from 'hooks/useVibrate';

import { axiosSsr } from 'lib/axios';
import { logAmplitudeEvent } from 'lib/log-event';
import { getCookie } from 'lib/storage';
import { isInBuckets } from 'lib/utils';

import LoanArrow from 'components/pashuLoan/loanArrow';
import { H2, H3, SmallBody } from 'components/ui/typography';

import useAppPaywall from 'modules/app-subscription/hooks/useAppPaywall';
import AppTrialPopupV2 from 'modules/home/<USER>/components/AppTrialPopupV2';
import MultiPlanAppTrialPopup from 'modules/home/<USER>/components/MultiPlanAppTrial';
import { useGrowthUser } from 'providers/GrowthUserProvider';
import { MonetizationContext } from 'providers/Monetization';

import LogModal from './LogModal';
import SessionTimer from './SessionTimer';

// Custom styles for the control animation
const customStyles = `
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  @keyframes bounce {
    0%, 33% {
      transform: translateY(0);
      animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    16.5% {
      transform: translateY(-25%);
      animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
    33%, 100% {
      transform: translateY(0);
    }
  }
  
  .animate-bounce-infinite {
    animation: bounce 1.5s infinite;
  }
`;

// Hero section component that manages session state and audio playback
const SessionTracker = ({ mutateMilkStats }) => {
  const { t } = useTranslation('milkingMusic');
  const { seconds, isRunning, start, pause, reset } = useTimer();
  const {
    currentTrack,
    isPlaying,
    play,
    pause: pauseAudio,
    stop: stopAudio,
  } = useAudioLoop();
  const { vibrate } = useVibrate();
  const { logSession } = useMilkingSessions();
  const { isPremiumUser } = useAppPaywall();
  const monetizationStats = useContext(MonetizationContext);
  const router = useRouter();

  const [showLogModal, setShowLogModal] = useState(false);
  const [isLogging, setIsLogging] = useState(false);
  const [error, setError] = useState(null);
  const [sessionStartTime, setSessionStartTime] = useState(null);
  const [showTimer, setShowTimer] = useState(false);
  const [showStopAnimation, setShowStopAnimation] = useState(false);
  const [showTrialPopup, setShowTrialPopup] = useState(false);

  const { isGrowthUser, isLoaded } = useGrowthUser();

  const handleStart = () => {
    const accessToken = getCookie('accessToken');
    const bucketId = parseInt(getCookie('bucketId'));

    if (!accessToken) {
      router.push('/login?redirect=/next/milking');
      return;
    }

    if (!isLoaded) {
      return;
    }

    if (!isPremiumUser && !(!isGrowthUser && isInBuckets([90, 99], bucketId))) {
      setShowTrialPopup(true);
      return;
    }

    start();
    play();
    vibrate(50);
    setSessionStartTime(new Date().toISOString());
    setShowTimer(true);

    logAmplitudeEvent('START', 'SESSION', 'MUSICMILKING', {
      TIME: seconds,
    });

    setTimeout(() => {
      const timerElement = document.querySelector('[data-timer]');
      if (timerElement) {
        timerElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }
    }, 100);
  };

  const handlePause = () => {
    pause();
    pauseAudio();
    vibrate([30, 50, 30]);

    // Log pause event with current time
    logAmplitudeEvent('PAUSE', 'SESSION', 'MUSICMILKING', {
      TIME: seconds,
    });
  };

  const handleStop = () => {
    // Log stop event with current time
    logAmplitudeEvent('STOP', 'SESSION', 'MUSICMILKING', {
      TIME: seconds,
    });

    // Show checkmark animation first
    setShowStopAnimation(true);
    vibrate(100);

    // After animation, stop audio and show modal
    setTimeout(() => {
      stopAudio();
      pause();
      setShowLogModal(true);
      setShowTimer(false);
      setShowStopAnimation(false);
    }, 1000);
  };

  const handleLogModalClose = () => {
    setShowLogModal(false);
    reset();
  };

  const handleLogModalConfirm = async (data) => {
    setIsLogging(true);
    setError(null);

    try {
      const endTime = new Date().toISOString();
      await logSession({
        startTime: sessionStartTime,
        endTime,
        duration: seconds,
        milkQty: data.milkQty,
        unitPrice: data.unitPrice,
      });

      // Log submit event with session details
      logAmplitudeEvent('SUBMIT', 'SESSION', 'MUSICMILKING', {
        TIME: seconds,
        MILK: data.milkQty,
        PRICE: data.unitPrice,
      });

      reset();
      setSessionStartTime(null);
      setShowTimer(false);

      // Update milk stats from DB before scrolling to analytics
      mutateMilkStats();

      // Scroll to analytics section after successful submission
      setTimeout(() => {
        const analyticsElement = document.querySelector('[data-analytics]');
        if (analyticsElement) {
          analyticsElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
          });
        } else {
          // Fallback to top if analytics element not found
          window.scrollTo({
            top: 0,
            behavior: 'smooth',
          });
        }
      }, 1500);
    } catch (err) {
      setError(err.message || 'Failed to save session');
    } finally {
      setIsLogging(false);
    }
  };

  const isX4_X5User = isInBuckets([40, 59], getCookie('bucketId'));

  return (
    <div className="text-center flex flex-col">
      <style dangerouslySetInnerHTML={{ __html: customStyles }} />

      {/* Header Section */}
      <div className="flex-none pt-8 pb-6 px-4">
        <H2
          className="text-3xl font-rajdhani font-bold leading-tight mb-1 p-0 rounded-lg"
          style={{
            color: '#B1F2ED',
            textShadow:
              '1px 1px 0px #0B433E, -1px -1px 0px #0B433E, 1px -1px 0px #0B433E, -1px 1px 0px #0B433E',
          }}
        >
          <span style={{ color: '#F8D851' }}>{t('hero.title')}</span>
        </H2>
        <H3 className="text-primary-600 mb-3">{t('hero.subtitle')}</H3>
      </div>

      {/* Main Content Area - Flex grow to fill remaining space */}
      <div className="flex-1 flex flex-col justify-between px-4">
        {/* Timer Section - Only show when session is active */}
        {showTimer ? (
          <div className="flex-1 flex flex-col justify-center" data-timer>
            {/* SessionTimer Component */}
            <div className="mb-8 pt-[100px]">
              <SessionTimer seconds={seconds} />
            </div>

            {/* Now Playing Indicator */}
            <div className="flex flex-col items-center mb-12">
              {/* Animated Waveform Icon */}
              <div className="flex items-center mb-3">
                <svg
                  className={'w-8 h-8 text-primary-600'}
                  viewBox="0 0 24 24"
                  fill="currentColor"
                >
                  <rect
                    x="2"
                    y="6"
                    width="2"
                    height="12"
                    className={isPlaying ? 'animate-bounce-infinite' : ''}
                    style={{ animationDelay: '0s' }}
                  />
                  <rect
                    x="6"
                    y="4"
                    width="2"
                    height="16"
                    className={isPlaying ? 'animate-bounce-infinite' : ''}
                    style={{ animationDelay: '0.1s' }}
                  />
                  <rect
                    x="10"
                    y="8"
                    width="2"
                    height="8"
                    className={isPlaying ? 'animate-bounce-infinite' : ''}
                    style={{ animationDelay: '0.2s' }}
                  />
                  <rect
                    x="14"
                    y="2"
                    width="2"
                    height="20"
                    className={isPlaying ? 'animate-bounce-infinite' : ''}
                    style={{ animationDelay: '0.3s' }}
                  />
                  <rect
                    x="18"
                    y="6"
                    width="2"
                    height="12"
                    className={isPlaying ? 'animate-bounce-infinite' : ''}
                    style={{ animationDelay: '0.4s' }}
                  />
                  <rect
                    x="22"
                    y="10"
                    width="2"
                    height="4"
                    className={isPlaying ? 'animate-bounce-infinite' : ''}
                    style={{ animationDelay: '0.5s' }}
                  />
                </svg>
              </div>

              {/* Current Track Name */}
              {currentTrack && (
                <p className="text-gray-600 text-lg font-medium">
                  {currentTrack.name}
                </p>
              )}
            </div>
          </div>
        ) : (
          <div className="flex-1 flex flex-col justify-center">
            {/* Spacer to center the main control when no timer */}
          </div>
        )}

        <div className="flex flex-col items-center py-8">
          <div className="flex flex-col items-center mb-8">
            <div
              className="relative w-40 h-40 cursor-pointer mb-6"
              onClick={isRunning ? handlePause : handleStart}
              style={{ minHeight: '44px', minWidth: '44px' }}
            >
              {/* Border circle - matches original .border */}
              <div
                className={`w-full h-full rounded-full border-8 transition-all duration-100 bg-primary-200 drop-shadow-[0_0_12px_rgba(34,207,193,0.6)] ${
                  isRunning
                    ? 'border-l-primary-400 border-r-primary-400 border-t-transparent border-b-transparent'
                    : 'border-primary-100'
                }`}
                style={{
                  animation: isRunning
                    ? 'spin 1.5s ease-in-out infinite'
                    : 'none',
                }}
              />

              {/* Play/Pause icon */}
              <div
                className="absolute transition-all duration-100 flex items-center justify-center"
                style={{
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  cursor: 'pointer',
                }}
              >
                {isRunning ? (
                  <svg width="55" height="80" viewBox="0 0 55 80" fill="none">
                    <rect
                      x="8"
                      y="12"
                      width="12"
                      height="56"
                      fill="#14776F"
                      rx="3"
                    />
                    <rect
                      x="35"
                      y="12"
                      width="12"
                      height="56"
                      fill="#14776F"
                      rx="3"
                    />
                  </svg>
                ) : (
                  <svg
                    width="55"
                    height="80"
                    viewBox="0 0 50 68"
                    fill="none"
                    className="ml-2"
                  >
                    <path
                      d="M0.333984 60.8537V7.52136C0.333984 1.99367 6.67428 -1.13273 11.0594 2.23263L46.6143 29.5189C50.1405 32.2251 50.081 37.5589 46.4952 40.1857L10.9404 66.2317C6.53647 69.4578 0.333984 66.3128 0.333984 60.8537Z"
                      fill="#14776F"
                    />
                  </svg>
                )}
              </div>
            </div>

            {/* Label below the control */}
            <H2 className="text-primary-600 font-bold text-3.5xl transition-all duration-300">
              {isRunning ? t('hero.pause') : t('hero.playMusic')}
            </H2>
          </div>

          {/* Stop Button - Only show when session is running or paused but timer is visible */}
          {showTimer && (
            <button
              onClick={handleStop}
              className="w-20 h-20 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center shadow-lg transition-all duration-200 transform mb-6"
              style={{ minHeight: '44px', minWidth: '44px' }}
            >
              <div className="flex flex-col items-center">
                {showStopAnimation ? (
                  // Animated Checkmark
                  <svg
                    className="w-10 h-10 text-white animate-bounce transform scale-110"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={3}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                ) : (
                  <>
                    <svg
                      className="w-10 h-10 text-white mb-1"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path d="M6 6h12v12H6z" />
                    </svg>
                    <span className="text-white font-bold text-sm">
                      {t('hero.stop')}
                    </span>
                  </>
                )}
              </div>
            </button>
          )}
        </div>

        {/* Music steps */}

        <div className="w-auto h-22 flex items-center justify-between">
          {/* Card 1 */}
          <div className="w-17 h-22 flex flex-col items-center justify-center gap-1">
            <Image
              src="https://static-assets.animall.in/static/images/milk-music/song.png"
              alt="Music Phone"
              width={50}
              height={50}
              className="object-contain"
            />
            <SmallBody className="text-xs text-center text-text-primary font-bold leading-tight whitespace-pre-line">
              {t('MilkBoostSteps.firstStep')}
            </SmallBody>
          </div>

          {/* Arrow 1 */}
          <div className="flex items-center justify-center h-22 px-1">
            <LoanArrow height={20} width={24} />
          </div>

          {/* Card 2 */}
          <div className="w-17 h-22 flex flex-col items-center justify-center gap-1">
            <Image
              src="https://static-assets.animall.in/static/images/milk-music/musiccow.png"
              alt="Music Cow"
              width={50}
              height={50}
              className="object-contain"
            />
            <SmallBody className="text-xs text-center text-text-primary font-bold leading-tight whitespace-pre-line">
              {t('MilkBoostSteps.secondStep')}
            </SmallBody>
          </div>

          {/* Arrow 2 */}
          <div className="flex items-center justify-center h-22 px-1">
            <LoanArrow height={20} width={24} />
          </div>

          {/* Card 3 */}
          <div className="w-22 h-22 flex flex-col items-center justify-center gap-1">
            <Image
              src="https://static-assets.animall.in/static/images/milk-music/milkbucket.png"
              alt="Milk bucket"
              width={50}
              height={50}
              className="object-contain"
            />
            <SmallBody className="text-xs text-center text-text-primary font-bold leading-tight whitespace-pre-line">
              {t('MilkBoostSteps.thirdStep')}
            </SmallBody>
          </div>
        </div>
      </div>

      {/* Log Modal */}
      <LogModal
        isOpen={showLogModal}
        onClose={handleLogModalClose}
        onConfirm={handleLogModalConfirm}
        isLogging={isLogging}
        error={error}
      />

      {isX4_X5User ? (
        <MultiPlanAppTrialPopup
          show={showTrialPopup}
          source="MILKING_TRACKER"
          onClose={() => setShowTrialPopup(false)}
          onSuccess={() => {
            setShowTrialPopup(false);
          }}
        />
      ) : (
        <AppTrialPopupV2
          show={showTrialPopup}
          source="MILKING_TRACKER"
          onClose={() => setShowTrialPopup(false)}
          onSuccess={() => {
            setShowTrialPopup(false);
          }}
        />
      )}
    </div>
  );
};

export default SessionTracker;
