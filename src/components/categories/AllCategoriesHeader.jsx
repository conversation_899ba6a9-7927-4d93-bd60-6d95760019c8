import { useState } from 'react';

import { useRouter } from 'next/router';

import {
  DEFAULT_DOCTORS,
  doctorCall,
  trackDoctorCallCategories,
} from 'lib/doctor-call';

import {
  PrimaryBody,
  SecondaryBody,
  SmallBody,
} from 'components/ui/typography';

import useAppPaywall from 'modules/app-subscription/hooks/useAppPaywall';
import AppTrialPopup from 'modules/home/<USER>/components/AppTrialPopup';
import { FEATURES } from 'modules/home/<USER>/constant';

export default function AllCategoryHeader() {
  const [submitting, setSubmitting] = useState(false);
  const { isPremiumUser } = useAppPaywall();
  const [showPaywall, setShowPaywall] = useState(false);
  const openPaywall = () => {
    setShowPaywall(true);
  };

  const handleBannerDoctorClick = async () => {
    if (!isPremiumUser) {
      openPaywall();
      return;
    }
    if (submitting) return;
    setSubmitting(true);
    try {
      const res = await doctor<PERSON>all({
        doctors: DEFAULT_DOCTORS,
        source: 'vet_intake_doctor_banner',
        answers: {
          problem_category: 'ALL_CATEGORIES_BANNER',
          question: 'User clicked banner on Categories page',
        },
      });

      trackDoctorCallCategories({
        source: 'Top Banner',
        PHONE: res.PHONE,
        DOCTOR: res.DOCTOR,
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div
      className="relative w-full max-w-md mx-auto h-[130px] overflow-visible"
      style={{
        background:
          'linear-gradient(180deg, #B1F2ED 0%, #85DDD7 76.36%, #78D7D0 100%)',
      }}
    >
      {/* Row layout: Left = image (fixed), Right = text */}
      <div className="h-full flex items-center gap-3 px-4">
        {/* LEFT: Doctors image (fixed width), bottom-aligned */}
        <div
          className="h-full flex items-end shrink-0 overflow-hidden"
          style={{ width: 130 }}
        >
          <img
            src="https://static-assets.animall.in/static/images/category/doctors-groups.png"
            alt="Doctors Group"
            className="w-[130px] h-[175.57px] object-contain select-none pointer-events-none -mb-10 "
          />
        </div>

        {/* RIGHT: Text + CTA (fills remaining space) */}
        <div className="flex-1 h-full flex flex-col justify-end pb-4 ml-9">
          <SecondaryBody className="text-sm sm:text-base font-mukta font-extrabold leading-tight text-primary-700 pb-1">
            पशु देखभाल में
          </SecondaryBody>

          <PrimaryBody className="text-xl sm:text-[22px] leading-[1.1] font-extrabold font-mukta">
            <span className="text-primary-600">डॉक्टर</span>{' '}
            <span className="text-text-primary">से</span>{' '}
            <span className="text-primary-600">सीधी बात</span>
          </PrimaryBody>

          <button
            onClick={handleBannerDoctorClick}
            disabled={submitting}
            className="mt-1 inline-flex w-fit self-start items-center gap-1
                       rounded-3xl px-3 py-1.5
                       text-surface-3 text-xs font-bold
                       shadow-[0px_0px_10px_0px_rgba(0,0,0,0.05)]
                       bg-gradient-to-b from-[#2F80ED] to-[#0385DC]"
            type="button"
          >
            <PhoneIcon className="h-3 w-3" />
            <SmallBody className="font-bold text-surface-3 text-xs whitespace-nowrap">
              {submitting ? 'कॉल हो रही है…' : 'अभी कॉल करें'}
            </SmallBody>
          </button>
        </div>
      </div>
      <AppTrialPopup
        show={showPaywall}
        setShow={setShowPaywall}
        featureName={FEATURES.TOP_CATEGORIES}
      />
    </div>
  );
}
function PhoneIcon({ className = 'h-3 w-3' }) {
  return (
    <svg
      width="10"
      height="10"
      viewBox="0 0 10 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M5.41981 3.3867C6.1141 3.3867 6.67694 3.94954 6.67694 4.64383C6.67694 4.87526 6.86455 5.06287 7.09598 5.06287C7.32741 5.06287 7.51502 4.87526 7.51502 4.64383C7.51502 3.48668 6.57696 2.54862 5.41981 2.54862C5.18838 2.54862 5.00077 2.73623 5.00077 2.96766C5.00077 3.19909 5.18838 3.3867 5.41981 3.3867Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.78492 6.42857C8.92574 6.46124 9.03977 6.56419 9.08663 6.70094C9.12199 6.80041 9.14863 6.90276 9.16624 7.00685C9.18311 7.11216 9.19152 7.21866 9.19139 7.32532C9.19139 7.83655 8.9883 8.32684 8.62681 8.68833C8.26531 9.04983 7.77502 9.25291 7.26379 9.25291C3.70168 9.24829 0.815164 6.36178 0.810547 2.79966C0.810547 1.73508 1.67356 0.87207 2.73814 0.87207C2.84681 0.87538 2.95489 0.889417 3.0608 0.913975C3.1636 0.929186 3.26465 0.95445 3.36251 0.989402C3.49927 1.03626 3.60222 1.15029 3.63489 1.29111L4.20898 3.77603C4.24042 3.9147 4.1994 4.05985 4.10003 4.16155C4.04862 4.22087 4.04199 4.22421 3.59407 4.44983C3.56737 4.46327 3.53911 4.47751 3.50918 4.49259C3.92193 5.39808 4.64612 6.12525 5.54991 6.54171C5.82229 6.0221 5.82648 6.01791 5.88515 5.96343C5.98684 5.86406 6.132 5.82304 6.27066 5.85448L8.78492 6.42857ZM8.3531 4.64345C8.3531 3.02343 7.03982 1.71015 5.41981 1.71015C5.18838 1.71015 5.00077 1.52254 5.00077 1.29111C5.00077 1.05968 5.18838 0.87207 5.41981 0.87207C7.50268 0.87207 9.19119 2.56057 9.19119 4.64345C9.19119 4.87488 9.00358 5.06249 8.77215 5.06249C8.54072 5.06249 8.3531 4.87488 8.3531 4.64345Z"
        fill="white"
      />
    </svg>
  );
}
