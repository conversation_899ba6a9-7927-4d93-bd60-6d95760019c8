// components/CategoryHeader.jsx
import { SecondaryBody } from 'components/ui/typography';

export default function CategoryHeader({ title = '', onBack }) {
  return (
    <div className="w-[360px] h-[56px] flex items-center px-4 py-1.5 bg-white">
      {/* Back Button */}
      <button onClick={onBack} className="flex items-center">
        <BackButton />
      </button>

      {/* Title */}
      <SecondaryBody className="ml-2 text-base font-medium text-text-primary">
        {title}
      </SecondaryBody>
    </div>
  );
}

function BackButton() {
  return (
    <svg
      width="16"
      height="24"
      viewBox="0 0 24 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M4.20874 6.97146L23.7282 6.97146L23.7282 9.02854L4.20874 9.02854L9.72584 14.5456L8.27148 16L0.271484 8L8.27148 1.55766e-06L9.72584 1.45436L4.20874 6.97146Z"
        fill="#2E3C4D"
      />
    </svg>
  );
}
