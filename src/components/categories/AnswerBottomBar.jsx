import { useState } from 'react';

import { useRouter } from 'next/router';

import {
  DEFAULT_DOCTORS,
  doctorCall,
  trackDoctorCallCategories,
} from 'lib/doctor-call';
import { logAmplitudeEvent } from 'lib/log-event';

import {
  SecondaryBody,
  SmallBody,
  TertiaryBody,
} from 'components/ui/typography';

import { useGrowthUser } from 'providers/GrowthUserProvider';

export default function AnswerBottomBar({
  onRobotClick,
  onDoctorClick,
  qa,
  category,
}) {
  const { isGrowthUser, isLoaded } = useGrowthUser();
  const router = useRouter();
  const [doctorBusy, setDoctorBusy] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const doctorShow = isLoaded && isGrowthUser;

  const handleRobotClick = () => {
    logAmplitudeEvent('CLICKED', 'SOLUTION', 'ROBOT', {
      SOURCE: router.query?.source || 'NA',

      QUESTION: qa?.q || 'NA',
      ANSWER: qa?.a || 'NA',
      CATEGORY: category || 'NA',
    });

    router.push({
      pathname: '/chat/generic-robot',
      query: {
        source: 'categories_question',
        question: qa?.q ?? '',
      },
    });

    if (onRobotClick) onRobotClick();
  };

  const handleDoctorClick = async (e) => {
    e.preventDefault();
    if (submitting) return;

    if (onDoctorClick) return onDoctorClick();

    setSubmitting(true);
    try {
      const res = await doctorCall({
        doctors: DEFAULT_DOCTORS,
        source: 'vet_intake_doctor',
        answers: {
          problem_category: category || 'NA',
          question: qa?.q || 'NA',
        },
      });

      trackDoctorCallCategories({
        source: router.query?.source || 'Solution Page',
        PHONE: res.PHONE,
        DOCTOR: res.DOCTOR,
      });

      if (res?.posted) setDoctorBusy(true);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div
      className={`fixed inset-x-0 bottom-0 z-50 ${
        doctorBusy ? 'bg-primary-25' : 'bg-surface-3'
      }`}
    >
      <div className="mx-auto w-full px-4 pt-3 pb-3">
        <>
          <TertiaryBody className="mb-2 text-center">
            और जानने के लिए रोबोट या सीधा हमारी टीम से बात करें
          </TertiaryBody>

          <div className="flex w-full items-center justify-between gap-2">
            <button
              onClick={handleRobotClick}
              className={`flex h-14 ${
                doctorShow ? 'min-w-[132px]' : 'w-full'
              } items-center justify-center gap-1.5 rounded-lg border-2 border-primary-600 bg-white px-4`}
            >
              <RobotMsgIcon className="h-3.5 w-4.5 flex-shrink-0" />
              <SmallBody className="text-primary-600 font-bold text-lg whitespace-nowrap">
                रोबोट से जानें
              </SmallBody>
            </button>

            {doctorShow && (
              <button
                onClick={handleDoctorClick}
                disabled={submitting}
                className="flex h-14 w-[186px] flex-none items-center justify-center gap-1.5 rounded-lg px-4 text-white 
               bg-gradient-to-b from-[#2F80ED] to-[#0385DC] shadow-md"
              >
                <PhoneIcon className="h-4 w-4" />
                <SmallBody className="text-surface-3 font-bold text-lg whitespace-nowrap">
                  {submitting ? 'कॉल हो रही है…' : 'डॉक्टर को कॉल करें'}
                </SmallBody>
              </button>
            )}
          </div>
        </>
      </div>
    </div>
  );
}

function PhoneIcon({ className = 'h-5 w-5' }) {
  return (
    <svg
      className={className}
      width="17"
      height="18"
      viewBox="0 0 17 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.33767 5.64841C10.7262 5.64841 11.8519 6.77407 11.8519 8.16266C11.8519 8.62552 12.2271 9.00074 12.69 9.00074C13.1529 9.00074 13.5281 8.62552 13.5281 8.16266C13.5281 5.84835 11.652 3.97224 9.33767 3.97224C8.8748 3.97224 8.49958 4.34746 8.49958 4.81032C8.49958 5.27318 8.8748 5.64841 9.33767 5.64841Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.0679 11.7321C16.3495 11.7975 16.5776 12.0034 16.6713 12.2769C16.742 12.4758 16.7953 12.6805 16.8305 12.8887C16.8643 13.0993 16.8811 13.3123 16.8808 13.5256C16.8808 14.5481 16.4747 15.5287 15.7517 16.2517C15.0287 16.9747 14.0481 17.3808 13.0256 17.3808C5.9014 17.3716 0.128375 11.5986 0.119141 4.47433C0.119141 2.34517 1.84517 0.619141 3.97433 0.619141C4.19167 0.625761 4.40783 0.653833 4.61965 0.702949C4.82524 0.733372 5.02735 0.783899 5.22307 0.853804C5.49658 0.947515 5.70248 1.17559 5.76783 1.45722L6.916 6.42706C6.97889 6.7044 6.89684 6.9947 6.6981 7.1981C6.59528 7.31674 6.58203 7.32342 5.68619 7.77465C5.63279 7.80155 5.57627 7.83002 5.5164 7.86019C6.34191 9.67117 7.79029 11.1255 9.59787 11.9584C10.1426 10.9192 10.151 10.9108 10.2683 10.8019C10.4717 10.6031 10.762 10.5211 11.0394 10.584L16.0679 11.7321ZM15.2043 8.1619C15.2043 4.92187 12.5777 2.29531 9.33767 2.29531C8.87481 2.29531 8.49958 1.92009 8.49958 1.45723C8.49958 0.994364 8.87481 0.619141 9.33767 0.619141C13.5034 0.619141 16.8804 3.99615 16.8804 8.1619C16.8804 8.62476 16.5052 8.99998 16.0423 8.99998C15.5795 8.99998 15.2043 8.62476 15.2043 8.1619Z"
        fill="white"
      />
    </svg>
  );
}

function RobotMsgIcon({ className = 'h-3 w-3' }) {
  return (
    <svg
      className={className}
      viewBox="0 0 15 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.1 0H1.9C1.13 0 0.507 0.63 0.507 1.4L0.5 14L3.3 11.2H13.1C13.87 11.2 14.5 10.57 14.5 9.8V1.4C14.5 0.63 13.87 0 13.1 0ZM4 4.9H11C11.385 4.9 11.7 5.215 11.7 5.6C11.7 5.985 11.385 6.3 11 6.3H4C3.615 6.3 3.3 5.985 3.3 5.6C3.3 5.215 3.615 4.9 4 4.9ZM8.2 8.4H4C3.615 8.4 3.3 8.085 3.3 7.7C3.3 7.315 3.615 7 4 7H8.2C8.585 7 8.9 7.315 8.9 7.7C8.9 8.085 8.585 8.4 8.2 8.4ZM11 4.2H4C3.615 4.2 3.3 3.885 3.3 3.5C3.3 3.115 3.615 2.8 4 2.8H11C11.385 2.8 11.7 3.115 11.7 3.5C11.7 3.885 11.385 4.2 11 4.2Z"
        fill="#14776F"
      />
    </svg>
  );
}
