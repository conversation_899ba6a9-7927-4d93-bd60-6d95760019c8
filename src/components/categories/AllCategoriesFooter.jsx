import { useState } from 'react';

import {
  DEFAULT_DOCTORS,
  doctor<PERSON>all,
  trackDoctorCallCategories,
} from 'lib/doctor-call';

import { H4, SmallBody } from 'components/ui/typography';

export default function AllCategoryFooter({}) {
  const [submitting, setSubmitting] = useState(false);

  const handleCallClick = async (e) => {
    e.preventDefault();
    if (submitting) return;

    setSubmitting(true);
    try {
      // make the vetted intake + start the call
      const res = await doctor<PERSON><PERSON>({
        doctors: DEFAULT_DOCTORS,
        source: 'vet_intake_footer',
        answers: {
          problem_category: 'ALL_CATEGORIES_FOOTER',
          question: 'User clicked on ALL_CATEGORIES',
        },
      });

      // track amplitude with dynamic SOURCE and doctor info
      trackDoctorCallCategories({
        source: 'Footer',

        PHONE: res.PHONE,
        DOCTOR: res.DOCTOR,
      });
    } finally {
      setSubmitting(false);
    }
  };
  return (
    <div className="w-full max-w-md mx-auto h-[120px] rounded-2xl flex items-center justify-between px-5 bg-[#B1F2ED] mt-4">
      {/* Left Text Section */}
      <div className="flex flex-col">
        <H4 className="text-base sm:text-lg font-bold text-text-primary mb-1">
          जो ढूँढ रहे थे नहीं मिला?
        </H4>
        <SmallBody className="text-[11px] sm:text-xs font-bold text-text-secondary mb-2">
          कोई बात नहीं, सीधा डॉक्टर से बात करें
        </SmallBody>

        <button
          className="flex w-[113px] h-[29px] items-center justify-start gap-1 px-2 py-[3px] rounded-full text-white text-xs font-bold shadow-[0px_0px_10px_rgba(0,0,0,0.05)] bg-gradient-to-b from-[#2F80ED] to-[#0385DC] leading-none"
          onClick={handleCallClick}
          disabled={submitting}
          type="button"
        >
          <PhoneIcon className="h-2.5 w-2.5 flex-shrink-0" />
          <span>डॉक्टर से बात करें</span>
        </button>
      </div>

      {/* Right Doctor Images */}
      <img
        src="https://static-assets.animall.in/static/images/category/doctors-group.png"
        alt="Doctors"
        className="h-[66px] w-[96px] object-contain"
      />
    </div>
  );
}

function PhoneIcon({ className = 'h-5 w-5' }) {
  return (
    <svg
      className={className}
      width="17"
      height="18"
      viewBox="0 0 17 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M9.33767 5.64841C10.7262 5.64841 11.8519 6.77407 11.8519 8.16266C11.8519 8.62552 12.2271 9.00074 12.69 9.00074C13.1529 9.00074 13.5281 8.62552 13.5281 8.16266C13.5281 5.84835 11.652 3.97224 9.33767 3.97224C8.8748 3.97224 8.49958 4.34746 8.49958 4.81032C8.49958 5.27318 8.8748 5.64841 9.33767 5.64841Z"
        fill="white"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M16.0679 11.7321C16.3495 11.7975 16.5776 12.0034 16.6713 12.2769C16.742 12.4758 16.7953 12.6805 16.8305 12.8887C16.8643 13.0993 16.8811 13.3123 16.8808 13.5256C16.8808 14.5481 16.4747 15.5287 15.7517 16.2517C15.0287 16.9747 14.0481 17.3808 13.0256 17.3808C5.9014 17.3716 0.128375 11.5986 0.119141 4.47433C0.119141 2.34517 1.84517 0.619141 3.97433 0.619141C4.19167 0.625761 4.40783 0.653833 4.61965 0.702949C4.82524 0.733372 5.02735 0.783899 5.22307 0.853804C5.49658 0.947515 5.70248 1.17559 5.76783 1.45722L6.916 6.42706C6.97889 6.7044 6.89684 6.9947 6.6981 7.1981C6.59528 7.31674 6.58203 7.32342 5.68619 7.77465C5.63279 7.80155 5.57627 7.83002 5.5164 7.86019C6.34191 9.67117 7.79029 11.1255 9.59787 11.9584C10.1426 10.9192 10.151 10.9108 10.2683 10.8019C10.4717 10.6031 10.762 10.5211 11.0394 10.584L16.0679 11.7321ZM15.2043 8.1619C15.2043 4.92187 12.5777 2.29531 9.33767 2.29531C8.87481 2.29531 8.49958 1.92009 8.49958 1.45723C8.49958 0.994364 8.87481 0.619141 9.33767 0.619141C13.5034 0.619141 16.8804 3.99615 16.8804 8.1619C16.8804 8.62476 16.5052 8.99998 16.0423 8.99998C15.5795 8.99998 15.2043 8.62476 15.2043 8.1619Z"
        fill="white"
      />
    </svg>
  );
}
