import { useEffect, useState } from 'react';

import { useRouter } from 'next/router';

import { logAmplitudeEvent } from 'lib/log-event';

import { SecondaryBody, SmallBody } from 'components/ui/typography';

import categoriesData from 'data/categoriesqn/categoriesqn';
import useAppPaywall from 'modules/app-subscription/hooks/useAppPaywall';
import ProtectedSection from 'modules/home/<USER>/components/ProtectedSection';
import { FEATURES } from 'modules/home/<USER>/constant';

export default function TopCategories() {
  const router = useRouter();
  const [randomCategory, setRandomCategory] = useState(null);
  const [randomQuestions, setRandomQuestions] = useState([]);
  const { isPremiumUser } = useAppPaywall();

  useEffect(() => {
    // 1️⃣ Pick a random category
    const randomCat =
      categoriesData[Math.floor(Math.random() * categoriesData.length)];

    logAmplitudeEvent('VIEWED', 'TOPCATEGORIES', 'HOMESCREEN', {
      SOURCE: router.query?.source || 'NA',
    });

    // 2️⃣ Shuffle questions and take 3
    const shuffled = [...randomCat.questions].sort(() => 0.5 - Math.random());
    const picked = shuffled.slice(0, 3);

    setRandomCategory(randomCat);
    setRandomQuestions(picked);
  }, []);

  if (!randomCategory) return null;

  const handleCardClick = (idx) => {
    router.push({
      pathname: `/categories/${randomCategory.categoryEn}/${idx}`,
      query: { source: 'homeWidget' },
    });
  };

  const handleSeeMore = () => {
    router.push({
      pathname: '/categories',
      query: {
        source: 'homeWidget',
      },
    });
  };

  return (
    <div className="sm:max-w-md sm:mx-auto mx-4 mb-1 flex flex-col gap-1 rounded-2xl">
      <div
        className="relative shadow-md flex flex-col gap-2 px-3 pt-3 pb-1 bg-white w-full
           rounded-xl overflow-hidden
           [background:linear-gradient(197.46deg,#FFF7DF_13.81%,#FFFFFF_88.04%)]
           [box-shadow:0px_0px_6px_0px_rgba(0,0,0,0.1)]"
      >
        {isPremiumUser && (
          <div
            className="absolute top-0 right-0 flex items-center
                 pt-1 pr-[6px] pb-[1px] pl-2
                 rounded-bl-xl  
                 [background:linear-gradient(96.48deg,#F8D851_3.88%,#E7B012_21.19%,#E2B65C_57.33%,#E6AA3B_97.72%)]
                 shadow-sm "
          >
            <SmallBody className="font-mukta font-bold text-[12px] leading-[140%] text-black">
              बिल्कुल फ्री
            </SmallBody>
          </div>
        )}
        {/* Pashu dekhbhal me doctor se baat karein */}

        <div className="flex items-center gap-2">
          <PhoneCall />
          <SecondaryBody className="font-medium text-text-primary text-base">
            पशुओं से जुड़े हर सवाल का जबाव
          </SecondaryBody>
        </div>

        <div className="flex justify-between gap-2">
          {randomQuestions.map((item, idx) => {
            const imageUrl = `https://static-assets.animall.in/static/images/category/${encodeURIComponent(
              item.q,
            )}_v2.png`;

            return (
              <ProtectedSection
                key={idx}
                shouldAuthenticate={!isPremiumUser}
                source="HOMESCREEN"
                showPopupOnLock={true}
                featureName={FEATURES.TOP_CATEGORIES}
              >
                <div
                  key={idx}
                  onClick={() =>
                    handleCardClick(randomCategory.questions.indexOf(item))
                  }
                  className="flex-1 min-w-[30%] aspect-[7/10] rounded-lg overflow-hidden border-2 border-surface-3 shadow-[0px_0px_8px_rgba(0,0,0,0.1)] cursor-pointer"
                >
                  <img
                    src={imageUrl}
                    alt={item.q}
                    className="w-full h-full object-cover "
                  />
                </div>
              </ProtectedSection>
            );
          })}
        </div>

        {/* Aur Dekhe CTA */}
        <button
          onClick={handleSeeMore}
          className="flex items-center justify-center py-1"
        >
          <SecondaryBody className="font-bold text-base text-primary-600">
            और देखें →
          </SecondaryBody>
        </button>
      </div>
    </div>
  );
}

const PhoneCall = ({ width = 13, height = 13 }) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 14 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.62874 4.48679C8.67017 4.48679 9.51443 5.33104 9.51443 6.37248C9.51443 6.71963 9.79584 7.00104 10.143 7.00104C10.4901 7.00104 10.7716 6.71963 10.7716 6.37248C10.7716 4.63675 9.36447 3.22967 7.62874 3.22967C7.28159 3.22967 7.00017 3.51108 7.00017 3.85823C7.00017 4.20538 7.28159 4.48679 7.62874 4.48679Z"
        fill="url(#paint0_linear_13225_6634)"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.6764 9.04959C12.8876 9.0986 13.0587 9.25302 13.129 9.45816C13.182 9.60735 13.222 9.76088 13.2484 9.91701C13.2737 10.075 13.2863 10.2347 13.2861 10.3947C13.2861 11.1616 12.9815 11.897 12.4392 12.4392C11.897 12.9815 11.1616 13.2861 10.3947 13.2861C5.05154 13.2792 0.721769 8.94941 0.714844 3.60623C0.714844 2.00936 2.00936 0.714844 3.60623 0.714844C3.76924 0.719809 3.93136 0.740863 4.09023 0.7777C4.24442 0.800517 4.396 0.838413 4.54279 0.890841C4.74793 0.961125 4.90235 1.13218 4.95136 1.34341L5.81249 5.07079C5.85965 5.27879 5.79812 5.49651 5.64906 5.64906C5.57195 5.73804 5.56201 5.74305 4.89013 6.08148C4.85008 6.10165 4.80769 6.123 4.76279 6.14563C5.38192 7.50386 6.46821 8.59461 7.82389 9.2193C8.23246 8.43988 8.23874 8.4336 8.32674 8.35188C8.47929 8.20283 8.69702 8.1413 8.90502 8.18846L12.6764 9.04959ZM12.0287 6.37191C12.0287 3.94189 10.0588 1.97197 7.62874 1.97197C7.28159 1.97197 7.00017 1.69055 7.00017 1.34341C7.00017 0.996261 7.28159 0.714844 7.62874 0.714844C10.753 0.714844 13.2858 3.2476 13.2858 6.37191C13.2858 6.71906 13.0044 7.00047 12.6572 7.00047C12.3101 7.00047 12.0287 6.71906 12.0287 6.37191Z"
        fill="url(#paint1_linear_13225_6634)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_13225_6634"
          x1="7.00048"
          y1="0.714844"
          x2="7.00048"
          y2="13.2861"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2F80ED" />
          <stop offset="1" stopColor="#0385DC" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_13225_6634"
          x1="7.00048"
          y1="0.714844"
          x2="7.00048"
          y2="13.2861"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2F80ED" />
          <stop offset="1" stopColor="#0385DC" />
        </linearGradient>
      </defs>
    </svg>
  );
};
