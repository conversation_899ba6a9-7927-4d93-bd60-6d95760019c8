import React from 'react';

import Image from 'next/image';
import { useRouter } from 'next/router';

import { H4, TertiaryBody } from 'components/ui/typography';

import { ROBOTS } from 'data/robots';
import useAppPaywall from 'modules/app-subscription/hooks/useAppPaywall';
import { useShowPaywall } from 'providers/PaywallProvider';

import RobotCard from './RobotCard';

const mainRobot =
  'https://static-assets.animall.in/static/images/robots/mainRobot.png';

const RobotGrid = ({ isFreeAccess = false }) => {
  const router = useRouter();
  const { isPremiumUser } = useAppPaywall();
  const { showPaywall } = useShowPaywall();

  // Filter out generic robot from display
  const displayRobots = ROBOTS.filter(
    (robot) => robot.link !== '/chat/generic-robot',
  );

  // premium OR override from Home (old user in X9) => free
  const canUseRobots = isFreeAccess || isPremiumUser;

  const handleRobotCardClick = (href) => {
    if (canUseRobots) {
      router.push(`${href}?source=HomeGrid`);
    } else {
      showPaywall({ source: 'ROBOT_GRID' });
    }
  };

  return (
    <div className="w-full pb-6 px-4">
      <div className="w-full max-w-[460px] mx-auto relative ">
        {/* Robot image + heading row */}
        <div className="flex flex-row items-center gap-3 z-10 -mb-3">
          <div className="flex-shrink-0 ml-2">
            <Image
              src={mainRobot}
              alt="Main Robot"
              width={98.2}
              height={75.8}
              className="z-10"
            />
          </div>

          <div className="flex flex-col justify-center ">
            <H4 className="text-xl leading-[120%] text-primary-600 text-left ">
              रोबोट से पूछो
            </H4>
            <TertiaryBody className="text-sm leading-[140%] tracking-[0.01em] text-text-primary text-left ">
              हर समस्या का हल
            </TertiaryBody>
          </div>
        </div>

        {/* Card Grid Container */}
        <div className="w-full bg-white rounded-xl shadow-[0_0_8px_rgba(0,0,0,0.05)] p-3 z-0 sm:max-w-[440px] md:max-w-[480px]">
          <div className="grid grid-cols-3 gap-x-2.5 gap-y-3 justify-items-center w-full h-full">
            {displayRobots.map((robot) => (
              <RobotCard
                key={robot.title}
                title={robot.title}
                imageSrc={robot.image}
                subtitle={robot.subtitle}
                href={robot.link}
                onClick={handleRobotCardClick}
                isLocked={!canUseRobots}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RobotGrid;
