import { useContext, useEffect, useMemo, useState } from 'react';

import { useTranslation } from 'next-i18next';
import { useRouter } from 'next/router';

import { logAmplitudeEvent } from 'lib/log-event';
import { getCookie } from 'lib/storage';
import { isInBuckets } from 'lib/utils';
import {
  compareAppVersions,
  getUserData,
  getUserProfileUrl,
  openSellPage,
} from 'lib/utils';

import { AnimallLoaderContext } from 'components/AnimallLoader';
import StethoScopeIcon from 'components/BottomBar/StethoScopeIcon';
import BottomNavBar from 'components/BottomNavBar';

import { getZoneFromLocalStorage } from 'modules/assessment/chatbot/utils';
import {
  BuyerFlows,
  OnboardingFlows,
} from 'modules/assessment/enums/request.enum';
import VipPlanLoader from 'modules/home/<USER>/VipPlanLoader';
import { USERTYPE } from 'modules/home/<USER>';
import { getUserType } from 'modules/home/<USER>';
import { isEligibleForNewHomePage } from 'modules/home/<USER>';
import { useGrowthUser } from 'providers/GrowthUserProvider';
import { MonetizationContext } from 'providers/Monetization';

import useAppPaywall from '../../modules/app-subscription/hooks/useAppPaywall';
import { BuyPashuIcon } from './BuyPashuIcon';
import CategoryIcon from './CategoryIcon';
import { CommunityIcon } from './CommunityIcon';
import { CowIcon } from './CowIcon';
import { HomeLandingIcon } from './HomeLandingIcon';
import { PartnerIcon } from './PartnerIcon';
import { PashuRobotIcon } from './PashuRobotIcon';
import { PashuScannerIcon } from './PashuScannerIcon';
import { ProfileIcon } from './ProfileIcon';
import VipBaneIcon from './VipBaneIcon';

const PASHU_ILLAJ_DISTRICT = 'AGRA';

function BottomBar({ classNames }) {
  const router = useRouter();
  const { pathname } = router;

  const { openAnimallLoader, closeAnimallLoader } =
    useContext(AnimallLoaderContext);

  const { t: cm } = useTranslation('common');

  const [navItems, setNavItems] = useState([]);
  const isNewHomePageEligible = isEligibleForNewHomePage();
  const [showLoadingAnimation, setShowLoadingAnimation] = useState(false);
  const monetizationStats = useContext(MonetizationContext);
  const { current: currentUserType, previous: previousUserType } =
    getUserType(monetizationStats);
  const { isGrowthUser } = useGrowthUser();
  const { isEligible: isAppPaywallEligible, isPremiumUser } = useAppPaywall();

  const robotEnabledBucket = isInBuckets([40, 99], getCookie('bucketId'));
  const categoryEligible =
    (isGrowthUser &&
      isPremiumUser &&
      isInBuckets([60, 99], getCookie('bucketId'))) ||
    (!isGrowthUser && isInBuckets([80, 89], getCookie('bucketId')));

  useEffect(() => {
    const handleMenuItems = async () => {
      let centerElement;
      let otherElements = [];

      let processedMenuItems = await Promise.all(
        menuItems.map(async (menuItem) => {
          if (
            menuItem.item === 'sell' &&
            !sessionStorage.getItem('showedActiveSellIcon')
          ) {
            menuItem.highlight = true;
          }
          if (menuItem.isConditional) {
            try {
              const conditional = await menuItem.condition();
              if (conditional) {
                return menuItem;
              }
            } catch (error) {
              console.error('Error resolving menuItem condition:', error);
              return null;
            }
          } else {
            return menuItem;
          }
        }),
      );

      processedMenuItems = processedMenuItems.filter((el) => !!el);
      processedMenuItems.forEach((menuItem, _, thisArray) => {
        if (thisArray.length % 2 === 1 && menuItem?.preferCenter) {
          centerElement = menuItem;
        } else {
          otherElements.push(menuItem);
        }
      });

      const totalItems = otherElements.length + (!!centerElement ? 1 : 0);
      const isOddElements = totalItems % 2 === 1;

      if (isOddElements) {
        if (!centerElement) {
          const landingPageElIndex = otherElements.findIndex(
            (menuItem) => !!menuItem.isLandingPage,
          );
          centerElement = otherElements[landingPageElIndex];
          otherElements.splice(landingPageElIndex, 1);
        }
        const centerIdx = Math.round(totalItems / 2) - 1;
        otherElements.splice(centerIdx, 0, centerElement);
      }
      setNavItems(otherElements);
    };

    handleMenuItems();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isGrowthUser]);

  const navigate = ({
    path,
    event = undefined,
    eventProps = {},
    isExternal = false,
  }) => {
    if (event) {
      logAmplitudeEvent(...event.split('_'), eventProps);
    }
    if (path.includes('/vip/dashboard')) {
      if (currentUserType === USERTYPE.VIP) {
        setShowLoadingAnimation(true);
      }
      setTimeout(() => router.push(path), 2000);
      return false;
    }
    isExternal
      ? (window.location.href = `${path}?source=BottomMenu`)
      : router.push(`${path}?source=BottomMenu`);
  };

  const shouldPashuIllajDisplayed = async () => {
    const userDistrict = (await getUserData())?.district?.toUpperCase();
    return userDistrict === PASHU_ILLAJ_DISTRICT;
  };

  const shouldProfileMenuIconDisplayed = async () => {
    const isPashuIllajRegion = await shouldPashuIllajDisplayed();
    const isLoggedIn = !!getCookie('accessToken');
    return isLoggedIn && !isPashuIllajRegion && !isNewHomePageEligible;
  };

  const menuItems = useMemo(
    () => [
      {
        item: 'home-landing',
        isLandingPage: true,
        preferCenter: isGrowthUser ? true : false,
        icon: <HomeLandingIcon active={['/', '/home'].includes(pathname)} />,
        text: 'Animall',
        activeLinkCheck: ['/', '/home'],
        clickAction: () => {
          navigate({ path: '/home' });
        },
        activeBackgroundClass: 'bg-primary-500',
      },
      {
        item: 'buy',
        icon: <BuyPashuIcon active={pathname === '/buy'} />,
        text: cm('menu.home'),
        activeLinkCheck: ['/buy'],
        clickAction: () => {
          navigate({ path: '/buy' });
        },
      },
      {
        item: 'sell',
        icon: (
          <CowIcon
            width={29}
            height={26}
            active={['/sell', '/sell/edit/[postId]'].includes(pathname)}
          />
        ),
        text: cm('menu.sell'),
        activeLinkCheck: ['/sell', '/sell/edit/[postId]'],
        clickAction: () =>
          openSellPage('BottomMenu', {
            beforeCb: openAnimallLoader,
            afterCb: () => {
              sessionStorage.setItem('showedActiveSellIcon', true);
              const appVersion = getCookie('appVersion');
              if (compareAppVersions(appVersion, '<', '3.0.7')) {
                // From 3.0.7, Android will call `hideLoader()` to close the loader (added inside `useEffect` in `_app.js`)
                setTimeout(closeAnimallLoader, 2000);
              }
            },
          }),
      },
      {
        item: 'chat',
        icon: (
          <CommunityIcon
            width={29}
            height={26}
            active={['/pashu-chat'].includes(pathname)}
          />
        ),
        text: cm('menu.community'),
        activeLinkCheck: ['/pashu-chat'],
        clickAction: () => {
          navigate({ path: '/pashu-chat' });
        },
        isConditional: true,
        condition: () => {
          return !isNewHomePageEligible;
        },
      },
      {
        item: 'profile',
        icon: <ProfileIcon active={['/user/[slug]'].includes(pathname)} />,
        text: cm('menu.profile'),
        isConditional: true,
        condition: shouldProfileMenuIconDisplayed,
        activeLinkCheck: ['/user/[slug]'],
        clickAction: () => {
          const url = getUserProfileUrl('BottomMenu');
          navigate({ path: url });
        },
      },
      {
        item: 'pashu-illaj',
        icon: <StethoScopeIcon active={['/veterinary'].includes(pathname)} />,
        text: cm('menu.pashuIllaj'),
        isConditional: true,
        condition: shouldPashuIllajDisplayed,
        activeLinkCheck: ['/veterinary'],
        clickAction: () => {
          navigate({ path: '/veterinary' });
        },
      },
      {
        item: 'pashu-scanner',
        icon: (
          <PashuScannerIcon
            width={29}
            height={26}
            active={['/pashu-scanner', '/assessment/landing'].includes(
              pathname,
            )}
          />
        ),
        text: 'फ्रॉड पकड़ें',
        activeLinkCheck: ['/pashu-scanner', '/assessment/landing'],
        isConditional: true,
        condition: async () => {
          const pashuIllaj = await shouldPashuIllajDisplayed();
          const profile = await shouldProfileMenuIconDisplayed();
          return !profile && !pashuIllaj && !isNewHomePageEligible;
        },
        clickAction: () => {
          navigate({
            path: '/assessment/landing',
            event: 'CLICKED_PASHUSCANNER_HOME',
            eventProps: {
              REQUEST_STATUS:
                sessionStorage.getItem('ASSESS_REQUEST_STATUS') || 'NA',
              ZONE: getZoneFromLocalStorage(),
              FLOW: BuyerFlows.NON_CHATBOT_V3,
              ONBOARDING: OnboardingFlows.FRAUD,
            },
          });
        },
      },
      {
        item: 'bot-partner',
        icon: (
          <PartnerIcon
            width={24}
            height={24}
            active={['/bot-partner'].includes(pathname)}
          />
        ),
        text: 'पशु पार्टनर',
        activeLinkCheck: ['/bot-partner'],
        isConditional: true,
        condition: () => !robotEnabledBucket && isAppPaywallEligible,
        clickAction: () => {
          navigate({ path: '/bot-partner' });
        },
      },
      {
        item: 'pashu-robots',
        icon: (
          <PashuRobotIcon
            width={24}
            height={24}
            active={['/robots'].includes(pathname)}
          />
        ),
        text: 'पशु रोबोट',
        activeLinkCheck: ['/robots'],
        isConditional: true,
        condition: () => robotEnabledBucket && isAppPaywallEligible,
        clickAction: () => {
          navigate({ path: '/robots' });
        },
      },
      {
        item: 'categories',
        icon: (
          <CategoryIcon
            width={18}
            height={18}
            active={['/categories'].includes(pathname)}
          />
        ),
        text: 'पशु देखभाल',
        activeLinkCheck: ['/categories'],
        isConditional: true,
        condition: () => categoryEligible,
        clickAction: () => {
          navigate({ path: '/categories' });
        },
      },
      {
        item: 'vip-bane',
        icon: <VipBaneIcon />,
        text: [currentUserType, previousUserType].includes(USERTYPE.VIP)
          ? 'VIP'
          : 'मेरा प्लान',
        activeLinkCheck: ['/vip/landing', '/myplan'],
        isConditional: true,
        showNewTag: [currentUserType, previousUserType].includes(USERTYPE.VIP)
          ? false
          : true,
        condition: async () => !isGrowthUser && !isAppPaywallEligible,
        activeTextClass: ['/vip/landing'].includes(pathname)
          ? 'text-[#E2B65C]'
          : null,
        clickAction: () => {
          navigate({
            path: [currentUserType, previousUserType].includes(USERTYPE.VIP)
              ? '/vip/dashboard?source=LandingPage'
              : '/myplan',
          });
        },
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isGrowthUser, isAppPaywallEligible],
  );

  return (
    <>
      <BottomNavBar
        classNames={classNames}
        navItems={navItems}
        theme={'/vip/landing' === pathname ? 'dark' : 'light'}
        userType={currentUserType}
        previousUserType={previousUserType}
      />
      {showLoadingAnimation && <VipPlanLoader />}
    </>
  );
}

export default BottomBar;
