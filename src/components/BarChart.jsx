import { useCallback, useEffect, useRef, useState } from 'react';

import { cn } from 'lib/utils';

import { DashedLines } from 'modules/post-details/icons';
import {
  Bar,
  ComposedChart,
  Line,
  ResponsiveContainer,
  XAxis,
  YAxis,
} from 'recharts';

const digitCount = (n) => Math.trunc(Math.abs(n)).toString().length;

const CHART_HEIGHT = 200;
const BarChart = ({
  data = [
    { day: '2025-08-15T00:00:00.000Z', count: 6 },
    { day: '2025-08-16T00:00:00.000Z', count: 9 },
    { day: '2025-08-17T00:00:00.000Z', count: 14 },
    { day: '2025-08-18T00:00:00.000Z', count: 15 },
    { day: '2025-08-19T00:00:00.000Z', count: 21 },
    { day: '2025-08-20T00:00:00.000Z', count: 18 },
    { day: '2025-08-21T00:00:00.000Z', count: 8 },
  ],
  BAR_WIDTH = 52,
  className = 'bg-[#FAFAFA]',
  xAxisField = 'day',
  yAxisField = 'count',
}) => {
  const scrollContainerRef = useRef(null);
  const [scrollPosition, setScrollPosition] = useState(0);
  const chartWidth = data.length * BAR_WIDTH;
  const lineWidth = `${chartWidth}px`;
  const handleScroll = useCallback(() => {
    const scrollContainer = scrollContainerRef.current;
    if (scrollContainer) {
      setScrollPosition(scrollContainer.scrollLeft);
    }
  }, []);

  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    const scrollToEnd = () => {
      const maxScroll =
        scrollContainer.scrollWidth - scrollContainer.clientWidth;
      scrollContainer.scrollLeft = maxScroll;
      setScrollPosition(maxScroll);
    };

    const timeoutId = setTimeout(scrollToEnd, 0);

    return () => clearTimeout(timeoutId);
  }, [data.length]);

  const CustomBar = useCallback((props) => {
    const { x, y, width, height, payload } = props;
    const isToday = payload?.date === 'आज';

    const fillColor = isToday ? '#FFCFCF' : '#DEE0E4';

    return (
      <rect
        x={x}
        y={y}
        width={width}
        height={height}
        fill={fillColor}
        rx={width / 2}
        ry={width / 2}
      />
    );
  }, []);

  const CustomLabel = useCallback((props) => {
    const { x, y, width, value, index } = props;
    const dataPoint = data[index];
    const isToday = dataPoint?.date === 'आज';
    const fill = '#E56E00';
    if (!isToday) {
      return (
        <text
          x={x + width / 2}
          y={y - 15}
          textAnchor="middle"
          fontSize="12"
          fontWeight="400"
          fill="#656F7C"
        >
          {value}
        </text>
      );
    }

    const bubbleConfig = {
      width: digitCount(value) > 3 ? 70 : 39,
      height: 24,
      x: x + width / 2 - (digitCount(value) > 3 ? 33.5 : 19.5),
      y: y - 50,
      arrowSize: 6,
    };

    return (
      <g>
        <defs>
          <filter id="labelShadow" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow
              dx="0"
              dy="2"
              stdDeviation="2"
              floodColor="#000000"
              floodOpacity="0.1"
            />
          </filter>
        </defs>

        <rect
          x={bubbleConfig.x}
          y={bubbleConfig.y}
          width={bubbleConfig.width}
          height={bubbleConfig.height}
          fill="white"
          rx="1"
          ry="1"
          filter="url(#labelShadow)"
        />

        <polygon
          points={`${x + width / 2 - bubbleConfig.arrowSize},${
            bubbleConfig.y + bubbleConfig.height
          } ${x + width / 2},${
            bubbleConfig.y + bubbleConfig.height + bubbleConfig.arrowSize
          } ${x + width / 2 + bubbleConfig.arrowSize},${
            bubbleConfig.y + bubbleConfig.height
          }`}
          fill="white"
          filter="url(#labelShadow)"
        />

        <text
          x={x + width / 2}
          y={bubbleConfig.y + bubbleConfig.height / 2 + 4}
          textAnchor="middle"
          fontSize="20"
          fontWeight="700"
          fill={fill}
        >
          {value}
        </text>
      </g>
    );
  }, []);

  const CustomDot = useCallback((props) => {
    const { cx, cy, payload } = props;
    const isToday = payload?.date === 'आज';

    if (isToday) {
      return (
        <g>
          <circle
            cx={cx}
            cy={cy}
            r="8"
            fill="#E56E00"
            stroke="#E56E00"
            strokeWidth="10"
            opacity="100"
          >
            <animate
              attributeName="r"
              values="6;20;6"
              dur="2s"
              repeatCount="indefinite"
              begin="0s"
            />
            <animate
              attributeName="opacity"
              values="0.8;0;0.8"
              dur="2s"
              repeatCount="indefinite"
              begin="0s"
            />
            <animate
              attributeName="strokeWidth"
              values="1;1;1"
              dur="3s"
              repeatCount="indefinite"
              begin="0s"
            />
          </circle>

          <circle
            cx={cx}
            cy={cy}
            r={6}
            fill="white"
            stroke="#E56E00"
            strokeWidth={3}
          />
        </g>
      );
    }
    const stroke =
      // payload?.status === POST_STATUS.EXPIRED
      //   ? '#E50000'
      //   : !isViewTabActive
      //   ? '#2F80ED'
      //   :
      '#2E3C4D';
    return (
      <circle
        cx={cx}
        cy={cy}
        r={6}
        fill="white"
        stroke={stroke}
        strokeWidth={3}
      />
    );
  }, []);

  if (!data.length) {
    return (
      <div className={cn('w-full max-w-4xl mx-auto bg-white p-4', className)}>
        <div className="text-center text-gray-500">No data available</div>
      </div>
    );
  }
  return (
    <div
      className={cn('w-full max-w-4xl mx-auto bg-white rounded-xl', className)}
    >
      <div className="relative">
        <div
          ref={scrollContainerRef}
          onScroll={handleScroll}
          className="overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 pb-2"
          style={{
            overflowX: 'auto',
            scrollBehavior: 'smooth',
            width: '100%',
            scrollbarWidth: 'thin',
            scrollbarColor: '#cbd5e0 #f7fafc',
          }}
        >
          <div
            style={{
              width: `${chartWidth}px`,
              height: `${CHART_HEIGHT}px`,
            }}
          >
            <DashedLines lineWidth={lineWidth} />
            <ResponsiveContainer width="100%" height="100%">
              <ComposedChart
                data={data}
                margin={{ top: 53, right: 10, left: 10, bottom: 0 }}
                barCategoryGap="10%"
              >
                <XAxis
                  dataKey={xAxisField}
                  axisLine={false}
                  tickLine={true}
                  tick={{ fontSize: 10, fill: '#656F7C', fontWeight: '700' }}
                />
                <YAxis hide />

                <Bar
                  dataKey={yAxisField}
                  shape={<CustomBar />}
                  label={<CustomLabel />}
                  maxBarSize={20}
                />

                <Line
                  type="linear"
                  dataKey="lineValue"
                  stroke={'#2E3C4D'}
                  strokeWidth={3}
                  dot={<CustomDot />}
                  connectNulls={false}
                  strokeLinecap="square"
                  strokeLinejoin="bevel"
                />
              </ComposedChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BarChart;
