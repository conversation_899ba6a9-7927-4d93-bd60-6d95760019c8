import { useRouter } from 'next/router';

import { cn } from 'lib/utils';

import CategoryIcon from 'components/BottomBar/CategoryIcon';
import { CowIcon } from 'components/BottomBar/CowIcon';
import MyPlanIcon from 'components/BottomBar/MyPlanIcon';
import { PartnerIcon } from 'components/BottomBar/PartnerIcon';
import { PashuRobotIcon } from 'components/BottomBar/PashuRobotIcon';
import VipBaneIcon from 'components/BottomBar/VipBaneIcon';

import { FarmHomeIcon } from 'modules/farm/components/BottomBar/icons';
import { MySiteIcon } from 'modules/farm/components/BottomBar/icons';
import { BuyerIcon } from 'modules/farm/components/BottomBar/icons';
import { USERTYPE } from 'modules/home/<USER>';
import { NewTagIcon } from 'modules/myplan/Icons';

import {
  AmartIcon,
  BuyIcon,
  ChatIcon,
  HealthIcon,
  HomeIcon,
  HomeLandingIcon,
  PashuScannerIcon,
  ProfileIcon,
  SellIcon,
} from './icons';

// theme : dark, light
const BottomNavBar = ({
  navItems = [],
  theme = 'light',
  classNames = '',
  userType,
  previousUserType,
}) => {
  const router = useRouter();
  const { pathname } = router;

  const NAV_ITEM_ICON_MAP = {
    home: <HomeIcon active={pathname === '/buy'} />,
    buy: <BuyIcon active={pathname === '/buy'} />,
    sell: (
      <SellIcon active={['/sell', '/sell/edit/[postId]'].includes(pathname)} />
    ),
    'home-landing': (
      <HomeLandingIcon active={['/', '/home'].includes(pathname)} />
    ),
    mart: <AmartIcon active={pathname === '/mart'} />,
    'pashu-scanner': (
      <PashuScannerIcon
        active={['/pashu-scanner', '/assessment/landing'].includes(pathname)}
      />
    ),
    chat: <ChatIcon active={pathname === '/pashu-chat'} />,
    profile: <ProfileIcon active={pathname === '/user/[slug]'} />,
    'pashu-illaj': <HealthIcon active={pathname === '/veterinary'} />,
    // farm
    'farm-home': (
      <FarmHomeIcon
        active={['/farm/[slug]/dashboard', '/vip/dashboard'].includes(pathname)}
      />
    ),
    'my-listings': (
      <CowIcon
        width={24}
        height={20}
        active={[
          '/farm/[slug]/my-listings',
          '/vip/dashboard/my-listings',
        ].includes(pathname)}
      />
    ),
    customers: (
      <BuyerIcon
        active={['/farm/[slug]/customers', '/vip/dashboard/customers'].includes(
          pathname,
        )}
      />
    ),
    'my-site': <MySiteIcon active={'/farm/[slug]' === pathname} />,
    'vip-bane': [userType, previousUserType].includes(USERTYPE.VIP) ? (
      <VipBaneIcon active={'/vip/landing' === pathname} />
    ) : (
      <MyPlanIcon active={'/myplan' === pathname} />
    ),
    'bot-partner': (
      <PartnerIcon
        width={24}
        height={24}
        active={['/bot-partner'].includes(pathname)}
      />
    ),
    'pashu-robots': (
      <PashuRobotIcon
        width={24}
        height={24}
        active={['/robots'].includes(pathname)}
      />
    ),
    categories: (
      <CategoryIcon
        width={18}
        height={18}
        active={['/categories'].includes(pathname)}
      />
    ),
  };

  return (
    <div
      id="bottomNav"
      className={cn(
        'fixed bottom-0 left-0 w-full h-[68px] py-1.5 px-0 z-20 flex items-center rounded-t-lg shadow-[0px_-2px_2px_rgba(0,0,0,0.2)]',
        theme === 'light' ? 'bg-white' : 'bg-vipPage-surface-0',
        classNames,
      )}
    >
      {navItems.map((navItem) => {
        const isActive = navItem.activeLinkCheck.includes(pathname);
        return (
          <div
            className="relative basis-full flex-grow flex flex-col items-center justify-between gap-[3px] py-1 px-0 h-full"
            key={`nav-${navItem.item}`}
            onClick={navItem.clickAction}
          >
            {NAV_ITEM_ICON_MAP[navItem.item]}

            <div className="flex justify-center gap-0.5 items-center">
              {navItem.showNewTag && (
                <p>
                  <NewTagIcon />
                </p>
              )}
              <span
                className={`flex-shrink-0 text-xs font-medium leading-5 text-center ${
                  isActive
                    ? navItem.activeTextClass || 'text-navActive'
                    : 'text-navInactive'
                }`}
              >
                {navItem.text}
              </span>
            </div>

            {navItem.highlight && (
              <div className="absolute top-0 left-[calc(50%-16px)] w-1.5 h-1.5 bg-status-rejected-text rounded-full"></div>
            )}
          </div>
        );
      })}
    </div>
  );
};

export default BottomNavBar;
