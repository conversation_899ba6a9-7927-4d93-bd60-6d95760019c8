import { useCallback, useContext, useEffect, useRef, useState } from 'react';

import axios from 'lib/axios';
import { isInBuckets } from 'lib/utils';

import { noop } from 'lodash';
import { parseCookies } from 'nookies';
import { MonetizationContext } from 'providers/Monetization';

const PHONEPE_BUCKET_RANGE = []; // Disabled for all buckets
const MAX_POLLING_ATTEMPTS = 60;
const MAX_POLLING_ATTEMPTS_AFTER_RETURN = 3;
const POLLING_INTERVAL = 5000;
const POPUP_TIMEOUT = 1000;

// Static UPI apps list for web users
const WEB_UPI_APPS = [
  'com.phonepe.app', // PhonePe (preselected)
  'com.google.android.apps.nbu.paisa.user', // Google Pay
  'net.one97.paytm', // Paytm
  'in.org.npci.upiapp', // BHIM
  'in.amazon.mShop.android.shopping', // Amazon Pay
];

// Payment status enum
const PAYMENT_STATUS = {
  PENDING: 'PENDING',
  COMPLETED: 'COMPLETED',
  FAILED: 'FAILED',
};

/**
 * Check if device is mobile
 * @returns {boolean} - Whether the device is mobile
 */
const isMobileDevice = () => {
  if (typeof window === 'undefined') return false;

  const userAgent = navigator.userAgent || navigator.vendor || window.opera;

  // Check for mobile devices
  return (
    /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
      userAgent,
    ) ||
    (navigator.maxTouchPoints &&
      navigator.maxTouchPoints > 2 &&
      /MacIntel/.test(navigator.platform))
  );
};

/**
 * Check if PhonePe feature is available based on bucket ID, device type, and app eligibility
 * @param {Object} stats - Monetization stats object
 * @returns {boolean} - Whether PhonePe feature is available
 */
const isPhonepeFeatureAvailable = (stats) => {
  const { bucketId } = parseCookies();

  const phonepeBuckets = isInBuckets(PHONEPE_BUCKET_RANGE, bucketId);
  const isAppEligible = stats?.app?.isEligible || false;

  // PhonePe only works on mobile devices (intent-based) and requires app eligibility
  return phonepeBuckets && isMobileDevice() && isAppEligible;
};

/**
 * Custom hook for PhonePe payment integration
 * @returns {Object} - PhonePe hook methods and state
 */
const usePhonepe = () => {
  const [isPhonepeSupported, setIsPhonepeSupported] = useState(false);
  const [availableUpiApps, setAvailableUpiApps] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // Get monetization stats to check app eligibility
  const stats = useContext(MonetizationContext);

  // Refs to track cleanup functions and prevent memory leaks
  const cleanupRefs = useRef(new Set());
  const isInitializedRef = useRef(false);
  const pollingRef = useRef(null);
  const attemptCountRef = useRef(0);
  const returnAttemptCountRef = useRef(0);
  const isReturnedRef = useRef(false);

  // Initialize PhonePe support
  useEffect(() => {
    const isEligible = isPhonepeFeatureAvailable(stats);
    setIsPhonepeSupported(isEligible);
  }, [stats]);

  useEffect(() => {
    if (!isPhonepeSupported || isInitializedRef.current) return;

    const cleanupSet = cleanupRefs.current;

    const fetchUpiApps = async () => {
      setIsLoading(true);
      try {
        // Setup handler BEFORE calling the bridge
        const upiAppsHandler = (apps) => {
          setAvailableUpiApps(Array.isArray(apps) ? apps : []);
          setIsLoading(false);
          isInitializedRef.current = true;
        };

        // Store cleanup function - only run on unmount, not re-render
        // const cleanup = () => {
        //   if (window.onUpiAppsFetched) {
        //     delete window.onUpiAppsFetched;
        //   }
        // };
        // cleanupSet.add(cleanup);

        // Set the handler first
        window.onUpiAppsFetched = upiAppsHandler;

        if (window.Android?.getInstalledUpiApps) {
          window.Android.getInstalledUpiApps();
        } else {
          // For web users, provide a static list of popular UPI apps
          setAvailableUpiApps(WEB_UPI_APPS);
          setIsLoading(false);
          isInitializedRef.current = true;
        }
      } catch (err) {
        console.error('Error while calling UPI apps bridge function:', err);
        // For web users or on error, provide static list of popular UPI apps
        setAvailableUpiApps(WEB_UPI_APPS);
        setIsLoading(false);
        isInitializedRef.current = true;
      }
    };

    fetchUpiApps();

    // Cleanup ONLY on unmount - not on re-render
    return () => {
      cleanupSet.forEach((cleanup) => cleanup());
      cleanupSet.clear();
    };
  }, [isPhonepeSupported]); // Removed availableUpiApps.length dependency

  /**
   * Clear polling interval and reset attempt counter
   */
  const clearPolling = useCallback(() => {
    if (pollingRef.current) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }
    attemptCountRef.current = 0;
  }, []);

  /**
   * Poll payment status from backend
   * @param {Object} params - Polling parameters
   * @param {string} params.orderId - Order ID
   * @param {string} params.merchantOrderId - Merchant Order ID
   * @param {Function} params.onSuccess - Success callback
   * @param {Function} params.onFailure - Failure callback
   */

  const pollPaymentStatus = useCallback(
    ({ orderId, merchantOrderId, onSuccess, onFailure }) => {
      clearPolling();

      pollingRef.current = setInterval(async () => {
        try {
          const currentAttempt = ++attemptCountRef.current;

          // timeouts
          const afterReturnAttempts =
            currentAttempt - (returnAttemptCountRef.current || 0);
          if (
            currentAttempt >= MAX_POLLING_ATTEMPTS ||
            (isReturnedRef.current &&
              afterReturnAttempts > MAX_POLLING_ATTEMPTS_AFTER_RETURN)
          ) {
            clearPolling();
            onFailure(new Error('Payment verification timeout'));
            return;
          }

          const response = await axios.post('/api/payment/phonepe/result', {
            orderId,
            merchantOrderId,
            type: 'subscription',
          });

          const { status, data } = response.data;

          switch (status) {
            case PAYMENT_STATUS.COMPLETED:
              clearPolling();
              onSuccess(data);
              break;

            case PAYMENT_STATUS.FAILED:
              clearPolling();
              onFailure(new Error('Payment failed'));
              break;

            case PAYMENT_STATUS.PENDING:
              break;

            default:
              console.warn(`[PhonePe] Unknown payment status: ${status}`);
          }
        } catch (error) {
          clearPolling();
          console.error('[PhonePe] Error polling payment status:', error);
          onFailure(error);
        }
      }, POLLING_INTERVAL);
    },
    [clearPolling],
  );

  /**
   * Initialize PhonePe payment
   * @param {Object} params - Payment parameters
   * @param {string} params.orderId - Order ID
   * @param {string} params.token - Payment token
   * @param {Function} params.successCallback - Success callback
   * @param {Function} params.failureCallback - Failure callback
   * @returns {boolean} - Whether initialization was successful
   */
  const initPhonepePayment = useCallback(
    ({ orderId, token, successCallback = noop, failureCallback = noop }) => {
      // Validation
      if (!orderId || !token) {
        failureCallback();
        return false;
      }

      if (!isPhonepeSupported) {
        failureCallback();
        return false;
      }

      try {
        if (!window.Android?.initiatePhonePePayment) {
          return false;
        }

        // Setup payment callbacks with proper cleanup
        const paymentCleanup = () => {
          delete window.phonePePaymentSuccess;
          delete window.phonePePaymentFailure;
          delete window.phonePePaymentCancelled;
        };

        window.phonePePaymentSuccess = () => {
          paymentCleanup();
          try {
            successCallback();
          } catch (err) {
            console.error('Error in success callback:', err);
          }
        };

        window.phonePePaymentFailure = (error) => {
          paymentCleanup();
          try {
            failureCallback();
          } catch (err) {
            console.error('Error in failure callback:', err);
          }
        };

        window.phonePePaymentCancelled = () => {
          console.log('[PhonePe] Payment Cancelled');
          paymentCleanup();
          try {
            failureCallback();
          } catch (err) {
            console.error('Error in cancellation callback:', err);
          }
        };

        // Store cleanup function
        cleanupRefs.current.add(paymentCleanup);

        // Initiate payment
        window.Android.initiatePhonePePayment(orderId, token);
        return true;
      } catch (error) {
        console.error('Error initializing PhonePe payment:', error);
        failureCallback();
        return false;
      }
    },
    [isPhonepeSupported],
  );

  /**
   * Initialize PhonePe subscription
   * @param {Object} params - Subscription parameters
   * @param {string} params.subscriptionOrderId - Subscription order ID
   * @param {string} params.token - Subscription token
   * @param {Function} params.successCallback - Success callback
   * @param {Function} params.failureCallback - Failure callback
   * @returns {boolean} - Whether initialization was successful
   */
  const initPhonepeSubscription = useCallback(
    ({
      orderId,
      merchantOrderId,
      token,
      intentUrl,
      successCallback = noop,
      failureCallback = noop,
    }) => {
      // Validation
      if (!orderId || !merchantOrderId || !token || !intentUrl) {
        failureCallback();
        return false;
      }

      if (!isPhonepeSupported) {
        failureCallback();
        return false;
      }

      try {
        // Open payment popup
        isReturnedRef.current = false;
        returnAttemptCountRef.current = 0;

        const onVisibility = () => {
          if (document.visibilityState === 'visible') {
            // user came back from UPI
            isReturnedRef.current = true;
            returnAttemptCountRef.current = attemptCountRef.current;
          }
        };

        document.addEventListener('visibilitychange', onVisibility);

        // IMPORTANT: make sure to remove the listener later (on terminal/timeout)
        const removeVisibility = () => {
          document.removeEventListener('visibilitychange', onVisibility);
        };
        // store cleanup so your unmount effect clears it
        cleanupRefs.current.add(removeVisibility);
        window.open(intentUrl, '_blank', 'noopener,noreferrer');

        // Start polling after popup opens
        setTimeout(() => {
          pollPaymentStatus({
            orderId,
            merchantOrderId,
            onSuccess: successCallback,
            onFailure: failureCallback,
          });
        }, POPUP_TIMEOUT);

        return true;
      } catch (error) {
        console.error('Error initializing PhonePe subscription:', error);
        failureCallback();
        return false;
      }
    },
    [isPhonepeSupported, pollPaymentStatus],
  );

  /**
   * Create window callback handlers for PhonePe subscription
   * @param {Function} onSuccess - Success callback
   * @param {Function} onFailure - Failure callback
   * @returns {Function} - Cleanup function
   */
  const createSubscriptionHandlers = useCallback((onSuccess, onFailure) => {
    const cleanup = () => {
      delete window.phonePeSubscriptionSuccess;
      delete window.phonePeSubscriptionFailure;
      delete window.phonePeSubscriptionCancelled;
    };

    window.phonePeSubscriptionSuccess = (data = {}) => {
      console.log('[PhonePe] Subscription success callback triggered');
      cleanup();
      try {
        onSuccess(data);
      } catch (error) {
        console.error('[PhonePe] Error in success handler:', error);
        onFailure(error);
      }
    };

    window.phonePeSubscriptionFailure = (error = {}) => {
      console.log('[PhonePe] Subscription failure callback triggered:', error);
      cleanup();
      onFailure(new Error(error.message || 'Subscription failed'));
    };

    window.phonePeSubscriptionCancelled = () => {
      console.log('[PhonePe] Subscription cancelled by user');
      cleanup();
      onFailure(new Error('Subscription cancelled by user'));
    };

    cleanupRefs.current.add(cleanup);
    return cleanup;
  }, []);

  /**
   * Get preferred PhonePe package name from available UPI apps
   * @returns {string|null} - Package name or null if not available
   */
  const getPhonepePackageName = useCallback(() => {
    if (!availableUpiApps.length) return null;

    const phonepeSimulatorApp = availableUpiApps.find(
      (app) => app === 'com.phonepe.simulator',
    ); // for dev
    const phonepeApp = availableUpiApps.find(
      (app) => app === 'com.phonepe.app',
    );
    const gpayApp = availableUpiApps.find(
      (app) => app === 'com.google.android.apps.nbu.paisa.user',
    );

    return phonepeSimulatorApp || phonepeApp || gpayApp || availableUpiApps[0];
  }, [availableUpiApps]);

  useEffect(() => {
    // Cleanup on unmount
    return () => {
      cleanupRefs.current.forEach((cleanup) => cleanup());
      cleanupRefs.current.clear();
      clearPolling();
    };
  }, [clearPolling]);

  return {
    // State
    isPhonepeSupported,
    availableUpiApps,
    isLoading,

    // Methods
    initPhonepePayment,
    initPhonepeSubscription,
    getPhonepePackageName,

    // Cleanup
    clearPolling,
  };
};

export default usePhonepe;
