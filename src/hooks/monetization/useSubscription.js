import {
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';

import usePhonepe from 'hooks/usePhonepe';
import useRazorpay from 'hooks/useRazorpay';

import axios from 'lib/axios';
import { logAmplitudeEvent, logFbEvent } from 'lib/log-event';
import { isAndroid } from 'lib/utils';

import { AnimallLoaderContext } from 'components/AnimallLoader';

import { AndroidModalVariant } from 'modules/sell/modals/AndroidModal';
import { parseCookies } from 'nookies';
import { useAndroidAppModal } from 'providers/AndroidAppModalProvider';
import { MonetizationContext } from 'providers/Monetization';
import { useShowPaywall } from 'providers/PaywallProvider';

// Constants
const DEFAULT_TOTAL_COUNT = 12;

// Payment gateways enum
const PAYMENT_GATEWAY = {
  PHONEPE: 'phonepe',
  RAZORPAY: 'razorpay',
};

const useSubscription = ({
  source,
  onSubscriptionSuccess,
  onSubscriptionFailure,
}) => {
  const { setShowLoaderScreen } = useShowPaywall();
  const { openModal: openAndroidModal } = useAndroidAppModal();
  const { closeAnimallLoader } = useContext(AnimallLoaderContext);
  const stats = useContext(MonetizationContext);
  const planRef = useRef(null);
  const subscriptionRef = useRef(null);
  const [selectedPackage, setSelectedPackage] = useState(null);

  const { initPayment } = useRazorpay();
  const {
    isPhonepeSupported,
    initPhonepeSubscription,
    availableUpiApps,
    getPhonepePackageName,
  } = usePhonepe();

  useEffect(() => {
    if (isPhonepeSupported && availableUpiApps.length && !selectedPackage) {
      setSelectedPackage(getPhonepePackageName());
    }
  }, [
    isPhonepeSupported,
    availableUpiApps,
    selectedPackage,
    getPhonepePackageName,
  ]);

  const { userPhone } = parseCookies();

  const [isEligibleForSubscription, setIsEligibleForSubscription] =
    useState(true); // Always enable subscriptions for web and app

  useEffect(() => {
    // Remove version gating - allow subscriptions for both web and app
    setIsEligibleForSubscription(true);
  }, []);

  const defaultGateway = useMemo(() => {
    // Always prefer PhonePe if supported, otherwise use Razorpay
    if (isPhonepeSupported) {
      return PAYMENT_GATEWAY.PHONEPE;
    }
    return PAYMENT_GATEWAY.RAZORPAY;
  }, [isPhonepeSupported]);

  const successCallback = useCallback(
    async ({
      razorpayPaymentId,
      razorpaySubscriptionId,
      razorpaySignature,
    }) => {
      try {
        await axios.post('/api/payment/result', {
          razorpayPaymentId,
          razorpaySubscriptionId,
          razorpaySignature,
        });

        logAmplitudeEvent('SUCCESS', 'PAYMENT', 'SUBSCRIPTION', {
          PLAN: planRef?.current?.name || 'NA',
          SOURCE: source,
          TRIAL:
            stats?.app?.latestAppSubscription?.recurringSubscription?.status ===
            'authenticated'
              ? 'YES'
              : 'NO',
          gateway: 'razorpay',
        });

        logFbEvent('SUCCESS_PAYMENT_SUBSCRIPTION');
        setShowLoaderScreen(false);
        onSubscriptionSuccess(planRef.current, subscriptionRef.current);
      } catch (error) {
        setShowLoaderScreen(false);
        onSubscriptionFailure();
      }
    },
    [
      onSubscriptionFailure,
      onSubscriptionSuccess,
      setShowLoaderScreen,
      source,
      stats,
    ],
  );

  /**
   * Handle PhonePe subscription success
   * @param {Object} data - PhonePe success data
   */
  const handlePhonepeSuccess = useCallback(
    async (data = {}) => {
      try {
        // PhonePe success handling - no additional API call needed as polling handles verification
        logAmplitudeEvent('SUCCESS', 'PAYMENT', 'SUBSCRIPTION', {
          PLAN: planRef?.current?.name || 'NA',
          SOURCE: source,
          TRIAL:
            stats?.app?.latestAppSubscription?.recurringSubscription?.status ===
            'authenticated'
              ? 'YES'
              : 'NO',
          gateway: 'phonepe',
        });

        logFbEvent('SUCCESS_PAYMENT_SUBSCRIPTION');

        setShowLoaderScreen(false);
        onSubscriptionSuccess(planRef.current, subscriptionRef.current);
      } catch (error) {
        console.error(
          '[Subscription] Error processing PhonePe success:',
          error,
        );
        setShowLoaderScreen(false);
        onSubscriptionFailure();
      }
    },
    [
      setShowLoaderScreen,
      onSubscriptionSuccess,
      onSubscriptionFailure,
      source,
      stats,
    ],
  );

  const failureCallback = useCallback(() => {
    logAmplitudeEvent('FAILURE', 'PAYMENT', 'SUBSCRIPTION', {
      PLAN: planRef?.current?.name || 'NA',
      SOURCE: source,
      TRIAL:
        stats?.app?.latestAppSubscription?.recurringSubscription?.status ===
        'authenticated'
          ? 'YES'
          : 'NO',
    });
    setShowLoaderScreen(false);
    onSubscriptionFailure();
  }, [onSubscriptionFailure, setShowLoaderScreen, source, stats]);

  const startSubscription = useCallback(
    async (
      plan,
      options = {
        totalCount: 12,
        postId: null,
      },
    ) => {
      setShowLoaderScreen(true);
      const gateway = defaultGateway;

      const { totalCount, postId } = options;
      planRef.current = plan;

      try {
        const hasHaltedSubscription =
          stats?.[plan?.type]?.recurringSubscription?.status === 'halted';
        const oldSubscriptionId =
          stats?.[plan?.type]?.recurringSubId ||
          stats?.[plan?.type]?.recurringSubscription?.id ||
          null;

        let subscription;
        if (hasHaltedSubscription && oldSubscriptionId) {
          subscription = {
            id: oldSubscriptionId,
          };
        } else {
          const subscriptionUrl =
            '/api/monetization/subscription/init-subscription';

          const payload = {
            gateway,
            type: plan.type,
            planName: plan?.name ?? '',
            totalCount: totalCount || 12,
            ...(postId && { postId }),
          };
          if (gateway === 'phonepe') {
            payload.phonepePackageName = selectedPackage || 'com.phonepe.app';
          } else {
            gateway = 'razorpay'; // Fallback to Razorpay if PhonePe is not available or no upi apps are found
          }

          const { data } = await axios.post(subscriptionUrl, payload);
          subscription = data?.data;
          subscriptionRef.current = subscription;
        }
        if (gateway === 'phonepe') {
          // open razorpay portal
          logAmplitudeEvent('OPENING', 'PHONEPE', 'PORTAL', {
            PLAN: plan?.name,
          });
          initPhonepeSubscription({
            orderId: subscription.orderId,
            merchantOrderId: subscription.merchantOrderId,
            token: subscription.token,
            intentUrl: subscription.intentUrl,
            successCallback: handlePhonepeSuccess,
            failureCallback,
          });
        } else {
          // open razorpay portal
          logAmplitudeEvent('OPENING', 'RAZORPAY', 'PORTAL', {
            PLAN: plan?.name,
          });
          initPayment({
            subscriptionId: subscription.id,
            phone: userPhone,
            description: plan?.name ?? 'Animall Subscription',
            successCallback,
            failureCallback,
          });
        }
      } catch (error) {
        console.error('Error starting subscription:', error);
        failureCallback();
      }
    },
    [
      defaultGateway,
      isEligibleForSubscription,
      setShowLoaderScreen,
      stats,
      initPhonepeSubscription,
      initPayment,
      userPhone,
      successCallback,
      failureCallback,
      handlePhonepeSuccess,
      selectedPackage,
      closeAnimallLoader,
      openAndroidModal,
    ],
  );

  return {
    startSubscription,
    setSelectedPackage,

    isEligibleForSubscription,
    defaultGateway,
  };
};

export default useSubscription;
