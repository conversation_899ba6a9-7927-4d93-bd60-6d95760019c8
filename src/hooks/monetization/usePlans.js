import { useContext, useEffect, useMemo, useState } from 'react';

import { getCommaSeparatedNumber } from 'lib/utils';

import { PlanVariant, UiVariant } from 'modules/classifieds-plans/enums';
import {
  getBenefitTexts,
  getDraftedPostBlockedDate,
  getExtraDiscountForBlockedUser,
  getPlanTitle,
  getTagline,
} from 'modules/classifieds-plans/utils';
import { isPostComplete } from 'modules/home/<USER>';
import { useGrowthUser } from 'providers/GrowthUserProvider';
import { MonetizationContext } from 'providers/Monetization';

const getDefaultSelectedPlan = ({
  plans,
  uiVariant,
  visiblePlanNames = null,
  freeListingPlans = false,
}) => {
  if (freeListingPlans) {
    const primeBooster350Plan = plans?.find((plan) =>
      ['PRIME_BOOSTER_350', 'PRIME_BOOSTER_350_V2'].includes(plan?.name),
    );
    return primeBooster350Plan;
  }
  switch (uiVariant) {
    case UiVariant.STANDARD:
      if (visiblePlanNames?.length) {
        return plans.find((p) => p.name === visiblePlanNames[0]);
      }
      return plans[0];
    case UiVariant.FIRST_PAYMENT:
    case UiVariant.SECOND_PAYMENT:
      return plans.find(
        (p) => p.extraInfo?.maxUsage === 1 && !p.extraInfo?.benefits?.guarantee,
      );
    case UiVariant.ALL_PLANS:
      const limited3PostsPlan = plans.find((p) => p.extraInfo?.maxUsage === 3);
      if (limited3PostsPlan) {
        return limited3PostsPlan;
      }
      return plans[0];
    case UiVariant.UNLIMITED:
      return plans.find((p) => p.extraInfo?.planVariant === PlanVariant.SILVER);
    default:
      return plans[0];
  }
};

const addUiInfo = ({ plan, uiVariant, defaultSelectedId }) => {
  const effectivePrice = plan.discount ? plan.discount.price : plan.price;
  const discountPercent = plan.discount ? plan.discount.extraInfo.percent : 0;

  return {
    ...plan,
    uiInfo: {
      title: getPlanTitle(plan),
      tagLine: getTagline({ plan, uiVariant }),
      benefitTexts: getBenefitTexts(plan),
      discountPercent,
      priceText: plan.price
        ? `₹${getCommaSeparatedNumber(effectivePrice)}`
        : 'फ्री',
      defaultSelected: plan.id === defaultSelectedId,
    },
  };
};

const applyExtraDiscount = (
  plans,
  extraDiscountConfig,
  excludeSubscriptions = true,
) => {
  return plans.map((plan) => {
    if (
      plan?.discount?.price === 0 ||
      plan?.price === 0 ||
      (excludeSubscriptions && !!plan?.extraInfo?.isSubscription)
    ) {
      return plan;
    }
    const updatedPlan = { ...plan, extraDiscountConfig };
    if (updatedPlan?.discount?.price) {
      updatedPlan.discount = {
        ...updatedPlan.discount,
        price: Math.max(
          Math.ceil(
            updatedPlan?.discount?.price -
              updatedPlan?.discount?.price *
                extraDiscountConfig?.discountPercentage *
                0.01,
          ),
          0,
        ),
      };
    } else {
      const originalPrice = plan.price;
      updatedPlan.originalPrice = originalPrice;
      updatedPlan.price = Math.max(
        Math.ceil(
          originalPrice -
            originalPrice * extraDiscountConfig?.discountPercentage * 0.01,
        ),
        0,
      );
    }

    return updatedPlan;
  });
};

const usePlans = ({ blockedDraftedListing, freeListingPlans }) => {
  const { isGrowthUser } = useGrowthUser();
  const { listing } = useContext(MonetizationContext);
  let { plans, uiVariant, visiblePlanNames } = listing;
  const [primaryPlans, setPrimaryPlans] = useState([]);
  const [otherPlans, setOtherPlans] = useState([]);
  const [extraDiscountConfig, setExtraDiscountConfig] = useState(null);
  const defaultSelected = useMemo(() => {
    if (!plans?.length) return null;
    return getDefaultSelectedPlan({
      plans,
      uiVariant,
      visiblePlanNames,
      freeListingPlans,
    });
  }, [freeListingPlans, plans, uiVariant, visiblePlanNames]);

  useEffect(() => {
    if (!plans?.length || !!extraDiscountConfig) return;

    let updatedPlans = plans;

    if (
      !isGrowthUser &&
      listing?.isLimitReached &&
      !!blockedDraftedListing &&
      blockedDraftedListing?.paymentRequired &&
      isPostComplete(blockedDraftedListing)
    ) {
      const blockedDate = getDraftedPostBlockedDate(blockedDraftedListing);
      const discountConfigObj = getExtraDiscountForBlockedUser(blockedDate);
      if (discountConfigObj) {
        updatedPlans = applyExtraDiscount(plans, discountConfigObj);
        setExtraDiscountConfig(discountConfigObj);
      }
    }

    let _primaryPlans = [];
    let _otherPlans = [];

    switch (uiVariant) {
      case UiVariant.STANDARD:
        _primaryPlans = updatedPlans
          .filter((plan) => (visiblePlanNames || []).includes(plan.name))
          .map((plan) =>
            addUiInfo({
              plan,
              uiVariant,
              defaultSelectedId: defaultSelected?.id,
            }),
          );

        _otherPlans = updatedPlans
          .filter((plan) => !visiblePlanNames?.includes(plan.name))
          .map((plan) =>
            addUiInfo({
              plan,
              uiVariant,
              defaultSelectedId: defaultSelected?.id,
            }),
          );
        break;
      case UiVariant.UNLIMITED:
        _primaryPlans = updatedPlans
          .filter((plan) =>
            [
              PlanVariant.SILVER,
              PlanVariant.VIP,
              PlanVariant.VIP_USER,
            ].includes(plan.extraInfo?.planVariant),
          )
          .map((plan) =>
            addUiInfo({
              plan,
              uiVariant,
              defaultSelectedId: defaultSelected?.id,
            }),
          )
          .sort((a, b) => a.price - b.price);
        _otherPlans = updatedPlans
          .filter(
            (plan) =>
              ![
                PlanVariant.SILVER,
                PlanVariant.VIP,
                PlanVariant.VIP_USER,
              ].includes(plan.extraInfo?.planVariant),
          )
          .map((plan) =>
            addUiInfo({
              plan,
              uiVariant,
              defaultSelectedId: defaultSelected?.id,
            }),
          )
          .sort((a, b) => a.price - b.price);
        break;
      case UiVariant.ALL_PLANS:
        _primaryPlans = updatedPlans.map((plan) =>
          addUiInfo({
            plan,
            uiVariant,
            defaultSelectedId: defaultSelected?.id,
          }),
        );
        break;
      default:
        _primaryPlans = updatedPlans.slice(0, 2).map((plan) =>
          addUiInfo({
            plan,
            uiVariant,
            defaultSelectedId: defaultSelected?.id,
          }),
        );
        _otherPlans = updatedPlans.slice(2).map((plan) =>
          addUiInfo({
            plan,
            uiVariant,
            defaultSelectedId: defaultSelected?.id,
          }),
        );
        break;
    }
    setPrimaryPlans(_primaryPlans);
    setOtherPlans(_otherPlans);
  }, [
    plans,
    uiVariant,
    defaultSelected,
    blockedDraftedListing,
    extraDiscountConfig,
    isGrowthUser,
    listing?.isLimitReached,
    visiblePlanNames,
  ]);

  return {
    plans: plans || [],
    uiVariant,
    primaryPlans,
    otherPlans,
    defaultSelectedId: defaultSelected?.id,
    extraDiscountConfig,
    setExtraDiscountConfig,
  };
};

export default usePlans;
